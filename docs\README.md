# Agentic Audio SDK Documentation

Welcome to the comprehensive documentation for the **Agentic Audio SDK** - a powerful, enterprise-grade toolkit for building medical consultation applications with real-time speech processing capabilities.

## 🎯 Overview

The Agentic Audio SDK is designed specifically for healthcare applications, providing seamless integration of speech-to-text, text-to-speech, audio capture, and session management capabilities. Built with medical workflows in mind, it offers enterprise-grade reliability, security, and performance.

### Key Features

- **🏥 Medical-First Design** - Optimized for healthcare consultation workflows
- **🔊 Real-time Speech Processing** - Live speech-to-text with medical terminology support
- **🗣️ Multi-language TTS** - Text-to-speech in English, Hindi, Tamil, and Malayalam
- **🎤 Professional Audio Capture** - High-quality audio recording with noise cancellation
- **📝 Session Management** - Complete consultation lifecycle management
- **🔒 Enterprise Security** - HIPAA-ready data handling and encryption
- **⚡ Real-time Processing** - WebSocket-based real-time communication
- **🎛️ React Integration** - Purpose-built React hooks for easy integration

## 📚 Documentation Structure

### 🚀 Getting Started

- **[Installation & Setup](./installation.md)** - Complete setup guide from installation to first app
- **[Basic Usage](./basic-usage.md)** - Build your first consultation app step-by-step
- **[Configuration](./configuration.md)** - SDK configuration options and environment setup

### 🎣 React Hooks Reference

- **[useArcaSessionManager](./hooks/use-arca-session-manager.md)** - Session lifecycle management for medical consultations
- **[useArcaSpeechToText](./hooks/use-arca-speech-to-text.md)** - Real-time speech-to-text with medical terminology
- **[useAudioCapture](./hooks/use-audio-capture.md)** - Professional microphone audio capture
- **[useArcaTextToSpeech](./hooks/use-arca-text-to-speech.md)** - Multi-language text-to-speech synthesis

### 📖 Core Concepts

- **[Session Management](./concepts/session-management.md)** - Understanding medical consultation sessions
- **[Audio Processing](./concepts/audio-processing.md)** - Audio capture, streaming, and optimization
- **[Error Handling](./concepts/error-handling.md)** - Comprehensive error management strategies
- **[Security & Privacy](./concepts/security.md)** - Data protection and healthcare compliance

### 🔍 API Reference

- **[Types & Interfaces](./api/types.md)** - Complete TypeScript definitions
- **[Configuration Options](./api/configuration.md)** - Detailed configuration reference
- **[Events & Callbacks](./api/events.md)** - Event handling and callback patterns
- **[Error Codes](./api/errors.md)** - Error codes and troubleshooting guide

### 💡 Examples & Tutorials

- **[Complete Examples](./examples/README.md)** - Working code examples and demos

### 🚀 Advanced Topics

- **[Performance Optimization](./advanced/performance.md)** - Production optimization strategies
- **[Custom Providers](./advanced/custom-providers.md)** - Implementing custom service providers
- **[Deployment Guide](./advanced/deployment.md)** - Production deployment best practices

## 🏃‍♂️ Quick Start

### Installation

```bash
npm install @arcaai/agentic-sdk
```

### Configuration Setup

Create a centralized configuration file:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
    apiEndpoint: 'https://api.arcaai.com',
    websocketUrl: 'wss://api.arcaai.com',
    credentials: {
        apiKey: 'your-api-key-here',
    },
    // Optional: Audio settings
    audioSettings: {
        sampleRate: 16000,
        channels: 1,
        noiseCancellation: true,
        echoCancellation: true,
        autoGainControl: true,
    },
    // Optional: Logging
    logging: {
        level: 'info',
        console: true,
    },
    environment: 'production',
};
```

### Basic Usage

```typescript
import React, { useState } from "react";
import {
  useArcaSessionManager,
  useArcaSpeechToText,
  useAudioCapture,
  useArcaTextToSpeech,
} from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "./config/sdk-config";

function MedicalConsultation() {
  const [transcript, setTranscript] = useState("");

  // Session management
  const sessionManager = useArcaSessionManager({
    doctorId: "dr-123",
    doctorName: "Dr. Smith",
    patientId: "pt-456",
    patientName: "John Doe",
    options: SDK_CONFIG_OPTIONS,
  });

  // Speech-to-text
  const speechToText = useArcaSpeechToText({
    sessionId: sessionManager.session?.id || "",
    language: "en-US",
    options: SDK_CONFIG_OPTIONS,
    onTranscript: (text, isFinal) => {
      if (isFinal) {
        setTranscript((prev) => prev + " " + text);
      }
    },
  });

  // Audio capture
  const audioCapture = useAudioCapture({
    options: SDK_CONFIG_OPTIONS.audioSettings,
    onAudioData: (data) => {
      if (sessionManager.session?.status === "ACTIVE") {
        speechToText.sendAudioData(data);
      }
    },
  });

  // Text-to-speech
  const [
    sendTextData,
    ttsLoading,
    ttsError,
    ttsConnect,
    ttsDisconnect,
    ttsIsConnected,
  ] = useArcaTextToSpeech({
    apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
    language: "en",
    options: SDK_CONFIG_OPTIONS,
  });

  const startConsultation = async () => {
    await sessionManager.createSession();
    await sessionManager.startSession();
    await speechToText.startTranscription();
    await audioCapture.startRecording();
    ttsConnect();
  };

  return (
    <div>
      <h1>Medical Consultation</h1>
      <button onClick={startConsultation}>Start Consultation</button>
      <div>
        <h3>Live Transcript:</h3>
        <p>{transcript}</p>
      </div>
    </div>
  );
}
```

## 🌟 Key Capabilities

### Medical Consultation Focus

- **Session-based Architecture** - Built around medical consultation sessions
- **Provider-Patient Workflow** - Clear separation of healthcare provider and patient roles
- **Medical Terminology Support** - Optimized recognition of medical terms across languages
- **Consultation Documentation** - Automatic transcript and session data management

### Multi-language Support

- **English** (`en-US`, `en-GB`) - Complete medical terminology support
- **Hindi** (`hi-IN`) - Medical terms and conversational Hindi
- **Tamil** (`ta-IN`) - Medical terms and native Tamil support
- **Malayalam** (`ml-IN`) - Medical terms and native Malayalam support

### Real-time Processing

- **WebSocket Architecture** - Low-latency real-time communication
- **Streaming Audio** - Continuous audio streaming with chunked processing
- **Live Transcription** - Real-time speech-to-text with interim and final results
- **Instant Synthesis** - Fast text-to-speech generation and playback

### Enterprise Features

- **Session Recovery** - Automatic session recovery on connection loss
- **Error Handling** - Comprehensive error management and reporting
- **Performance Monitoring** - Built-in performance metrics and optimization
- **Security** - Enterprise-grade security with encryption and access controls
- **Centralized Configuration** - Single source of truth for SDK settings

## 🛠️ Architecture

The SDK is built with a modular architecture:

```
@arcaai/agentic-sdk
├── React Hooks Layer          # React integration hooks
│   ├── useArcaSessionManager  # Session lifecycle management
│   ├── useArcaSpeechToText    # Speech-to-text integration
│   ├── useAudioCapture        # Audio capture management
│   └── useArcaTextToSpeech    # Text-to-speech integration
├── Core Services Layer        # Core business logic
│   ├── SessionManager         # Session management core
│   ├── AudioCaptureManager    # Audio capture core
│   ├── WebSocketManager       # Real-time communication
│   └── CloudSTTService        # Speech processing core
├── Services Layer             # External integrations
│   ├── API Client             # HTTP API communication
│   └── Session API            # Session management API
├── Configuration Layer        # Centralized configuration
│   └── SDK_CONFIG_OPTIONS     # Single source of truth
└── Types & Utilities          # TypeScript definitions and utilities
```

## ⚙️ Configuration Management

The SDK uses a centralized configuration approach for better maintainability and consistency:

### Environment-Specific Configuration

```typescript
// src/config/sdk-config.ts
const baseConfig = {
    credentials: {
        apiKey: process.env.REACT_APP_API_KEY || 'your-default-api-key',
    },
    audioSettings: {
        sampleRate: 16000,
        channels: 1,
        noiseCancellation: true,
        echoCancellation: true,
        autoGainControl: true,
    },
};

const developmentConfig = {
    ...baseConfig,
    apiEndpoint: 'http://localhost:3333',
    websocketUrl: 'ws://localhost:3333',
    logging: { level: 'debug', console: true },
    environment: 'development',
};

const productionConfig = {
    ...baseConfig,
    apiEndpoint: 'https://api.arcaai.com',
    websocketUrl: 'wss://api.arcaai.com',
    logging: { level: 'info', console: false },
    environment: 'production',
};

export const SDK_CONFIG_OPTIONS =
    process.env.NODE_ENV === 'development'
        ? developmentConfig
        : productionConfig;
```

### Benefits of Centralized Configuration

1. **Single Source of Truth** - All SDK settings in one place
2. **Environment Management** - Easy switching between dev/staging/prod
3. **Type Safety** - Full TypeScript support for configuration
4. **Consistency** - Same configuration used across all hooks
5. **Maintainability** - Easy to update and manage settings

## 🔧 Prerequisites

- **Node.js** 16+ (recommended: 18+)
- **React** 18+
- **TypeScript** 5+ (for TypeScript projects)
- **Modern Browser** with WebSocket and WebRTC support

## 📋 Supported Browsers

| Browser | Minimum Version | Notes                            |
| ------- | --------------- | -------------------------------- |
| Chrome  | 90+             | Recommended for best performance |
| Firefox | 88+             | Full support                     |
| Safari  | 14+             | iOS Safari 14+                   |
| Edge    | 90+             | Chromium-based Edge              |

## 🏗️ Project Structure

After setup, your project should follow this recommended structure:

```
src/
├── config/
│   └── sdk-config.ts          # Centralized SDK configuration
├── components/
│   ├── MedicalConsole/        # Main consultation interface
│   ├── AudioControls/         # Audio recording controls
│   ├── TranscriptDisplay/     # Real-time transcript display
│   └── SessionManager/        # Session management UI
├── hooks/                     # Custom React hooks
├── services/                  # API and external services
├── utils/                     # Shared utilities
├── types/                     # TypeScript definitions
└── App.tsx                    # Main application component
```

## 🤝 Support & Community

- **📧 Email Support** - [<EMAIL>](mailto:<EMAIL>)
- **📖 API Documentation** - [api.arcaai.com](https://api.arcaai.com)
- **🐛 Issue Tracking** - [GitHub Issues](https://github.com/ArcaAI/Arca-AgenticSDK/issues)
- **💬 Discord Community** - [Join our Discord](https://discord.gg/arcaai)

## 📄 License

MIT License - see [LICENSE](../LICENSE) for details.

---

## 🚀 Next Steps

1. **[Install the SDK](./installation.md)** - Set up your development environment with centralized configuration
2. **[Follow the Basic Usage Guide](./basic-usage.md)** - Build your first consultation app using `SDK_CONFIG_OPTIONS`
3. **[Explore the Hook Documentation](./hooks/use-arca-session-manager.md)** - Deep dive into specific features
4. **[Check Out Examples](./examples/README.md)** - See working code examples with the new configuration approach

Ready to build amazing healthcare applications? Let's get started! 🏥✨
