# Build outputs
dist/
build/
lib/
*.tsbuildinfo
*.d.ts.map

# Dependencies
node_modules/
.pnp/
.pnp.js
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Package managers
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.npm
.yarn-integrity

# Testing
coverage/
.nyc_output/
.vitest-cache/
test-results/
junit.xml

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env*.local

# IDE and editors
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Turbo
.turbo/

# Documentation build (if auto-generated)
docs/api/
docs/build/

# SDK specific files
# Audio cache and temporary files
*.wav
*.mp3
*.ogg
*.m4a
audio-cache/
temp-audio/

# Session data and recovery files
session-*.json
recovery-*.json
.session-storage/

# WebSocket connection logs
ws-*.log
connection-*.log

# Development files
.npmrc
!.npmrc.sample

# Temporary files
*.tmp
*.temp
.tmp/

# Lock files (optional - uncomment if you want to ignore them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# TypeScript incremental compilation
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.build

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Backup files
*.bak
*.backup

# Generated API documentation
api-docs/
typedoc-out/