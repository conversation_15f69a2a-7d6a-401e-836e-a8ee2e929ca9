# useAudioCapture

The `useAudioCapture` hook provides professional-grade audio capture capabilities from the user's microphone with real-time processing, noise reduction, and device management.

## Overview

This hook manages microphone access, audio stream processing, and real-time audio data delivery. It includes advanced features like noise cancellation, echo cancellation, and automatic gain control for optimal audio quality in medical consultations.

## Import

```typescript
import { useAudioCapture } from "@arcaai/agentic-sdk";
```

## Configuration Setup

For optimal audio settings, you can reference your centralized SDK configuration:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
    apiEndpoint: 'https://api.arcaai.com',
    websocketUrl: 'wss://api.arcaai.com',
    credentials: {
        apiKey: 'your-api-key-here',
    },
    // Audio processing settings
    audioSettings: {
        sampleRate: 16000,
        channels: 1,
        bitDepth: 16,
        format: 'pcm',
        chunkSize: 1024,
        noiseCancellation: true,
        echoCancellation: true,
        autoGainControl: true,
    },
};
```

## Basic Usage

```typescript
import React, { useState } from "react";
import { useAudioCapture } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

function AudioCaptureDemo() {
  const [audioLevel, setAudioLevel] = useState(0);

  const {
    isRecording,
    isReady,
    deviceStatus,
    error,
    startRecording,
    stopRecording,
    getDeviceStatus,
  } = useAudioCapture({
    onAudioData: (audioData) => {
      console.log("Received audio chunk:", audioData.byteLength, "bytes");
      // Process audio data (e.g., send to STT service)
    },
    onError: (error) => {
      console.error("Audio capture error:", error);
    },
    options: SDK_CONFIG_OPTIONS.audioSettings,
  });

  const handleStartRecording = async () => {
    try {
      await startRecording();
      console.log("Recording started");
    } catch (error) {
      console.error("Failed to start recording:", error);
    }
  };

  const handleStopRecording = async () => {
    try {
      await stopRecording();
      console.log("Recording stopped");
    } catch (error) {
      console.error("Failed to stop recording:", error);
    }
  };

  const handleCheckDevices = async () => {
    const status = await getDeviceStatus();
    console.log("Device status:", status);
  };

  return (
    <div>
      <h2>Audio Capture Demo</h2>

      <div>
        <p>Ready: {isReady ? "✅" : "❌"}</p>
        <p>Recording: {isRecording ? "🔴" : "⚫"}</p>
        <p>Permission: {deviceStatus?.permissionStatus || "Unknown"}</p>
        <p>Devices: {deviceStatus?.inputDevices.length || 0} available</p>
      </div>

      <div>
        <button
          onClick={handleStartRecording}
          disabled={!isReady || isRecording}
        >
          Start Recording
        </button>
        <button onClick={handleStopRecording} disabled={!isRecording}>
          Stop Recording
        </button>
        <button onClick={handleCheckDevices}>Check Devices</button>
      </div>

      {error && <div style={{ color: "red" }}>Error: {error.message}</div>}
    </div>
  );
}
```

## API Reference

### Parameters

The hook accepts a configuration object with the following properties:

#### `UseAudioCaptureProps`

| Name        | Type                          | Required | Default | Description                                                |
| ----------- | ----------------------------- | -------- | ------- | ---------------------------------------------------------- |
| options     | `Partial<AudioConfig>`         | No       | —       | Optional audio configuration (sample rate, channels, etc.)  |
| autoStart   | `boolean`                     | No       | false   | If true, recording starts automatically on mount           |
| onAudioData | `(data: ArrayBuffer) => void` | No       | —       | Callback for receiving audio data chunks                   |
| onError     | `(error: ErrorInfo) => void`  | No       | —       | Optional error callback for custom error handling          |

The hook returns an object with the following properties:

#### `UseAudioCaptureReturn`

| Property          | Type                                       | Description                                        |
| ----------------- | ------------------------------------------ | -------------------------------------------------- |
| `isRecording`     | `boolean`                                  | Whether audio is currently being recorded          |
| `isReady`         | `boolean`                                  | Whether the audio capture is initialized and ready |
| `deviceStatus`    | `AudioDeviceStatus \| null`                | Current audio device status                        |
| `error`           | `ErrorInfo \| null`                        | Current error state                                |
| `startRecording`  | `() => Promise<void>`                      | Starts audio recording                             |
| `stopRecording`   | `() => Promise<void>`                      | Stops audio recording                              |
| `getDeviceStatus` | `() => Promise<AudioDeviceStatus \| null>` | Gets current device status                         |

## Audio Configuration

### AudioConfig Interface

```typescript
interface AudioConfig {
  sampleRate: number; // Sample rate in Hz (8000, 16000, 44100, 48000)
  channels: 1 | 2; // Number of audio channels (1=mono, 2=stereo)
  bitDepth: 8 | 16 | 24 | 32; // Bit depth for audio samples
  format: "pcm" | "wav" | "mp3"; // Audio format
  chunkSize: number; // Size of audio chunks in samples
  noiseCancellation: boolean; // Enable noise cancellation
  echoCancellation: boolean; // Enable echo cancellation
  autoGainControl: boolean; // Enable automatic gain control
}
```

### Default Configuration

```typescript
const DEFAULT_AUDIO_CONFIG: AudioConfig = {
  sampleRate: 16000, // Optimal for speech recognition
  channels: 1, // Mono for medical consultations
  bitDepth: 16, // Standard quality
  format: "pcm", // Raw audio format
  chunkSize: 1024, // 1KB chunks for real-time processing
  noiseCancellation: true, // Reduce background noise
  echoCancellation: true, // Reduce echo feedback
  autoGainControl: true, // Normalize volume levels
};
```

## Advanced Usage

### Custom Audio Configuration

```typescript
import { useAudioCapture } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const audioCapture = useAudioCapture({
  options: {
    // Use base settings from configuration
    ...SDK_CONFIG_OPTIONS.audioSettings,
    // Override specific settings for high quality
    sampleRate: 48000,
    channels: 2,
    bitDepth: 24,
    chunkSize: 2048,
    autoGainControl: false, // Manual gain control
  },
  onAudioData: (data) => {
    // Process high-quality audio data
    console.log("High-quality audio chunk:", data.byteLength);
  },
});
```

### Audio Level Monitoring

```typescript
import { useAudioCapture } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const [audioLevel, setAudioLevel] = useState(0);
const [audioLevels, setAudioLevels] = useState<number[]>([]);

const audioCapture = useAudioCapture({
  options: SDK_CONFIG_OPTIONS.audioSettings,
  onAudioData: (data) => {
    // Calculate audio level from raw data
    const samples = new Int16Array(data);
    let sum = 0;
    for (let i = 0; i < samples.length; i++) {
      sum += Math.abs(samples[i]);
    }
    const level = (sum / samples.length / 32768) * 100; // Convert to percentage

    setAudioLevel(level);
    setAudioLevels((prev) => [...prev.slice(-59), level]); // Keep last 60 samples
  },
});

// Audio level visualization
const AudioLevelMeter = () => (
  <div style={{ width: "200px", height: "20px", border: "1px solid #ccc" }}>
    <div
      style={{
        width: `${audioLevel}%`,
        height: "100%",
        backgroundColor:
          audioLevel > 70 ? "red" : audioLevel > 30 ? "yellow" : "green",
        transition: "width 0.1s",
      }}
    />
  </div>
);
```

### Device Selection

```typescript
import { useAudioCapture } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const [selectedDevice, setSelectedDevice] = useState<string>("");
const [availableDevices, setAvailableDevices] = useState<MediaDeviceInfo[]>([]);

const audioCapture = useAudioCapture({
  options: SDK_CONFIG_OPTIONS.audioSettings,
  onAudioData: (data) => {
    // Process audio from selected device
  },
  onError: (error) => {
    console.error("Audio error:", error);
  },
});

useEffect(() => {
  const loadDevices = async () => {
    const status = await audioCapture.getDeviceStatus();
    if (status) {
      setAvailableDevices(status.inputDevices);
    }
  };

  loadDevices();
}, []);

const DeviceSelector = () => (
  <select
    value={selectedDevice}
    onChange={(e) => setSelectedDevice(e.target.value)}
  >
    <option value="">Default Device</option>
    {availableDevices.map((device) => (
      <option key={device.deviceId} value={device.deviceId}>
        {device.label || `Device ${device.deviceId.substring(0, 8)}`}
      </option>
    ))}
  </select>
);
```

### Integration with STT

```typescript
import { useArcaSpeechToText, useAudioCapture } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

function AudioToTextCapture() {
  const speechToText = useArcaSpeechToText({
    sessionId: "session-123",
    language: "en-US",
    options: SDK_CONFIG_OPTIONS,
    onTranscript: (text, isFinal) => {
      console.log(`${isFinal ? "Final" : "Interim"}: ${text}`);
    },
  });

  const audioCapture = useAudioCapture({
    onAudioData: (data) => {
      // Send audio data directly to STT service
      speechToText.sendAudioData(data);
    },
    onError: (error) => {
      console.error("Audio capture error:", error);
    },
    options: {
      // Use optimized settings for STT from configuration
      ...SDK_CONFIG_OPTIONS.audioSettings,
      format: "pcm", // Raw format for STT
    },
  });

  const handleStart = async () => {
    try {
      await speechToText.startTranscription();
      await audioCapture.startRecording();
    } catch (error) {
      console.error("Failed to start audio-to-text:", error);
    }
  };

  const handleStop = async () => {
    try {
      await audioCapture.stopRecording();
      await speechToText.stopTranscription();
    } catch (error) {
      console.error("Failed to stop audio-to-text:", error);
    }
  };

  return (
    <div>
      <button onClick={handleStart} disabled={!audioCapture.isReady}>
        Start Recording & Transcription
      </button>
      <button onClick={handleStop} disabled={!audioCapture.isRecording}>
        Stop Recording & Transcription
      </button>
    </div>
  );
}
```

## Device Status

### AudioDeviceStatus Interface

```typescript
interface AudioDeviceStatus {
  inputDevices: MediaDeviceInfo[]; // Available microphones
  selectedDevice?: MediaDeviceInfo; // Currently selected device
  permissionStatus: "granted" | "denied" | "prompt"; // Permission state
  audioLevel: number; // Current audio input level (0-100)
}
```

### Permission Handling

```typescript
import { useAudioCapture } from "@arcaai/agentic-sdk";

const audioCapture = useAudioCapture({
  onError: (error) => {
    if (error.code === "PERMISSION_DENIED") {
      // Guide user to grant permissions
      showPermissionDialog();
    }
  },
});

const checkPermissions = async () => {
  const status = await audioCapture.getDeviceStatus();

  switch (status?.permissionStatus) {
    case "granted":
      console.log("✅ Microphone access granted");
      break;
    case "denied":
      console.log("❌ Microphone access denied");
      showPermissionHelp();
      break;
    case "prompt":
      console.log("⏳ Microphone permission required");
      // Permission will be requested on first startRecording()
      break;
  }
};
```

## Audio Processing Events

The hook emits various events during audio processing:

```typescript
import { useAudioCapture } from "@arcaai/agentic-sdk";

const audioCapture = useAudioCapture({
  onAudioData: (data) => {
    // Raw audio data received
  },
  onVoiceStart: () => {
    // Voice activity detected
    console.log("Voice started");
  },
  onVoiceEnd: () => {
    // Voice activity ended
    console.log("Voice ended");
  },
  onSilence: (duration) => {
    // Silence detected
    console.log(`Silence detected: ${duration}ms`);
  },
  onVolumeChange: (level) => {
    // Audio level changed
    setAudioLevel(level);
  },
});
```

## Error Handling

### Common Error Codes

| Error Code             | Description                      | Action                          |
| ---------------------- | -------------------------------- | ------------------------------- |
| `PERMISSION_DENIED`    | Microphone access denied         | Guide user to grant permissions |
| `NO_DEVICES_FOUND`     | No audio input devices           | Check hardware connections      |
| `DEVICE_BUSY`          | Microphone in use by another app | Close other applications        |
| `INITIALIZATION_ERROR` | Failed to initialize audio       | Check browser compatibility     |
| `AUDIO_CONTEXT_ERROR`  | Web Audio API error              | Refresh page or restart browser |
| `STREAM_ERROR`         | Audio stream error               | Check device and try again      |

### Error Recovery

```typescript
import { useAudioCapture } from "@arcaai/agentic-sdk";

const [retryCount, setRetryCount] = useState(0);

const audioCapture = useAudioCapture({
  onError: (error) => {
    console.error("Audio error:", error);

    switch (error.code) {
      case "PERMISSION_DENIED":
        showPermissionDialog();
        break;

      case "DEVICE_BUSY":
        if (retryCount < 3) {
          setTimeout(() => {
            audioCapture.startRecording();
            setRetryCount((prev) => prev + 1);
          }, 2000);
        }
        break;

      case "INITIALIZATION_ERROR":
        // Reload the page as last resort
        if (retryCount > 2) {
          window.location.reload();
        }
        break;
    }
  },
});
```

## Performance Optimization

### Efficient Audio Processing

```typescript
import { useAudioCapture } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

// Use optimal settings for real-time processing
const audioCapture = useAudioCapture({
  options: {
    // Start with configuration defaults
    ...SDK_CONFIG_OPTIONS.audioSettings,
    // Optimize for real-time processing
    chunkSize: 512, // Smaller chunks for lower latency
    format: "pcm", // Raw format avoids encoding overhead
  },
  onAudioData: (data) => {
    // Process audio efficiently
    processAudioChunk(data);
  },
});

// Efficient audio processing function
const processAudioChunk = (data: ArrayBuffer) => {
  // Use Worker for heavy processing to avoid blocking UI
  if (audioWorker) {
    audioWorker.postMessage({ type: "PROCESS_AUDIO", data });
  }
};
```

### Memory Management

```typescript
import { useAudioCapture } from "@arcaai/agentic-sdk";

const audioCapture = useAudioCapture({
  onAudioData: (data) => {
    // Process and immediately release audio data
    processAudio(data);

    // Don't store large audio buffers in state
    // Instead, process and send to backend immediately
  },
});

// Cleanup on component unmount
useEffect(() => {
  return () => {
    audioCapture.stopRecording();
  };
}, []);
```

## Best Practices

### 1. Audio Quality Settings

```typescript
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

// For medical consultations - prioritize clarity
const medicalAudioConfig = {
  ...SDK_CONFIG_OPTIONS.audioSettings,
  sampleRate: 16000, // Sufficient for speech
  channels: 1, // Mono for consistency
  bitDepth: 16, // Good quality without excessive data
  noiseCancellation: true, // Essential for clinical environments
  echoCancellation: true, // Prevent feedback
  autoGainControl: true, // Normalize doctor/patient voices
};

// For high-quality recording - prioritize fidelity
const highQualityConfig = {
  sampleRate: 48000, // Professional quality
  channels: 2, // Stereo for spatial audio
  bitDepth: 24, // High dynamic range
  noiseCancellation: false, // Preserve natural audio
  echoCancellation: false, // Keep spatial characteristics
  autoGainControl: false, // Manual control
};
```

### 2. User Experience

```typescript
import { useAudioCapture } from "@arcaai/agentic-sdk";

const AudioCaptureWithFeedback = () => {
  const [isListening, setIsListening] = useState(false);

  const audioCapture = useAudioCapture({
    onAudioData: (data) => {
      setIsListening(true);
      // Reset listening indicator after short delay
      setTimeout(() => setIsListening(false), 100);
    },
  });

  return (
    <div>
      <button
        onClick={audioCapture.startRecording}
        disabled={!audioCapture.isReady}
        style={{
          backgroundColor: audioCapture.isRecording ? "#ff4444" : "#44ff44",
          border: isListening ? "3px solid #ffff44" : "1px solid #ccc",
        }}
      >
        {audioCapture.isRecording ? "🔴 Recording" : "🎤 Start Recording"}
      </button>

      {!audioCapture.isReady && <p>Initializing microphone...</p>}

      {audioCapture.error && (
        <div style={{ color: "red" }}>
          {getErrorMessage(audioCapture.error)}
        </div>
      )}
    </div>
  );
};
```

### 3. Progressive Enhancement

```typescript
// Check browser support before using advanced features
const getBrowserCapabilities = () => {
  const capabilities = {
    getUserMedia: !!navigator.mediaDevices?.getUserMedia,
    audioContext: !!(window.AudioContext || window.webkitAudioContext),
    audioWorklet: !!window.AudioWorkletNode,
    noiseSuppression: true, // Check specific constraints support
  };

  return capabilities;
};

const audioCapture = useAudioCapture({
  options: {
    // Adjust features based on browser capabilities
    ...(getBrowserCapabilities().noiseSuppression
      ? { noiseCancellation: true }
      : { noiseCancellation: false }),
  },
});
```

## Troubleshooting

### Common Issues

1. **No Audio Input**

   - Check microphone permissions
   - Verify device is not muted
   - Test with other applications

2. **Poor Audio Quality**

   - Adjust noise cancellation settings
   - Check microphone positioning
   - Verify sample rate settings

3. **High Latency**

   - Reduce chunk size
   - Use lower sample rate
   - Check system audio settings

4. **Permission Errors**
   - Ensure HTTPS connection
   - Check browser security settings
   - Guide user through permission flow

### Debug Mode

```typescript
import { useAudioCapture } from "@arcaai/agentic-sdk";

const audioCapture = useAudioCapture({
  options: {
    // Enable debug logging
    debug: true,
  },
  onAudioData: (data) => {
    console.log("Audio chunk received:", {
      size: data.byteLength,
      timestamp: Date.now(),
    });
  },
});
```

## Browser Compatibility

| Browser     | Basic Support | Advanced Features | Notes                      |
| ----------- | ------------- | ----------------- | -------------------------- |
| Chrome 66+  | ✅            | ✅                | Full support               |
| Firefox 60+ | ✅            | ✅                | Full support               |
| Safari 11+  | ✅            | ⚠️                | Limited noise cancellation |
| Edge 79+    | ✅            | ✅                | Full support               |

## Examples

- **[Complete Examples](./examples/README.md)** - Working code examples and demos