import { EventEmitter } from 'eventemitter3';
import { ApiClient } from '../services/apiClient';
import { SessionApi } from '../services/sessionApi';
import type {
    MedicalSession,
    OfflineAction,
    PatientInfo,
    ProviderInfo,
    RecoveryOptions,
    SessionLifecycleEvents,
    SessionManagerConfig,
    SessionMetadata,
    SessionPerformanceMetrics,
    SessionRecoveryInfo,
} from '../types/index';
import { DeviceInfoCollector } from '../utils/DeviceInfoCollector';
import { Logger } from './Logger';
import { SessionRecoveryManager } from './SessionRecoveryManager';
import { SessionStorageManager } from './SessionStorageManager';

export class SessionManager extends EventEmitter<SessionLifecycleEvents> {
    private currentSession: MedicalSession | null = null;
    private storageManager: SessionStorageManager;
    private recoveryManager: SessionRecoveryManager;
    private deviceInfoCollector: DeviceInfoCollector;
    private logger: Logger;
    private sessionApi: SessionApi | null = null;
    private config: SessionManagerConfig | null = null;
    private performanceMetrics: Map<string, SessionPerformanceMetrics> = new Map();
    private autoBackupInterval: NodeJS.Timeout | null = null;
    private beforeUnloadHandler: ((event: BeforeUnloadEvent) => void) | null = null;
    private pageLoadHandler: (() => void) | null = null;
    private offlineActionQueue: OfflineAction[] = [];
    private isOnline: boolean = true;
    private syncInProgress: boolean = false;
    private reconnectAttempts: number = 0;
    private maxReconnectAttempts: number = 5;
    private reconnectTimeout: NodeJS.Timeout | null = null;

    constructor() {
        super();
        this.storageManager = new SessionStorageManager();
        this.recoveryManager = new SessionRecoveryManager();
        this.deviceInfoCollector = new DeviceInfoCollector();
        this.logger = new Logger();

        this.isOnline = (typeof navigator !== 'undefined' && typeof navigator.onLine === 'boolean') ? navigator.onLine : true;

        this.setupEventHandlers();
        this.setupNetworkMonitoring();
    }

    public async initialize(config: SessionManagerConfig): Promise<void> {
        this.config = config;

        try {
            await this.storageManager.initialize(config);
            await this.recoveryManager.initialize(config);

            // Initialize SessionApi if API endpoint and credentials are provided
            if (config.apiEndpoint && config.credentials?.apiKey) {
                const apiClient = new ApiClient({
                    baseUrl: config.apiEndpoint,
                    timeout: 30000, // 30 seconds default timeout
                    apiKey: config.credentials.apiKey,
                    defaultHeaders: {
                        'Content-Type': 'application/json',
                    },
                });

                this.sessionApi = new SessionApi(apiClient, {
                    basePath: '/api/sessions'
                });

                this.logger.info('SessionApi initialized successfully', { 
                    apiEndpoint: config.apiEndpoint 
                });
            } else {
                this.logger.warn('SessionApi not initialized - missing apiEndpoint or apiKey', {
                    hasApiEndpoint: !!config.apiEndpoint,
                    hasApiKey: !!config.credentials?.apiKey
                });
            }

            this.setupPageRefreshRecovery();

            await this.checkForRecoverableSessions();

            // Setup automatic backup
            if (config.backupInterval > 0) {
                this.setupAutoBackup();
            }

            this.logger.info('SessionManager initialized successfully', { config });
        } catch (error) {
            this.logger.error('Failed to initialize SessionManager', { error });
            throw new SessionManagerError('SESSION_INIT_FAILED', 'Failed to initialize session manager', error as Error);
        }
    }

    public async createSession(metadata: Partial<SessionMetadata> = {}): Promise<MedicalSession> {
        const startTime = performance.now();

        try {
            if (this.currentSession) {
                await this.endSession();
            }

            const deviceInfo = await this.deviceInfoCollector.collect();
            const sessionId = this.generateSessionId();

            const session: MedicalSession = {
                id: sessionId,
                startTime: new Date(),
                lastActivity: new Date(),
                status: 'IDLE',
                audioData: {
                    totalDuration: 0,
                    chunks: [],
                    sampleRate: 16000,
                    channels: 1,
                    format: 'pcm',
                    totalSize: 0,
                },
                transcriptData: {
                    segments: [],
                    totalCharacters: 0,
                    language: 'en-US',
                    confidence: 0,
                },
                metadata: {
                    tags: [],
                    priority: 'medium',
                    sessionType: 'consultation',
                    customFields: {},
                    ...metadata,
                },
                deviceInfo,
                version: 1,
                checksum: '',
            };

            session.checksum = await this.calculateSessionChecksum(session);

            await this.storageManager.storeSession(session);

            this.currentSession = session;

            // Track performance metrics
            this.performanceMetrics.set(sessionId, {
                sessionId,
                creationTime: performance.now() - startTime,
                lastBackupTime: Date.now(),
                totalSize: 0,
                audioSize: 0,
                transcriptSize: 0,
                metadataSize: 0,
                syncLatency: 0,
                storageOperationTime: 0,
            });

            this.emit('session:created', { session });
            this.logger.info('Session created successfully', { sessionId, metadata });

            return session;
        } catch (error) {
            this.logger.error('Failed to create session', { error, metadata });
            throw new SessionManagerError('SESSION_CREATE_FAILED', 'Failed to create new session', error as Error);
        }
    }

    public async startSession(): Promise<void> {
        if (!this.currentSession) {
            throw new SessionManagerError('NO_ACTIVE_SESSION', 'No active session to start');
        }

        try {
            this.currentSession.status = 'ACTIVE';
            this.currentSession.lastActivity = new Date();

            // Call Session API to create database record if SessionApi is available
            if (this.sessionApi) {
                try {
                    const createSessionRequest = {
                        sessionToken: this.currentSession.id, // Use session ID as token for simplicity
                        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
                        sessionType: 'AGENTIC_SDK' as const,
                        sessionStatus: 'ACTIVE' as const,
                        medicalSessionId: this.currentSession.id,
                        ...(this.currentSession.patientId && { patientId: this.currentSession.patientId }),
                        ...(this.currentSession.metadata.providerInfo?.id && { providerId: this.currentSession.metadata.providerInfo.id }),
                        ...(this.currentSession.metadata.providerInfo?.name && { providerName: this.currentSession.metadata.providerInfo.name }),
                        audioSessionData: {
                            sampleRate: this.currentSession.audioData.sampleRate,
                            channels: this.currentSession.audioData.channels,
                            format: this.currentSession.audioData.format,
                        },
                        transcriptData: {
                            language: this.currentSession.transcriptData.language,
                        },
                        deviceCapabilities: this.currentSession.deviceInfo as unknown as Record<string, unknown>,
                        metadata: {
                            sessionMetadata: this.currentSession.metadata,
                            startTime: new Date(this.currentSession.startTime).toISOString(),
                            version: this.currentSession.version,
                        },
                    };

                    const apiResponse = await this.sessionApi.createSession(createSessionRequest);
                    
                    // Store the server session ID for future reference
                    this.currentSession.metadata.serverSessionId = apiResponse.id;
                    
                    this.logger.info('Session created in database successfully', { 
                        sessionId: this.currentSession.id,
                        serverSessionId: apiResponse.id 
                    });
                } catch (apiError) {
                    this.logger.warn('Failed to create session in database, continuing offline', { 
                        sessionId: this.currentSession.id,
                        error: apiError 
                    });
                    
                    // Add to offline queue for retry when online
                    this.addToOfflineQueue({
                        action: 'create',
                        sessionId: this.currentSession.id,
                        data: {
                            sessionToken: this.currentSession.id,
                            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
                            sessionType: 'AGENTIC_SDK',
                            sessionStatus: 'ACTIVE',
                            medicalSessionId: this.currentSession.id,
                            ...(this.currentSession.patientId && { patientId: this.currentSession.patientId }),
                            ...(this.currentSession.metadata.providerInfo?.id && { providerId: this.currentSession.metadata.providerInfo.id }),
                        },
                        priority: 'high',
                        maxRetries: 3,
                        dependencies: [],
                    });
                }
            }

            this.emit('session:started', { sessionId: this.currentSession.id });
            this.logger.info('Session started', { sessionId: this.currentSession.id });
        } catch (error) {
            this.logger.error('Failed to start session', { error, sessionId: this.currentSession.id });
            throw new SessionManagerError('SESSION_START_FAILED', 'Failed to start session', error as Error);
        }
    }

    public async pauseSession(reason: string = 'User initiated'): Promise<void> {
        if (!this.currentSession) {
            throw new SessionManagerError('NO_ACTIVE_SESSION', 'No active session to pause');
        }

        try {
            this.currentSession.status = 'PAUSED';
            this.currentSession.lastActivity = new Date();

            // First update locally
            await this.updateSession();

            // Then immediately call API to update pause status with reason if available
            if (this.sessionApi && this.currentSession.metadata.serverSessionId) {
                try {
                    const pauseUpdateRequest = {
                        sessionStatus: 'PAUSED' as const,
                        lastAccessedAt: new Date(this.currentSession.lastActivity).toISOString(),
                        metadata: {
                            sessionMetadata: {
                                ...this.currentSession.metadata,
                                pauseReason: reason,
                                pausedAt: new Date(this.currentSession.lastActivity).toISOString(),
                            },
                            version: this.currentSession.version,
                            checksum: this.currentSession.checksum,
                            lastActivity: new Date(this.currentSession.lastActivity).toISOString(),
                        },
                    };

                    await this.sessionApi.updateSession(this.currentSession.metadata.serverSessionId, pauseUpdateRequest);
                    
                    this.logger.info('Session pause status updated in database', { 
                        sessionId: this.currentSession.id,
                        serverSessionId: this.currentSession.metadata.serverSessionId,
                        reason 
                    });
                } catch (apiError) {
                    this.logger.warn('Failed to update pause status in database, queuing for retry', { 
                        sessionId: this.currentSession.id,
                        serverSessionId: this.currentSession.metadata.serverSessionId,
                        reason,
                        error: apiError 
                    });
                    
                    // Add to offline queue for retry when online
                    this.addToOfflineQueue({
                        action: 'update',
                        sessionId: this.currentSession.id,
                        data: {
                            serverSessionId: this.currentSession.metadata.serverSessionId,
                            sessionStatus: 'PAUSED',
                            lastAccessedAt: new Date(this.currentSession.lastActivity).toISOString(),
                            pauseReason: reason,
                            pausedAt: new Date(this.currentSession.lastActivity).toISOString(),
                        },
                        priority: 'high', // Pause events are important for tracking
                        maxRetries: 3,
                        dependencies: [],
                    });
                }
            }

            this.emit('session:paused', { sessionId: this.currentSession.id, reason });
            this.logger.info('Session paused', { sessionId: this.currentSession.id, reason });
        } catch (error) {
            this.logger.error('Failed to pause session', { error, sessionId: this.currentSession.id });
            throw new SessionManagerError('SESSION_PAUSE_FAILED', 'Failed to pause session', error as Error);
        }
    }

    public async resumeSession(): Promise<void> {
        if (!this.currentSession) {
            throw new SessionManagerError('NO_ACTIVE_SESSION', 'No active session to resume');
        }

        try {
            this.currentSession.status = 'ACTIVE';
            this.currentSession.lastActivity = new Date();

            // First update locally
            await this.updateSession();

            // Then immediately call API to update resume status if available
            if (this.sessionApi && this.currentSession.metadata.serverSessionId) {
                try {
                    const resumeUpdateRequest = {
                        sessionStatus: 'ACTIVE' as const,
                        lastAccessedAt: new Date(this.currentSession.lastActivity).toISOString(),
                        metadata: {
                            sessionMetadata: {
                                ...this.currentSession.metadata,
                                resumedAt: new Date(this.currentSession.lastActivity).toISOString(),
                            },
                            version: this.currentSession.version,
                            checksum: this.currentSession.checksum,
                            lastActivity: new Date(this.currentSession.lastActivity).toISOString(),
                        },
                    };

                    await this.sessionApi.updateSession(this.currentSession.metadata.serverSessionId, resumeUpdateRequest);
                    
                    this.logger.info('Session resume status updated in database', { 
                        sessionId: this.currentSession.id,
                        serverSessionId: this.currentSession.metadata.serverSessionId 
                    });
                } catch (apiError) {
                    this.logger.warn('Failed to update resume status in database, queuing for retry', { 
                        sessionId: this.currentSession.id,
                        serverSessionId: this.currentSession.metadata.serverSessionId,
                        error: apiError 
                    });
                    
                    // Add to offline queue for retry when online
                    this.addToOfflineQueue({
                        action: 'update',
                        sessionId: this.currentSession.id,
                        data: {
                            serverSessionId: this.currentSession.metadata.serverSessionId,
                            sessionStatus: 'ACTIVE',
                            lastAccessedAt: new Date(this.currentSession.lastActivity).toISOString(),
                            resumedAt: new Date(this.currentSession.lastActivity).toISOString(),
                        },
                        priority: 'high', // Resume events are important for tracking
                        maxRetries: 3,
                        dependencies: [],
                    });
                }
            }

            this.emit('session:resumed', { sessionId: this.currentSession.id });
            this.logger.info('Session resumed', { sessionId: this.currentSession.id });
        } catch (error) {
            this.logger.error('Failed to resume session', { error, sessionId: this.currentSession.id });
            throw new SessionManagerError('SESSION_RESUME_FAILED', 'Failed to resume session', error as Error);
        }
    }

    public async endSession(): Promise<void> {
        if (!this.currentSession) {
            return;
        }

        const sessionIdToEnd = this.currentSession.id;

        try {
            const duration = Date.now() - this.currentSession.startTime.getTime();

            this.currentSession.status = 'TERMINATED';
            this.currentSession.endTime = new Date();
            this.currentSession.lastActivity = new Date();

            // First update locally
            await this.updateSession();

            // Track if API sync was successful
            let apiSyncSuccessful = false;

            if (this.sessionApi && this.currentSession.metadata.serverSessionId) {
                try {
                    const endUpdateRequest = {
                        sessionStatus: 'TERMINATED' as const,
                        lastAccessedAt: new Date(this.currentSession.lastActivity).toISOString(),
                        metadata: {
                            sessionMetadata: {
                                ...this.currentSession.metadata,
                                endedAt: this.currentSession.endTime!.toISOString(),
                                sessionDuration: duration,
                            },
                            version: this.currentSession.version,
                            checksum: this.currentSession.checksum,
                            lastActivity: new Date(this.currentSession.lastActivity).toISOString(),
                            sessionSummary: {
                                startTime: this.currentSession.startTime.toISOString(),
                                endTime: this.currentSession.endTime!.toISOString(),
                                duration,
                                totalAudioDuration: this.currentSession.audioData.totalDuration,
                                totalCharacters: this.currentSession.transcriptData.totalCharacters,
                                audioChunks: this.currentSession.audioData.chunks.length,
                                transcriptSegments: this.currentSession.transcriptData.segments.length,
                            },
                        },
                    };

                    await this.sessionApi.updateSession(this.currentSession.metadata.serverSessionId, endUpdateRequest);
                    
                    apiSyncSuccessful = true;
                    this.logger.info('Session end status updated in database', { 
                        sessionId: sessionIdToEnd,
                        serverSessionId: this.currentSession.metadata.serverSessionId,
                        duration 
                    });
                } catch (apiError) {
                    this.logger.warn('Failed to update end status in database, queuing for retry', { 
                        sessionId: sessionIdToEnd,
                        serverSessionId: this.currentSession.metadata.serverSessionId,
                        duration,
                        error: apiError 
                    });
                    
                    // Add to offline queue for retry when online
                    this.addToOfflineQueue({
                        action: 'update',
                        sessionId: sessionIdToEnd,
                        data: {
                            serverSessionId: this.currentSession.metadata.serverSessionId,
                            sessionStatus: 'TERMINATED',
                            lastAccessedAt: new Date(this.currentSession.lastActivity).toISOString(),
                            endedAt: this.currentSession.endTime!.toISOString(),
                            sessionDuration: duration,
                        },
                        priority: 'critical', // End events are critical for tracking
                        maxRetries: 5, // More retries for end events
                        dependencies: [],
                    });
                }
            } else {
                // No API available, consider it successful for local operations
                apiSyncSuccessful = true;
            }

            this.emit('session:ended', { sessionId: sessionIdToEnd, duration });
            this.logger.info('Session ended', { sessionId: sessionIdToEnd, duration });

            this.currentSession = null;

            // Delete session from IndexedDB after successful end
            if (apiSyncSuccessful) {
                try {
                    await this.storageManager.deleteSession(sessionIdToEnd);
                    this.logger.info('Session deleted from IndexedDB after successful end', { 
                        sessionId: sessionIdToEnd 
                    });
                } catch (deleteError) {
                    this.logger.warn('Failed to delete session from IndexedDB after end', { 
                        sessionId: sessionIdToEnd,
                        error: deleteError 
                    });
                }
            } else {
                this.logger.info('Session kept in IndexedDB due to API sync failure', { 
                    sessionId: sessionIdToEnd 
                });
            }

        } catch (error) {
            this.logger.error('Failed to end session', { error });
            throw new SessionManagerError('SESSION_END_FAILED', 'Failed to end session', error as Error);
        }
    }

    public getCurrentSession(): MedicalSession | null {
        return this.currentSession;
    }

    public setCurrentSession(session: MedicalSession | null): void {
        this.currentSession = session;
        if (session) {
            this.emit('session:loaded', { session });
        }
    }

    public async updateSessionMetadata(updates: Partial<SessionMetadata>): Promise<void> {
        if (!this.currentSession) {
            throw new SessionManagerError('NO_ACTIVE_SESSION', 'No active session to update');
        }

        try {
            this.currentSession.metadata = {
                ...this.currentSession.metadata,
                ...updates,
            };

            await this.updateSession();
            this.logger.debug('Session metadata updated', { sessionId: this.currentSession.id, updates });
        } catch (error) {
            this.logger.error('Failed to update session metadata', { error, updates });
            throw new SessionManagerError('SESSION_UPDATE_FAILED', 'Failed to update session metadata', error as Error);
        }
    }

    public async setPatientInfo(patientInfo: PatientInfo): Promise<void> {
        if (!this.currentSession) {
            throw new SessionManagerError('NO_ACTIVE_SESSION', 'No active session to update');
        }

        try {
            this.currentSession.patientId = patientInfo.id;
            this.currentSession.metadata.patientInfo = patientInfo;

            await this.updateSession();
            this.logger.info('Patient info set for session', { sessionId: this.currentSession.id, patientId: patientInfo.id });
        } catch (error) {
            this.logger.error('Failed to set patient info', { error, patientInfo });
            throw new SessionManagerError('SESSION_UPDATE_FAILED', 'Failed to set patient info', error as Error);
        }
    }

    public async setProviderInfo(providerInfo: ProviderInfo): Promise<void> {
        if (!this.currentSession) {
            throw new SessionManagerError('NO_ACTIVE_SESSION', 'No active session to update');
        }

        try {
            this.currentSession.metadata.providerInfo = providerInfo;

            await this.updateSession();
            this.logger.info('Provider info set for session', { sessionId: this.currentSession.id, providerId: providerInfo.id });
        } catch (error) {
            this.logger.error('Failed to set provider info', { error, providerInfo });
            throw new SessionManagerError('SESSION_UPDATE_FAILED', 'Failed to set provider info', error as Error);
        }
    }

    public async checkForRecoverableSessions(): Promise<SessionRecoveryInfo[]> {
        try {
            const recoverableSessions = await this.recoveryManager.findRecoverableSessions();

            if (recoverableSessions.length > 0) {
                this.logger.info('Found recoverable sessions', { count: recoverableSessions.length });
            }

            return recoverableSessions;
        } catch (error) {
            this.logger.error('Failed to check for recoverable sessions', { error });
            return [];
        }
    }

    public async recoverSession(sessionId: string, options: RecoveryOptions): Promise<MedicalSession> {
        try {
            this.logger.info('Starting session recovery', { sessionId, options });

            const recoveredSession = await this.recoveryManager.recoverSession(sessionId, options);

            this.currentSession = recoveredSession;

            this.emit('session:recovery-completed', { sessionId, success: true });
            this.logger.info('Session recovery completed successfully', { sessionId });

            return recoveredSession;
        } catch (error) {
            this.emit('session:recovery-completed', { sessionId, success: false });
            this.logger.error('Session recovery failed', { error, sessionId });
            throw new SessionManagerError('SESSION_RECOVERY_FAILED', 'Failed to recover session', error as Error);
        }
    }

    public getSessionMetrics(sessionId: string): SessionPerformanceMetrics | undefined {
        return this.performanceMetrics.get(sessionId);
    }

    public async cleanup(): Promise<{ removedSessions: number; freedSpace: number }> {
        try {
            if (this.beforeUnloadHandler && typeof window !== 'undefined') {
                window.removeEventListener('beforeunload', this.beforeUnloadHandler);
                this.beforeUnloadHandler = null;
            }
            if (this.pageLoadHandler && typeof document !== 'undefined') {
                document.removeEventListener('DOMContentLoaded', this.pageLoadHandler);
                this.pageLoadHandler = null;
            }

            const result = await this.storageManager.cleanup();

            if (this.autoBackupInterval) {
                clearInterval(this.autoBackupInterval);
                this.autoBackupInterval = null;
            }

            this.emit('session:cleanup-completed', result);
            this.logger.info('Session cleanup completed', result);

            return result;
        } catch (error) {
            this.logger.error('Session cleanup failed', { error });
            throw new SessionManagerError('SESSION_CLEANUP_FAILED', 'Failed to cleanup sessions', error as Error);
        }
    }

    async updateSession(jsonData?: Record<string, any>): Promise<void> {
        if (!this.currentSession) return;

        try {
            this.currentSession.lastActivity = new Date();
            this.currentSession.version++;
            this.currentSession.checksum = await this.calculateSessionChecksum(this.currentSession);

            await this.storageManager.storeSession(this.currentSession);

            // Call Session API to update database record if SessionApi is available and serverSessionId exists
            if (this.sessionApi && this.currentSession.metadata.serverSessionId) {
                try {
                    const updateSessionRequest = {
                        lastAccessedAt: new Date(this.currentSession.lastActivity).toISOString(),
                        sessionStatus: this.currentSession.status,
                        ...(this.currentSession.patientId && { patientId: this.currentSession.patientId }),
                        ...(this.currentSession.metadata.providerInfo?.id && { providerId: this.currentSession.metadata.providerInfo.id }),
                        ...(this.currentSession.metadata.providerInfo?.name && { providerName: this.currentSession.metadata.providerInfo.name }),
                        audioSessionData: {
                            sampleRate: this.currentSession.audioData.sampleRate,
                            channels: this.currentSession.audioData.channels,
                            format: this.currentSession.audioData.format,
                            totalDuration: this.currentSession.audioData.totalDuration,
                            totalSize: this.currentSession.audioData.totalSize,
                        },
                        transcriptData: {
                            language: this.currentSession.transcriptData.language,
                            totalCharacters: this.currentSession.transcriptData.totalCharacters,
                            confidence: this.currentSession.transcriptData.confidence,
                        },
                        deviceCapabilities: this.currentSession.deviceInfo as unknown as Record<string, unknown>,
                        metadata: {
                            sessionMetadata: this.currentSession.metadata,
                            version: this.currentSession.version,
                            checksum: this.currentSession.checksum,
                            lastActivity: new Date(this.currentSession.lastActivity).toISOString(),
                        },
                        ...(jsonData ? { jsonData } : {}),
                    };


                    await this.sessionApi.updateSession(this.currentSession.metadata.serverSessionId, updateSessionRequest);
                    
                    this.logger.debug('Session updated in database successfully', { 
                        sessionId: this.currentSession.id,
                        serverSessionId: this.currentSession.metadata.serverSessionId 
                    });
                } catch (apiError) {
                    this.logger.warn('Failed to update session in database, queuing for retry', { 
                        sessionId: this.currentSession.id,
                        serverSessionId: this.currentSession.metadata.serverSessionId,
                        error: apiError 
                    });
                    
                    // Add to offline queue for retry when online
                    this.addToOfflineQueue({
                        action: 'update',
                        sessionId: this.currentSession.id,
                        data: {
                            serverSessionId: this.currentSession.metadata.serverSessionId,
                            lastAccessedAt: new Date(this.currentSession.lastActivity).toISOString(),
                            sessionStatus: this.currentSession.status,
                            version: this.currentSession.version,
                        },
                        priority: 'medium',
                        maxRetries: 3,
                        dependencies: [],
                    });
                }
            }
        } catch (error) {
            this.logger.error('Failed to update session', { error, sessionId: this.currentSession.id });
            throw error;
        }
    }


    private async calculateSessionChecksum(session: MedicalSession): Promise<string> {
        const sessionForHash = { ...session, checksum: '' };
        const data = JSON.stringify(sessionForHash);

        try {
            if (typeof crypto !== 'undefined' && crypto.subtle) {
                const encoder = new TextEncoder();
                const dataBuffer = encoder.encode(data);
                const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
                return hashHex;
            }
        } catch (error) {
            console.warn('Web Crypto API not available, using fallback checksum:', error);
        }

        // Fallback: Simple hash algorithm
        return this.simpleHash(data);
    }

    private simpleHash(str: string): string {
        let hash = 0;
        if (str.length === 0) return hash.toString(16);
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        
        // Convert to positive hex string
        return Math.abs(hash).toString(16).padStart(8, '0');
    }

    private generateSessionId(): string {
        return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    private setupAutoBackup(): void {
        if (!this.config?.backupInterval) return;

        this.autoBackupInterval = setInterval(async () => {
            if (this.currentSession && this.currentSession.status === 'ACTIVE') {
                try {
                    await this.createSessionBackup();
                } catch (error) {
                    this.logger.error('Auto backup failed', { error, sessionId: this.currentSession.id });
                }
            }
        }, this.config.backupInterval);
    }

    private async createSessionBackup(): Promise<void> {
        if (!this.currentSession) return;

        try {
            const backupId = await this.storageManager.createBackup(this.currentSession);

            this.emit('session:backup-created', { sessionId: this.currentSession.id, backupId });
            this.logger.debug('Session backup created', { sessionId: this.currentSession.id, backupId });
        } catch (error) {
            this.logger.error('Failed to create session backup', { error, sessionId: this.currentSession.id });
        }
    }

    private setupEventHandlers(): void {
        this.recoveryManager.on('recovery-progress', (data: any) => {
            this.emit('session:recovery-progress', data);
        });

        this.storageManager.on('storage-warning', (data: any) => {
            this.emit('session:storage-warning', data);
        });
    }

    private setupNetworkMonitoring(): void {
        if (typeof window === 'undefined') {
            return;
        }

        window.addEventListener('online', () => {
            this.handleNetworkOnline();
        });

        window.addEventListener('offline', () => {
            this.handleNetworkOffline();
        });

        if (!this.isOnline) {
            this.logger.warn('Application started offline');
        }

        this.restoreOfflineQueue();
    }

    private async handleNetworkOnline(): Promise<void> {
        this.isOnline = true;
        this.reconnectAttempts = 0;
        this.logger.info('Network connection restored');

        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }

        await this.processOfflineQueue();
    }

    private handleNetworkOffline(): void {
        this.isOnline = false;
        this.logger.warn('Network connection lost');

        this.scheduleReconnection();
    }

    private scheduleReconnection(): void {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.logger.error('Max reconnection attempts reached');
            return;
        }

        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000); // Max 30 seconds
        this.reconnectAttempts++;

        this.reconnectTimeout = setTimeout(() => {
            if (!this.isOnline) {
                this.logger.info(`Reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
                this.scheduleReconnection();
            }
        }, delay);
    }

    public addToOfflineQueue(action: Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>): void {
        const offlineAction: OfflineAction = {
            id: `offline_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            timestamp: Date.now(),
            retryCount: 0,
            ...action,
        };

        this.offlineActionQueue.push(offlineAction);
        this.logger.debug('Action queued for offline processing', { actionId: offlineAction.id });

        this.persistOfflineQueue();
    }

    private async processOfflineQueue(): Promise<void> {
        if (this.syncInProgress || this.offlineActionQueue.length === 0) {
            return;
        }

        this.syncInProgress = true;
        this.emit('session:sync-started', { sessionId: this.currentSession?.id || 'unknown' });

        let successCount = 0;
        let errorCount = 0;

        // Sort by priority and timestamp
        const sortedQueue = [...this.offlineActionQueue].sort((a, b) => {
            const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
            const aPriority = priorityOrder[a.priority];
            const bPriority = priorityOrder[b.priority];

            if (aPriority !== bPriority) {
                return aPriority - bPriority;
            }
            return a.timestamp - b.timestamp;
        });

        for (const action of sortedQueue) {
            try {
                await this.processOfflineAction(action);
                this.removeFromOfflineQueue(action.id);
                successCount++;
            } catch (error) {
                action.retryCount++;
                errorCount++;

                if (action.retryCount >= action.maxRetries) {
                    this.logger.error('Offline action exceeded max retries', { actionId: action.id });
                    this.removeFromOfflineQueue(action.id);
                } else {
                    this.logger.warn('Offline action failed, will retry', {
                        actionId: action.id,
                        retryCount: action.retryCount
                    });
                }
            }
        }

        this.syncInProgress = false;
        this.persistOfflineQueue();

        this.emit('session:sync-completed', {
            sessionId: this.currentSession?.id || 'unknown',
            success: errorCount === 0,
            processedActions: successCount,
            failedActions: errorCount
        });

        this.logger.info('Offline queue processing completed', {
            successCount,
            errorCount,
            remainingActions: this.offlineActionQueue.length
        });
    }

    private async processOfflineAction(action: OfflineAction): Promise<void> {
        if (!this.sessionApi) {
            throw new Error('SessionApi not available for offline action processing');
        }

        switch (action.action) {
            case 'create':
                await this.sessionApi.createSession(action.data as any);
                this.logger.debug('Offline session creation synced', { actionId: action.id });
                break;
            case 'update':
                const updateData = action.data as any;
                if (!updateData.serverSessionId) {
                    throw new Error('Missing serverSessionId for update action');
                }
                await this.sessionApi.updateSession(updateData.serverSessionId, updateData);
                this.logger.debug('Offline session update synced', { actionId: action.id, serverSessionId: updateData.serverSessionId });
                break;
            case 'upload':
                await this.delay(200);
                break;
            case 'delete':
                await this.delay(50);
                break;
            default:
                throw new Error(`Unknown offline action: ${action.action}`);
        }
    }

    private removeFromOfflineQueue(actionId: string): void {
        this.offlineActionQueue = this.offlineActionQueue.filter(action => action.id !== actionId);
    }

    private persistOfflineQueue(): void {
        try {
            if (typeof localStorage !== 'undefined') {
                localStorage.setItem('AgenticSDK_OfflineQueue', JSON.stringify(this.offlineActionQueue));
            }
        } catch (error) {
            this.logger.error('Failed to persist offline queue', { error });
        }
    }

    private restoreOfflineQueue(): void {
        try {
            if (typeof localStorage === 'undefined') {
                return;
            }

            const queueData = localStorage.getItem('AgenticSDK_OfflineQueue');
            if (queueData) {
                this.offlineActionQueue = JSON.parse(queueData);
                this.logger.info('Offline queue restored', { queueLength: this.offlineActionQueue.length });
            }
        } catch (error) {
            this.logger.error('Failed to restore offline queue', { error });
            this.offlineActionQueue = [];
        }
    }

    public getSyncStatus(): {
        isOnline: boolean;
        syncInProgress: boolean;
        pendingActions: number;
        lastSyncTime?: Date;
    } {
        return {
            isOnline: this.isOnline,
            syncInProgress: this.syncInProgress,
            pendingActions: this.offlineActionQueue.length,
        };
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    private setupPageRefreshRecovery(): void {
        if (typeof window === 'undefined' || typeof document === 'undefined') {
            return;
        }

        if (this.beforeUnloadHandler) {
            window.removeEventListener('beforeunload', this.beforeUnloadHandler);
        }
        if (this.pageLoadHandler) {
            window.removeEventListener('load', this.pageLoadHandler);
        }

        this.beforeUnloadHandler = (_event: BeforeUnloadEvent) => {
            this.handlePageRefresh();
        };

        this.pageLoadHandler = () => {
            this.handlePageLoad();
        };

        window.addEventListener('beforeunload', this.beforeUnloadHandler);
        document.addEventListener('DOMContentLoaded', this.pageLoadHandler);
    }

    private async handlePageRefresh(): Promise<void> {
        if (this.currentSession && this.currentSession.status === 'ACTIVE') {
            try {
                this.currentSession.lastActivity = new Date();

                await this.createSessionBackup();

                // Store session state in sessionStorage for immediate recovery
                const sessionState = {
                    sessionId: this.currentSession.id,
                    wasActive: true,
                    timestamp: Date.now(),
                    lastActivity: new Date(this.currentSession.lastActivity).toISOString(),
                };

                if (typeof sessionStorage !== 'undefined') {
                    sessionStorage.setItem('AgenticSDK_PageRefreshState', JSON.stringify(sessionState));
                }

                this.logger.info('Session preserved for page refresh', { sessionId: this.currentSession.id });
            } catch (error) {
                this.logger.error('Failed to preserve session during page refresh', { error });
            }
        }
    }

    private async handlePageLoad(): Promise<void> {
        try {
            if (typeof sessionStorage === 'undefined') {
                return;
            }

            const pageRefreshState = sessionStorage.getItem('AgenticSDK_PageRefreshState');

            if (pageRefreshState) {
                const state = JSON.parse(pageRefreshState);
                const timeSinceRefresh = Date.now() - state.timestamp;

                // Only recover if refresh was recent (within 30 seconds)
                if (timeSinceRefresh < 30000) {
                    await this.recoverFromPageRefresh(state);
                }

                // Clean up the temporary state
                sessionStorage.removeItem('AgenticSDK_PageRefreshState');
            }
        } catch (error) {
            this.logger.error('Failed to restore session after page refresh', { error });
        }
    }

    private async recoverFromPageRefresh(state: any): Promise<void> {
        try {
            const recoveryInfo = await this.checkForRecoverableSessions();
            const targetSession = recoveryInfo.find(info => info.sessionId === state.sessionId);

            if (targetSession && targetSession.dataIntegrity !== 'corrupted') {
                this.emit('session:recovery-started', {
                    sessionId: state.sessionId,
                    recoveryInfo: targetSession
                });

                const recoveredSession = await this.recoverSession(state.sessionId, {
                    autoRecover: true,
                    showRecoveryModal: false,
                    maxRecoveryAttempts: 3,
                    recoveryTimeout: 5000,
                    fallbackToPartialRecovery: true,
                    userConfirmationRequired: false,
                });
                this.logger.info('Recovered session', { session: recoveredSession });

                this.emit('session:recovery-completed', {
                    sessionId: state.sessionId,
                    success: true
                });

                this.logger.info('Session successfully recovered after page refresh', {
                    sessionId: state.sessionId,
                    recoveryTime: Date.now() - state.timestamp
                });
            }
        } catch (error) {
            this.emit('session:recovery-completed', {
                sessionId: state.sessionId,
                success: false
            });
            this.logger.error('Failed to recover session after page refresh', { error });
        }
    }

    public getSessionApi(): SessionApi | null {
        return this.sessionApi;
    }

    public getStorageManager(): SessionStorageManager {
        return this.storageManager;
    }

    public hasSessionApi(): boolean {
        return this.sessionApi !== null;
    }

    public async setSession(sessionId: string): Promise<MedicalSession> {
        try {
            this.logger.info('Loading session from API', { sessionId });

            // Call Session API to get session data if available
            if (this.sessionApi) {
                try {
                    const apiSession = await this.sessionApi.getSession({ id: sessionId });
                    
                    const medicalSession: MedicalSession = {
                        id: apiSession.medicalSessionId || sessionId,
                        startTime: new Date(apiSession.createdAt),
                        lastActivity: new Date(apiSession.lastAccessedAt || apiSession.createdAt),
                        status: apiSession.sessionStatus,
                        audioData: {
                            totalDuration: 0,
                            chunks: [],
                            sampleRate: (apiSession.audioSessionData as any)?.sampleRate || 16000,
                            channels: (apiSession.audioSessionData as any)?.channels || 1,
                            format: (apiSession.audioSessionData as any)?.format || 'pcm',
                            totalSize: 0,
                        },
                        transcriptData: {
                            segments: [],
                            totalCharacters: 0,
                            language: (apiSession.transcriptData as any)?.language || 'en-US',
                            confidence: 0,
                        },
                        metadata: {
                            tags: ['medical-consultation'],
                            priority: 'medium',
                            sessionType: 'consultation',
                            customFields: {},
                            serverSessionId: apiSession.id,
                            ...(apiSession.jsonData?.sessionMetadata || {}),
                            ...(apiSession.patientId && { 
                                patientInfo: { 
                                    id: apiSession.patientId, 
                                    name: 'Patient' // Default name, will be updated by setPatientInfo if needed
                                } 
                            }),
                            ...(apiSession.providerId && { 
                                providerInfo: { 
                                    id: apiSession.providerId, 
                                    name: apiSession.providerName || 'Provider',
                                    role: 'physician'
                                } 
                            }),
                        },
                        deviceInfo: (apiSession.deviceCapabilities as any) || {
                            deviceId: 'unknown',
                            deviceType: 'desktop',
                            browser: 'unknown',
                            browserVersion: 'unknown',
                            platform: 'unknown',
                            userAgent: 'unknown',
                            screenResolution: 'unknown',
                            timezone: 'unknown',
                            lastSeen: new Date(),
                        },
                        version: (apiSession.jsonData as any)?.version || 1,
                        checksum: '',
                        ...(apiSession.patientId && { patientId: apiSession.patientId }),
                    };

                    medicalSession.checksum = await this.calculateSessionChecksum(medicalSession);

                    await this.storageManager.storeSession(medicalSession);

                    this.currentSession = medicalSession;

                    this.emit('session:loaded', { session: medicalSession });
                    this.logger.info('Session loaded successfully from API', { 
                        sessionId, 
                        serverSessionId: apiSession.id 
                    });

                    return medicalSession;

                } catch (apiError) {
                    this.logger.warn('Failed to load session from API, checking local storage', { 
                        sessionId, 
                        error: apiError 
                    });
                    
                    // Fallback to local storage
                    const localSession = await this.storageManager.getSession(sessionId);
                    if (localSession) {
                        this.currentSession = localSession;
                        this.emit('session:loaded', { session: localSession });
                        this.logger.info('Session loaded from local storage', { sessionId });
                        return localSession;
                    }
                    
                    throw new SessionManagerError(
                        'SESSION_NOT_FOUND', 
                        `Session not found in API or local storage: ${sessionId}`,
                        apiError as Error
                    );
                }
            } else {
                // No API available, try local storage only
                const localSession = await this.storageManager.getSession(sessionId);
                if (localSession) {
                    this.currentSession = localSession;
                    this.emit('session:loaded', { session: localSession });
                    this.logger.info('Session loaded from local storage (no API)', { sessionId });
                    return localSession;
                }
                
                throw new SessionManagerError(
                    'SESSION_NOT_FOUND', 
                    `Session not found in local storage: ${sessionId}`
                );
            }

        } catch (error) {
            this.logger.error('Failed to set session', { error, sessionId });
            throw new SessionManagerError('SESSION_LOAD_FAILED', 'Failed to load session', error as Error);
        }
    }

}

class SessionManagerError extends Error {
    public readonly sessionId?: string;
    public readonly errorType: 'storage' | 'encryption' | 'network' | 'recovery' | 'sync' | 'validation';
    public readonly errorCode: string;
    public readonly recoverable: boolean;
    public readonly userMessage: string;
    public readonly technicalDetails: Record<string, unknown>;
    public readonly timestamp: Date;

    constructor(
        errorCode: string,
        userMessage: string,
        originalError?: Error,
        sessionId?: string,
        errorType: 'storage' | 'encryption' | 'network' | 'recovery' | 'sync' | 'validation' = 'storage'
    ) {
        super(userMessage);
        this.name = 'SessionManagerError';
        this.errorCode = errorCode;
        this.errorType = errorType;
        this.userMessage = userMessage;
        if (sessionId !== undefined) {
            this.sessionId = sessionId;
        }
        this.timestamp = new Date();
        this.recoverable = this.determineRecoverability(errorCode);
        this.technicalDetails = {
            originalError: originalError?.message,
            stack: originalError?.stack,
        };

        if (originalError?.stack) {
            this.stack = originalError.stack;
        }
    }

    private determineRecoverability(errorCode: string): boolean {
        const recoverableErrors = [
            'SESSION_PAUSE_FAILED',
            'SESSION_RESUME_FAILED',
            'SESSION_UPDATE_FAILED',
            'SESSION_BACKUP_FAILED',
        ];
        return recoverableErrors.includes(errorCode);
    }
}

export { SessionManagerError as SessionError };
