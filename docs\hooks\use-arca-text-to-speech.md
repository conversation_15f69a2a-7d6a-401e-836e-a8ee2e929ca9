# useArcaTextToSpeech

The `useArcaTextToSpeech` hook provides real-time text-to-speech synthesis with streaming audio playback, multiple voice options, and audio download capabilities.

## Overview

This hook establishes a WebSocket connection to the text-to-speech service and provides real-time synthesis of text into natural-sounding speech. It supports multiple languages and voices, with automatic session integration for medical consultation scenarios.

## Import

```typescript
import { useArcaTextToSpeech } from "@arcaai/agentic-sdk";
```

## Configuration Setup

Before using the hook, ensure you have your SDK configuration ready:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
    apiEndpoint: 'https://api.arcaai.com',
    websocketUrl: 'wss://api.arcaai.com',
    credentials: {
        apiKey: 'your-api-key-here',
    },
    // Optional: Additional configuration
    logging: {
        level: 'info',
        console: true,
    },
    environment: 'production',
};
```

## Basic Usage

```typescript
import React, { useState } from "react";
import { useArcaTextToSpeech } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

function TextToSpeechDemo() {
  const [inputText, setInputText] = useState(
    "Hello! This is a test of the text-to-speech service."
  );
  const [language, setLanguage] = useState("en");

  const [
    sendTextData,
    loading,
    error,
    connect,
    disconnect,
    isConnected,
    audioData,
    downloadAudio,
  ] = useArcaTextToSpeech({
    apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
    socketPath: "/tts",
    language: language,
    storeAudio: true,
    options: SDK_CONFIG_OPTIONS,
  });

  const handleSynthesize = () => {
    if (!inputText.trim() || !isConnected) return;

    sendTextData(inputText, {
      voice: getVoiceForLanguage(language),
      rate: 1.0,
      pitch: "medium",
    });
  };

  const handleDownload = () => {
    if (!audioData || audioData.length === 0) {
      alert("No audio available to download");
      return;
    }

    const url = downloadAudio();
    if (url) {
      const link = document.createElement("a");
      link.href = url;
      link.download = `tts-${language}-${Date.now()}.mp3`;
      link.click();
      URL.revokeObjectURL(url);
    }
  };

  const getVoiceForLanguage = (lang: string) => {
    const voiceMap = {
      en: "en-US-JennyNeural",
      hi: "hi-IN-SwaraNeural",
      ta: "ta-IN-PallaviNeural",
      ml: "ml-IN-SobhanaNeural",
    };
    return voiceMap[lang] || voiceMap["en"];
  };

  return (
    <div>
      <h2>Text-to-Speech Demo</h2>

      <div>
        <label>Language:</label>
        <select value={language} onChange={(e) => setLanguage(e.target.value)}>
          <option value="en">English</option>
          <option value="hi">Hindi</option>
          <option value="ta">Tamil</option>
          <option value="ml">Malayalam</option>
        </select>
      </div>

      <div>
        <textarea
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          placeholder="Enter text to synthesize..."
          rows={4}
          style={{ width: "100%", margin: "10px 0" }}
        />
      </div>

      <div>
        <span>Status: </span>
        <span
          style={{
            color: isConnected ? "green" : "red",
            fontWeight: "bold",
          }}
        >
          {isConnected ? "Connected" : "Disconnected"}
        </span>
        <button
          onClick={isConnected ? disconnect : connect}
          style={{ marginLeft: "10px" }}
        >
          {isConnected ? "Disconnect" : "Connect"}
        </button>
      </div>

      <div style={{ margin: "10px 0" }}>
        <button
          onClick={handleSynthesize}
          disabled={loading || !isConnected || !inputText.trim()}
          style={{
            backgroundColor: loading ? "#ccc" : "#007bff",
            color: "white",
            padding: "10px 20px",
            marginRight: "10px",
          }}
        >
          {loading ? "Synthesizing..." : "Synthesize Speech"}
        </button>

        <button
          onClick={handleDownload}
          disabled={!audioData || audioData.length === 0}
          style={{
            backgroundColor:
              !audioData || audioData.length === 0 ? "#ccc" : "#28a745",
            color: "white",
            padding: "10px 20px",
          }}
        >
          Download Audio
        </button>
      </div>

      {audioData && audioData.length > 0 && (
        <div
          style={{
            padding: "10px",
            backgroundColor: "#e8f5e8",
            border: "1px solid #28a745",
            borderRadius: "4px",
          }}
        >
          Audio ready: {audioData.length} chunks,
          {Math.round(
            audioData.reduce((total, chunk) => total + chunk.length, 0) / 1024
          )} KB
        </div>
      )}

      {error && (
        <div style={{ color: "red", margin: "10px 0" }}>Error: {error}</div>
      )}
    </div>
  );
}
```

## API Reference

### Parameters

The hook accepts a configuration object with the following properties:

#### `I_ArcaTTSOptions`

| Property      | Type                 | Required | Description                       |
| ------------- | -------------------- | -------- | --------------------------------- |
| `apiEndpoint` | `string`             | ❌       | TTS API endpoint URL (use `SDK_CONFIG_OPTIONS.apiEndpoint`) |
| `socketPath`  | `string`             | ❌       | WebSocket path (default: '/tts')  |
| `language`    | `string`             | ❌       | Default language for synthesis    |
| `storeAudio`  | `boolean`            | ❌       | Enable audio storage for download |
| `options`     | `Partial<SDKConfig>` | ❌       | SDK configuration options (use `SDK_CONFIG_OPTIONS`) |

### Return Value

The hook returns a tuple with the following elements:

#### `useArcaTextToSpeech` Return Tuple

| Index | Type                                           | Description                 |
| ----- | ---------------------------------------------- | --------------------------- |
| `[0]` | `(text: string, options?: TTSOptions) => void` | Send text for synthesis     |
| `[1]` | `boolean`                                      | Loading/synthesis state     |
| `[2]` | `string \| null`                               | Current error message       |
| `[3]` | `() => void`                                   | Connect to TTS service      |
| `[4]` | `() => void`                                   | Disconnect from TTS service |
| `[5]` | `boolean`                                      | Connection status           |
| `[6]` | `Uint8Array[]`                                 | Audio data chunks           |
| `[7]` | `() => string \| null`                         | Download audio as blob URL  |

### TTS Options

```typescript
interface TTSOptions {
  voice?: string; // Voice identifier
  rate?: number; // Speech rate (0.5 - 2.0)
  pitch?: string; // Pitch level ('low', 'medium', 'high')
  style?: string; // Speaking style ('neutral', 'cheerful', 'sad', etc.)
  styleLevel?: number; // Style intensity (0.1 - 2.0)
}
```

## Advanced Usage

### Integration with Session Manager

```typescript
import { useArcaSessionManager, useArcaTextToSpeech } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

function MedicalConsultationWithTTS() {
  const sessionManager = useArcaSessionManager({
    doctorId: "dr-123",
    doctorName: "Dr. Smith",
    patientId: "pt-456",
    patientName: "John Doe",
    options: SDK_CONFIG_OPTIONS,
  });

  const [
    sendTextData,
    ttsLoading,
    ttsError,
    ttsConnect,
    ttsDisconnect,
    ttsIsConnected,
    ttsAudioData,
    downloadAudio,
  ] = useArcaTextToSpeech({
    apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
    socketPath: "/tts",
    language: "en",
    storeAudio: true,
    options: SDK_CONFIG_OPTIONS,
  });

  // Auto-connect TTS when session starts
  useEffect(() => {
    if (sessionManager.session?.status === "ACTIVE" && !ttsIsConnected) {
      ttsConnect();
    }
  }, [sessionManager.session?.status, ttsIsConnected, ttsConnect]);

  const handleSynthesizeText = (text: string) => {
    if (sessionManager.session?.status === "ACTIVE" && ttsIsConnected) {
      sendTextData(text, {
        voice: "en-US-JennyNeural",
        rate: 1.0,
        style: "neutral",
      });
    }
  };

  return (
    <div>
      <h2>Medical Consultation with TTS</h2>

      {/* Session status */}
      <p>Session: {sessionManager.session?.status || "No session"}</p>
      <p>TTS: {ttsIsConnected ? "Connected" : "Disconnected"}</p>

      {/* Quick response buttons */}
      <div>
        <button
          onClick={() => handleSynthesizeText("How are you feeling today?")}
          disabled={!ttsIsConnected || ttsLoading}
        >
          Ask How They Feel
        </button>
        <button
          onClick={() => handleSynthesizeText("Please describe your symptoms.")}
          disabled={!ttsIsConnected || ttsLoading}
        >
          Ask About Symptoms
        </button>
      </div>
    </div>
  );
}
```

### Multi-language Support

```typescript
import { useArcaTextToSpeech } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

function MultiLanguageTTS() {
  const [currentLanguage, setCurrentLanguage] = useState("en");

  const [
    sendTextData,
    loading,
    error,
    connect,
    disconnect,
    isConnected,
    audioData,
    downloadAudio,
  ] = useArcaTextToSpeech({
    apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
    socketPath: "/tts",
    language: currentLanguage,
    storeAudio: true,
    options: SDK_CONFIG_OPTIONS,
  });

  const languageVoices = {
    en: { voice: "en-US-JennyNeural", sample: "Hello, how can I help you today?" },
    hi: { voice: "hi-IN-SwaraNeural", sample: "नमस्ते, आज मैं आपकी कैसे सहायता कर सकता हूं?" },
    ta: { voice: "ta-IN-PallaviNeural", sample: "வணக்கம், இன்று நான் உங்களுக்கு எப்படி உதவ முடியும்?" },
    ml: { voice: "ml-IN-SobhanaNeural", sample: "നമസ്കാരം, ഇന്ന് ഞാൻ നിങ്ങളെ എങ്ങനെ സഹായിക്കാൻ കഴിയും?" },
  };

  const handleLanguageChange = (lang: string) => {
    setCurrentLanguage(lang);
    // Test the new language
    const config = languageVoices[lang];
    if (config && isConnected) {
      sendTextData(config.sample, { voice: config.voice });
    }
  };

  return (
    <div>
      <h2>Multi-language TTS Demo</h2>

      <div>
        <label>Language: </label>
        {Object.keys(languageVoices).map((lang) => (
          <button
            key={lang}
            onClick={() => handleLanguageChange(lang)}
            style={{
              margin: "0 5px",
              backgroundColor: currentLanguage === lang ? "#007bff" : "#f8f9fa",
              color: currentLanguage === lang ? "white" : "black",
            }}
          >
            {lang.toUpperCase()}
          </button>
        ))}
      </div>
    </div>
  );
}
```

### Custom Voice Styles

```typescript
import { useArcaTextToSpeech } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

function StyledTTS() {
  const [
    sendTextData,
    loading,
    error,
    connect,
    disconnect,
    isConnected,
  ] = useArcaTextToSpeech({
    apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
    socketPath: "/tts",
    language: "en",
    options: SDK_CONFIG_OPTIONS,
  });

  const voiceStyles = [
    { style: "neutral", description: "Professional tone" },
    { style: "cheerful", description: "Friendly and upbeat" },
    { style: "sad", description: "Compassionate tone" },
    { style: "angry", description: "Serious and firm" },
    { style: "fearful", description: "Concerned tone" },
  ];

  const handleSynthesizeWithStyle = (style: string) => {
    const text = "I understand your concerns and we'll address them together.";
    sendTextData(text, {
      voice: "en-US-JennyNeural",
      style: style,
      styleLevel: 1.5,
      rate: 1.0,
    });
  };

  return (
    <div>
      <h2>Voice Style Demo</h2>

      <div>
        <p>Click to hear the same text in different emotional styles:</p>
        {voiceStyles.map((styleOption) => (
          <div key={styleOption.style}>
            <button
              onClick={() => handleSynthesizeWithStyle(styleOption.style)}
              disabled={!isConnected || loading}
              style={{ margin: "5px 0", padding: "10px" }}
            >
              {styleOption.style.charAt(0).toUpperCase() + styleOption.style.slice(1)}
            </button>
            <span style={{ marginLeft: "10px", color: "#666" }}>
              {styleOption.description}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}
```

## Supported Languages and Voices

### Voice Options by Language

| Language               | Voice ID             | Gender | Quality | Medical Optimized |
| ---------------------- | -------------------- | ------ | ------- | ----------------- |
| **English**            |
| `en-US-JennyNeural`    | en-US-JennyNeural    | Female | High    | ✅                |
| `en-US-GuyNeural`      | en-US-GuyNeural      | Male   | High    | ✅                |
| `en-GB-SoniaNeural`    | en-GB-SoniaNeural    | Female | High    | ✅                |
| **Hindi**              |
| `hi-IN-SwaraNeural`    | hi-IN-SwaraNeural    | Female | High    | ✅                |
| `hi-IN-MadhurNeural`   | hi-IN-MadhurNeural   | Male   | High    | ✅                |
| **Tamil**              |
| `ta-IN-PallaviNeural`  | ta-IN-PallaviNeural  | Female | High    | ✅                |
| `ta-IN-ValluvarNeural` | ta-IN-ValluvarNeural | Male   | High    | ✅                |
| **Malayalam**          |
| `ml-IN-SobhanaNeural`  | ml-IN-SobhanaNeural  | Female | High    | ✅                |
| `ml-IN-MidhunNeural`   | ml-IN-MidhunNeural   | Male   | High    | ✅                |

## Best Practices

### 1. Connection Management

```typescript
import { useArcaTextToSpeech } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

// Auto-manage TTS connection based on session state
const [
  sendTextData,
  ttsLoading,
  ttsError,
  ttsConnect,
  ttsDisconnect,
  ttsIsConnected,
] = useArcaTextToSpeech({
  apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
  socketPath: "/tts",
  language: "en",
  options: SDK_CONFIG_OPTIONS,
});

// Connect when needed, disconnect when done
useEffect(() => {
  return () => {
    if (ttsIsConnected) {
      ttsDisconnect();
    }
  };
}, [ttsIsConnected, ttsDisconnect]);
```

### 2. Error Handling

```typescript
import { useArcaTextToSpeech } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const [, , ttsError] = useArcaTextToSpeech({
  apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
  socketPath: "/tts",
  language: "en",
  options: SDK_CONFIG_OPTIONS,
});

// Handle TTS errors gracefully
useEffect(() => {
  if (ttsError) {
    console.error("TTS Error:", ttsError);

    // Show user-friendly error message
    if (ttsError.includes("connection")) {
      showNotification("Connection issue with speech service", "warning");
    } else if (ttsError.includes("authentication")) {
      showNotification("Authentication failed", "error");
    } else {
      showNotification("Speech synthesis temporarily unavailable", "info");
    }
  }
}, [ttsError]);
```

### 3. Performance Optimization

```typescript
import { useArcaTextToSpeech } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

// Debounce text input to avoid excessive synthesis requests
const [inputText, setInputText] = useState("");
const [debouncedText, setDebouncedText] = useState("");

useEffect(() => {
  const timer = setTimeout(() => {
    setDebouncedText(inputText);
  }, 500);

  return () => clearTimeout(timer);
}, [inputText]);

const [sendTextData] = useArcaTextToSpeech({
  apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
  socketPath: "/tts",
  language: "en",
  options: SDK_CONFIG_OPTIONS,
});

// Only synthesize when text is stable
useEffect(() => {
  if (debouncedText && debouncedText.length > 10) {
    sendTextData(debouncedText);
  }
}, [debouncedText, sendTextData]);
```

## Configuration Examples

### Basic Configuration

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
    apiEndpoint: 'https://api.arcaai.com',
    websocketUrl: 'wss://api.arcaai.com',
    credentials: {
        apiKey: 'your-api-key-here',
    },
};

// Usage
const [sendTextData] = useArcaTextToSpeech({
    apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
    options: SDK_CONFIG_OPTIONS,
});
```

### Advanced Configuration

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
    apiEndpoint: 'https://api.arcaai.com',
    websocketUrl: 'wss://api.arcaai.com',
    credentials: {
        apiKey: 'your-api-key-here',
    },
    // TTS-specific settings
    ttsSettings: {
        defaultLanguage: 'en',
        defaultVoice: 'en-US-JennyNeural',
        defaultRate: 1.0,
        defaultStyle: 'neutral',
        maxTextLength: 1000,
    },
    logging: {
        level: 'info',
        console: true,
    },
};

// Usage with advanced settings
const [sendTextData] = useArcaTextToSpeech({
    apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
    language: SDK_CONFIG_OPTIONS.ttsSettings?.defaultLanguage || 'en',
    options: SDK_CONFIG_OPTIONS,
});
```

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Verify API endpoint in `SDK_CONFIG_OPTIONS`
   - Check API key is valid
   - Ensure WebSocket connectivity

2. **Audio Quality Issues**
   - Check voice selection for language
   - Verify text length limits
   - Test with different speech rates

3. **Language Not Supported**
   - Confirm language code is supported
   - Check voice availability for language
   - Verify API key has language permissions

### Debug Configuration

```typescript
// src/config/sdk-config.ts (debug mode)
export const SDK_CONFIG_OPTIONS = {
    apiEndpoint: 'https://api.arcaai.com',
    websocketUrl: 'wss://api.arcaai.com',
    credentials: {
        apiKey: 'your-api-key-here',
    },
    logging: {
        level: 'debug',
        console: true,
    },
    // Enable TTS debugging
    debug: true,
};
```

## Examples

- **[Complete Examples](./examples/README.md)** - Working code examples and demos