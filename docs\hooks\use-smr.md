# useSMR

The `useSMR` hook provides access to the Medical Summary and Reporting (SMR) service, enabling synchronous generation of detailed clinical summaries from unstructured text.

## Overview

This hook interacts with a RESTful API to submit text for summarization and retrieve structured medical summaries. It abstracts the complexity of API calls, request payload formatting, and state management for loading and errors.

## Import

```typescript
import { useSMR } from '@arcaai/agentic-sdk/react';
```

## Basic Usage

For immediate results, use the `summarize` or `summarizeSync` function.

```typescript
import React, { useState } from "react";
import { useSMR } from "@arcaai/agentic-sdk/react";
import type { SummaryResponse, ErrorInfo } from "@arcaai/agentic-sdk";

function SyncSummaryDemo() {
  const [summary, setSummary] = useState<SummaryResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { summarize, error } = useSMR({
    options: {
      apiEndpoint: "https://api.taphuynh.dev/api/smr",
      credentials: { apiKey: "your-api-key" },
    },
    onComplete: (result) => {
      console.log("Summary complete:", result);
      setSummary(result);
    },
    onError: (err: ErrorInfo) => {
      console.error("SMR Error:", err);
    },
  });

  const handleSummarize = async () => {
    setIsLoading(true);
    const consultationText = "The patient reports chest pain and shortness of breath. History of hypertension.";
    try {
      await summarize({ text: consultationText });
    } catch (e) {
      // Error is already handled by the onError callback
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <h2>Medical Summary Demo</h2>
      <button onClick={handleSummarize} disabled={isLoading}>
        {isLoading ? "Generating..." : "Generate Summary"}
      </button>

      {error && <div style={{ color: "red" }}>Error: {error}</div>}

      {summary && (
        <div>
          <h3>Summary Results:</h3>
          <pre>{JSON.stringify(summary.summary, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}
```

## API Reference

### Parameters

The hook accepts a configuration object with the following properties:

#### `UseSMROptions`

| Property      | Type                                 | Required | Description                                                                |
| ------------- | ------------------------------------ | -------- | -------------------------------------------------------------------------- |
| `apiEndpoint` | `string`                             | ❌       | Base URL for the SMR service. Defaults to `https://api.taphuynh.dev/api/smr`. |
| `sessionId`   | `string`                             | ❌       | A unique identifier for the session.                                       |
| `options`     | `Partial<SDKConfig>`                 | ❌       | General SDK configuration, including `credentials` for the API key.        |
| `onComplete`  | `(summary: SummaryResponse) => void` | ❌       | Callback for when a synchronous summary is successfully generated.         |
| `onError`     | `(error: ErrorInfo) => void`         | ❌       | Global error handler for any operation within the hook.                    |

### Return Value

The hook returns an object with state and functions:

#### `UseSMRReturn`

| Property          | Type                                            | Description                                               |
| ----------------- | ----------------------------------------------- | --------------------------------------------------------- |
| `loading`         | `boolean`                                       | True when an API request is in progress.                  |
| `error`           | `string \| null`                                | A string message describing the last error that occurred. |
| `isConnected`     | `boolean`                                       | Always `true` for the HTTP-based SMR hook.                |
| `summarize`       | `(req: SMRRequest) => Promise<SummaryResponse>` | Triggers a synchronous summarization request.             |
| `summarizeSync`   | `(req: SMRRequest) => Promise<SummaryResponse>` | An alias for `summarize`.                                 |
| `getProviders`    | `() => Promise<string[]>`                       | Fetches the available AI providers from the SMR service.  |
| `getModels`       | `() => Promise<string[]>`                       | Fetches the available AI models from the SMR service.     |
| `getHealthStatus` | `() => Promise<any>`                            | Checks the health status of the SMR service.              |

### Request Object

The `summarize` and `summarizeSync` functions accept a request object:

#### `SMRRequest`

| Property      | Type                  | Required | Description                                                      |
| ------------- | --------------------- | -------- | ---------------------------------------------------------------- |
| `text`        | `string`              | ✅       | The unstructured medical text to be summarized.                  |
| `sessionId`   | `string`              | ❌       | Overrides the `sessionId` from the hook's initial configuration. |
| `language`    | `string`              | ❌       | The language of the input text (e.g., 'en'). Defaults to 'en'.   |
| `template`    | `string`              | ❌       | The summarization template to use. Defaults to 'medical'.        |
| `patientId`   | `string`              | ❌       | An identifier for the patient.                                   |
| `patientName` | `string`              | ❌       | The name of the patient.                                         |

## Data Structures

The hook uses rich data structures to represent medical summaries. The full interfaces are defined in the SDK, but key structures are detailed below for convenience.

### `SummaryResponse`

This object is the main output for a successful summarization.

```typescript
interface SummaryResponse {
    session_id: string;
    summary: MedicalSummary; // This can be EnhancedMedicalSummary or a simplified version
    created_at: string;
    processing_time_ms: number;
    token_usage?: Record<string, number>;
    llm_provider: string;
    model_name: string;
    confidence_score?: number;
    metadata?: Record<string, any>;
}
```

### `EnhancedMedicalSummary`

This is the primary, detailed interface for the summary, divided into logical clinical sections. Key fields include `encounter_summary`, `clinical_findings`, `clinical_assessment`, `treatment_plan`, and `quality_metrics`.

## Advanced Usage

### Checking Health Status

Before making a request, you can check if the SMR service is online.

```typescript
const { getHealthStatus } = useSMR({ ... });

const handleCheckHealth = async () => {
    try {
        const health = await getHealthStatus();
        console.log("Service is available:", health);
        alert("SMR Service is online!");
    } catch (err) {
        console.error("Health check failed:", err);
        alert("SMR Service is currently unavailable.");
    }
};
```

## Error Handling

Errors are communicated through two mechanisms:

1.  **`onError` Callback**: A centralized callback for handling all errors thrown by the hook's functions. This is the recommended approach for logging and displaying user-facing error messages.
2.  **`error` State Variable**: This state holds the message of the most recent error, which can be used to conditionally render UI elements.
3.  **Thrown Promises**: Each async function will throw an error on failure, allowing for local `try...catch` blocks.

```typescript
// Example of using local try...catch
const checkHealth = async () => {
    try {
        const status = await getHealthStatus();
        console.log('Service is healthy:', status);
    } catch (e) {
        // This error will also be sent to the global onError callback
        console.error('Failed to get health status:', e.message);
    }
};
```

## Error Codes

The `onError` callback provides an `ErrorInfo` object. The `code` property can be used to handle specific failure scenarios.

| Error Code                  | Description                                              | Suggested Action                                              |
| --------------------------- | -------------------------------------------------------- | ------------------------------------------------------------- |
| `SUMMARIZATION_ERROR`       | The synchronous summarization request failed.            | Check the error message for details. Verify input text.       |
| `GET_PROVIDERS_FAILED`      | Failed to fetch the list of available providers.         | Check SMR service health and configuration.                   |
| `GET_MODELS_FAILED`         | Failed to fetch the list of available models.            | Check SMR service health and configuration.                   |
| `HEALTH_CHECK_FAILED`       | The health check request failed.                         | The SMR service may be down or the endpoint is incorrect.    |

## Troubleshooting

### Common Issues

1.  **401 Unauthorized Error**

    - **Cause**: The API key is missing, invalid, or expired.
    - **Solution**: Ensure the `credentials.apiKey` is correctly passed in the `options` prop. Verify the key is valid with your service administrator.

2.  **404 Not Found Error**

    - **Cause**: The `apiEndpoint` is incorrect, or the specific path (e.g., `/summary/sync`) does not exist.
    - **Solution**: Double-check the `apiEndpoint` URL. Ensure it points to the base path of the SMR API (e.g., `https://your-api-endpoint.com/api/smr`).

3.  **Poor Summary Quality**

    - **Cause**: The input text may be too short, ambiguous, or lack clinical context. The selected `model` or `template` may also not be optimal.
    - **Solution**: Provide more detailed input text. Experiment with different models or templates if available.

4.  **CORS Errors**
    - **Cause**: The SMR service has not been configured to allow requests from the domain where your web application is hosted.
    - **Solution**: The server administrator must update the Cross-Origin Resource Sharing (CORS) policy on the SMR service to include your application's origin.

## Best Practices

- **Centralize API Configuration**: Set the `apiEndpoint` and `credentials` in a single, top-level component (e.g., in a context provider) to ensure all instances of the hook use the same configuration.
- **Secure the API Key**: Never hardcode the API key directly in the source code. Use environment variables or a secure key management service to provide the key at runtime.
