# Agentic Audio SDK Examples

This directory contains interactive examples demonstrating the capabilities of the Agentic Audio SDK. The examples are built with React + TypeScript + Vite and provide a hands-on way to explore the SDK's features.

## Available Examples

### 1. STT Demo (Beginner)
A basic demonstration of the `useArcaSpeechToText` and `useAudioCapture` hooks working together for real-time speech-to-text functionality.

**Features:**
- Real-time audio capture from microphone
- Speech-to-text transcription using Arca AI services
- Live transcript display with line breaks
- Audio recording controls (start/stop)
- Device status monitoring and permission checking
- Error handling and status indicators
- Connection status monitoring

**Components Used:**
- `useArcaSpeechToText` - Handles WebSocket connection to STT service
- `useAudioCapture` - Manages microphone access and audio data streaming

## Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start the development server:**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   Navigate to `http://localhost:5173` to see the example.

## Prerequisites

- Node.js 16+
- Modern browser with WebRTC support
- Microphone access permissions
- STT service running on:
  - API Endpoint: `http://localhost:3000`
  - WebSocket URL: `ws://localhost:3000`

## Configuration

The STT demo is configured with the following default settings:

```typescript
{
  sessionId: '123',
  language: 'en-US',
  options: {
    apiEndpoint: 'http://localhost:3000',
    websocketUrl: 'ws://localhost:3000',
  }
}
```

You can modify these settings in `src/stt-demo.tsx` to connect to your own STT service endpoints.

## Project Structure

```
src/
├── App.tsx          # Main application with example navigation
├── stt-demo.tsx     # STT demonstration component
├── utils.ts         # Utility functions
├── main.tsx         # React app entry point
└── index.css        # Global styles
```

## Development

This project uses Vite for development with React and TypeScript. The setup includes hot module replacement (HMR) and ESLint for code quality.
