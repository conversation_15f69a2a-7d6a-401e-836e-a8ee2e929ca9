import { EventEmitter } from 'eventemitter3';
import type {
    MedicalSession,
    SessionStorageConfig,
    EncryptedSessionData,
    StorageQuota,
} from '../types/index';

export class SessionStorageManager extends EventEmitter {
    private config: SessionStorageConfig | null = null;
    private db: IDBDatabase | null = null;
    private readonly dbName = 'AgenticSDK_Sessions';
    private readonly dbVersion = 1;
    private fallbackToLocalStorage = false;

    public async initialize(config: SessionStorageConfig): Promise<void> {
        this.config = config;
        
        try {
            console.log('Initializing SessionStorageManager...');
            await this.openDatabase();
            console.log('IndexedDB initialized successfully');
        } catch (error) {
            console.warn('IndexedDB failed, falling back to localStorage:', error);
            this.fallbackToLocalStorage = true;
        }
    }

    public async storeSession(session: MedicalSession): Promise<void> {
        if (!this.config) {
            throw new Error('Storage manager not initialized');
        }

        try {
            console.log('Storing session:', session.id);
            
            if (this.fallbackToLocalStorage) {
                return await this.storeSessionInLocalStorage(session);
            }

            if (!this.db) {
                throw new Error('Database not available');
            }

            const encryptedData = await this.encryptSession(session);

            const transaction = this.db.transaction(['sessions'], 'readwrite');
            const store = transaction.objectStore('sessions');

            await this.promisifyRequest(store.put(encryptedData));
            console.log('Session stored successfully in IndexedDB');
        } catch (error) {
            console.error('Failed to store session in IndexedDB, trying localStorage:', error);
            // Fallback to localStorage if IndexedDB fails
            await this.storeSessionInLocalStorage(session);
        }
    }

    private async storeSessionInLocalStorage(session: MedicalSession): Promise<void> {
        try {
            const sessionData = {
                sessionId: session.id,
                data: JSON.stringify(session),
                timestamp: Date.now(),
                size: JSON.stringify(session).length,
            };

            localStorage.setItem(`AgenticSDK_Session_${session.id}`, JSON.stringify(sessionData));
            console.log('Session stored successfully in localStorage');
        } catch (error) {
            throw new Error(`Failed to store session in localStorage: ${error}`);
        }
    }

    public async getSession(sessionId: string): Promise<MedicalSession | null> {
        if (!this.config) {
            throw new Error('Storage manager not initialized');
        }

        try {
            if (this.fallbackToLocalStorage) {
                return await this.getSessionFromLocalStorage(sessionId);
            }

            if (!this.db) {
                throw new Error('Database not available');
            }

            const transaction = this.db.transaction(['sessions'], 'readonly');
            const store = transaction.objectStore('sessions');

            const encryptedData = await this.promisifyRequest(store.get(sessionId)) as EncryptedSessionData;

            if (!encryptedData) {
                return null;
            }

            return await this.decryptSession(encryptedData);
        } catch (error) {
            console.warn('Failed to get session from IndexedDB, trying localStorage:', error);
            return await this.getSessionFromLocalStorage(sessionId);
        }
    }

    private async getSessionFromLocalStorage(sessionId: string): Promise<MedicalSession | null> {
        try {
            const sessionData = localStorage.getItem(`AgenticSDK_Session_${sessionId}`);
            if (!sessionData) {
                return null;
            }

            const parsed = JSON.parse(sessionData);
            return JSON.parse(parsed.data);
        } catch (error) {
            console.error('Failed to get session from localStorage:', error);
            return null;
        }
    }

    public async createBackup(session: MedicalSession): Promise<string> {
        const backupId = `backup_${session.id}_${Date.now()}`;

        try {
            const backupSession = { ...session, id: backupId };
            await this.storeSession(backupSession);
            return backupId;
        } catch (error) {
            throw new Error(`Failed to create backup: ${error}`);
        }
    }

    public async listSessions(): Promise<string[]> {
        try {
            if (this.fallbackToLocalStorage) {
                return this.listSessionsFromLocalStorage();
            }

            if (!this.db) {
                throw new Error('Database not available');
            }

            const transaction = this.db.transaction(['sessions'], 'readonly');
            const store = transaction.objectStore('sessions');

            const keys = await this.promisifyRequest(store.getAllKeys()) as string[];
            return keys;
        } catch (error) {
            console.warn('Failed to list sessions from IndexedDB, trying localStorage:', error);
            return this.listSessionsFromLocalStorage();
        }
    }

    private listSessionsFromLocalStorage(): string[] {
        const keys: string[] = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('AgenticSDK_Session_')) {
                const sessionId = key.replace('AgenticSDK_Session_', '');
                keys.push(sessionId);
            }
        }
        return keys;
    }

    public async deleteSession(sessionId: string): Promise<boolean> {
        if (!this.config) {
            throw new Error('Storage manager not initialized');
        }

        try {
            console.log('Deleting session:', sessionId);
            
            if (this.fallbackToLocalStorage) {
                return this.deleteSessionFromLocalStorage(sessionId);
            }

            if (!this.db) {
                throw new Error('Database not available');
            }

            const transaction = this.db.transaction(['sessions'], 'readwrite');
            const store = transaction.objectStore('sessions');

            // Check if session exists first
            const existingSession = await this.promisifyRequest(store.get(sessionId));
            if (!existingSession) {
                console.log('Session not found for deletion:', sessionId);
                return false;
            }

            // Delete the session
            await this.promisifyRequest(store.delete(sessionId));
            console.log('Session deleted successfully from IndexedDB:', sessionId);
            return true;

        } catch (error) {
            console.warn('Failed to delete session from IndexedDB, trying localStorage:', error);
            return this.deleteSessionFromLocalStorage(sessionId);
        }
    }

    private deleteSessionFromLocalStorage(sessionId: string): boolean {
        try {
            const key = `AgenticSDK_Session_${sessionId}`;
            const sessionData = localStorage.getItem(key);
            
            if (!sessionData) {
                console.log('Session not found in localStorage for deletion:', sessionId);
                return false;
            }

            localStorage.removeItem(key);
            console.log('Session deleted successfully from localStorage:', sessionId);
            return true;
        } catch (error) {
            console.error('Failed to delete session from localStorage:', error);
            return false;
        }
    }

    public async getStorageQuota(): Promise<StorageQuota> {
        try {
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                const estimate = await navigator.storage.estimate();
                const total = estimate.quota || 0;
                const used = estimate.usage || 0;
                const available = total - used;
                const percentage = total > 0 ? (used / total) * 100 : 0;

                return { total, used, available, percentage };
            }

            // Fallback for browsers without storage estimate
            return { total: 0, used: 0, available: 0, percentage: 0 };
        } catch (error) {
            throw new Error(`Failed to get storage quota: ${error}`);
        }
    }

    public async cleanup(): Promise<{ removedSessions: number; freedSpace: number }> {
        if (!this.config) {
            throw new Error('Storage manager not initialized');
        }

        try {
            if (this.fallbackToLocalStorage) {
                return this.cleanupLocalStorage();
            }

            if (!this.db) {
                throw new Error('Database not available');
            }

            const transaction = this.db.transaction(['sessions'], 'readwrite');
            const store = transaction.objectStore('sessions');

            const allData = await this.promisifyRequest(store.getAll()) as EncryptedSessionData[];
            const cutoffTime = Date.now() - this.config.retentionPeriod;

            let removedSessions = 0;
            let freedSpace = 0;

            for (const sessionData of allData) {
                if (sessionData.timestamp < cutoffTime) {
                    await this.promisifyRequest(store.delete(sessionData.sessionId));
                    removedSessions++;
                    freedSpace += sessionData.size;
                }
            }

            if (removedSessions > 0) {
                this.emit('storage-warning', {
                    message: `Cleaned up ${removedSessions} expired sessions`,
                    removedSessions,
                    freedSpace
                });
            }

            return { removedSessions, freedSpace };
        } catch (error) {
            console.warn('Failed to cleanup IndexedDB, trying localStorage:', error);
            return this.cleanupLocalStorage();
        }
    }

    private cleanupLocalStorage(): { removedSessions: number; freedSpace: number } {
        if (!this.config) {
            return { removedSessions: 0, freedSpace: 0 };
        }

        const cutoffTime = Date.now() - this.config.retentionPeriod;
        let removedSessions = 0;
        let freedSpace = 0;

        const keysToRemove: string[] = [];

        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('AgenticSDK_Session_')) {
                try {
                    const sessionData = localStorage.getItem(key);
                    if (sessionData) {
                        const parsed = JSON.parse(sessionData);
                        if (parsed.timestamp < cutoffTime) {
                            keysToRemove.push(key);
                            freedSpace += sessionData.length;
                        }
                    }
                } catch (error) {
                    // Remove corrupted entries
                    keysToRemove.push(key);
                }
            }
        }

        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
            removedSessions++;
        });

        return { removedSessions, freedSpace };
    }

    private async openDatabase(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!('indexedDB' in window)) {
                reject(new Error('IndexedDB not supported'));
                return;
            }

            console.log('Opening IndexedDB database...');
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = (event) => {
                console.error('IndexedDB open error:', event);
                reject(new Error('Failed to open database'));
            };
            
            request.onsuccess = (event) => {
                console.log('IndexedDB opened successfully');
                this.db = (event.target as IDBOpenDBRequest).result;
                resolve();
            };

            request.onupgradeneeded = (event) => {
                console.log('IndexedDB upgrade needed');
                const db = (event.target as IDBOpenDBRequest).result;

                // Create sessions object store
                if (!db.objectStoreNames.contains('sessions')) {
                    const sessionsStore = db.createObjectStore('sessions', { keyPath: 'sessionId' });
                    sessionsStore.createIndex('timestamp', 'timestamp', { unique: false });
                    console.log('Created sessions object store');
                }

                // Create audio chunks object store
                if (!db.objectStoreNames.contains('audioChunks')) {
                    const audioStore = db.createObjectStore('audioChunks', { keyPath: 'id' });
                    audioStore.createIndex('sessionId', 'sessionId', { unique: false });
                    console.log('Created audioChunks object store');
                }

                // Create transcripts object store
                if (!db.objectStoreNames.contains('transcripts')) {
                    const transcriptStore = db.createObjectStore('transcripts', { keyPath: 'id' });
                    transcriptStore.createIndex('sessionId', 'sessionId', { unique: false });
                    console.log('Created transcripts object store');
                }
            };

            request.onblocked = (event) => {
                console.warn('IndexedDB blocked:', event);
                reject(new Error('Database blocked'));
            };
        });
    }

    private async encryptSession(session: MedicalSession): Promise<EncryptedSessionData> {
        if (!this.config) {
            throw new Error('No encryption configuration');
        }

        const sessionData = JSON.stringify(session);

        try {
            // Try Web Crypto API first
            if (typeof crypto !== 'undefined' && crypto.subtle) {
                const encoder = new TextEncoder();
                const data = encoder.encode(sessionData);

                const iv = crypto.getRandomValues(new Uint8Array(12));

                const key = await crypto.subtle.importKey(
                    'raw',
                    encoder.encode(this.config.encryptionKey),
                    { name: 'AES-GCM' },
                    false,
                    ['encrypt']
                );

                const encryptedBuffer = await crypto.subtle.encrypt(
                    { name: 'AES-GCM', iv },
                    key,
                    data
                );

                const checksum = await this.calculateChecksum(sessionData);

                return {
                    sessionId: session.id,
                    encryptedData: Array.from(new Uint8Array(encryptedBuffer)).map(b => b.toString(16).padStart(2, '0')).join(''),
                    iv: Array.from(iv).map(b => b.toString(16).padStart(2, '0')).join(''),
                    checksum,
                    timestamp: Date.now(),
                    version: session.version,
                    size: sessionData.length,
                };
            }
        } catch (error) {
            console.warn('Web Crypto API encryption failed, using fallback:', error);
        }

        // Fallback: Base64 encoding
        console.log('Using fallback encryption (base64 encoding)');
        const checksum = await this.calculateChecksum(sessionData);
        const encodedData = btoa(sessionData); // Base64 encode

        return {
            sessionId: session.id,
            encryptedData: encodedData,
            iv: 'fallback', // Indicator that this is fallback encoding
            checksum,
            timestamp: Date.now(),
            version: session.version,
            size: sessionData.length,
        };
    }

    private async decryptSession(encryptedData: EncryptedSessionData): Promise<MedicalSession> {
        if (!this.config) {
            throw new Error('No encryption configuration');
        }

        try {
            // Check if this is fallback encoding
            if (encryptedData.iv === 'fallback') {
                console.log('Decrypting using fallback method (base64)');
                const sessionData = atob(encryptedData.encryptedData); // Base64 decode

                const calculatedChecksum = await this.calculateChecksum(sessionData);
                if (calculatedChecksum !== encryptedData.checksum) {
                    console.warn('Session checksum mismatch, data may be corrupted');
                }

                return JSON.parse(sessionData);
            }

            // Try Web Crypto API decryption
            if (typeof crypto !== 'undefined' && crypto.subtle) {
                const encoder = new TextEncoder();
                const decoder = new TextDecoder();

                // Convert hex strings back to Uint8Array
                const encryptedArray = new Uint8Array(encryptedData.encryptedData.match(/.{2}/g)!.map(byte => parseInt(byte, 16)));
                const iv = new Uint8Array(encryptedData.iv.match(/.{2}/g)!.map(byte => parseInt(byte, 16)));

                // Import the decryption key
                const key = await crypto.subtle.importKey(
                    'raw',
                    encoder.encode(this.config.encryptionKey),
                    { name: 'AES-GCM' },
                    false,
                    ['decrypt']
                );

                // Decrypt the data
                const decryptedBuffer = await crypto.subtle.decrypt(
                    { name: 'AES-GCM', iv },
                    key,
                    encryptedArray
                );

                const sessionData = decoder.decode(decryptedBuffer);

                // Verify checksum
                const calculatedChecksum = await this.calculateChecksum(sessionData);
                if (calculatedChecksum !== encryptedData.checksum) {
                    console.warn('Session checksum mismatch, data may be corrupted');
                }

                return JSON.parse(sessionData);
            }
        } catch (error) {
            console.error('Decryption failed:', error);
            throw new Error(`Failed to decrypt session: ${error}`);
        }

        throw new Error('No decryption method available');
    }

    private async calculateChecksum(data: string): Promise<string> {
        try {
            // Try Web Crypto API first
            if (typeof crypto !== 'undefined' && crypto.subtle) {
                const encoder = new TextEncoder();
                const dataBuffer = encoder.encode(data);
                const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
            }
        } catch (error) {
            console.warn('Web Crypto API checksum failed, using fallback:', error);
        }

        // Fallback: Simple hash algorithm
        return this.simpleHash(data);
    }

    private simpleHash(str: string): string {
        let hash = 0;
        if (str.length === 0) return hash.toString(16);
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        
        return Math.abs(hash).toString(16).padStart(8, '0');
    }

    private promisifyRequest<T>(request: IDBRequest<T>): Promise<T> {
        return new Promise((resolve, reject) => {
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }
}