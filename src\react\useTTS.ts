import { useEffect, useRef, useState } from 'react';
import type { SDKConfig } from '../types';

interface I_ArcaTTSOptions {
  apiEndpoint?: string;
  socketPath?: string;
  storeAudio?: boolean;
  language?: string;
  options?: Partial<SDKConfig>;
}

const LANGUAGE_VOICE_MAP: Record<string, string> = {
  'en': 'en-US-JennyNeural',
  'hi': 'hi-IN-SwaraNeural',
  'ta': 'ta-IN-PallaviNeural',
  'ml': 'ml-IN-SobhanaNeural'
};

export function useArcaTextToSpeech(props: I_ArcaTTSOptions = {}) {
    const { 
        apiEndpoint,
        socketPath,
        storeAudio,
        language,
        options,
    } = props;

    // WebSocket and audio state
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isConnected, setIsConnected] = useState(false);
    const [audioData, setAudioData] = useState<Uint8Array[]>([]);
    const [downloadUrl, setDownloadUrl] = useState<string | null>(null);

    const socketRef = useRef<WebSocket | null>(null);
    const audioContextRef = useRef<AudioContext | null>(null);
    const audioQueueRef = useRef<Uint8Array[]>([]);
    const isPlayingRef = useRef<boolean>(false);
    const audioDataRef = useRef<Uint8Array[]>([]);
    const sessionIdRef = useRef<string>(`tts-${Date.now()}-${Math.floor(Math.random() * 1000)}`);
    
    // Keep audioDataRef in sync with audioData state
    useEffect(() => {
        audioDataRef.current = audioData;
    }, [audioData]);

    // Clean up on unmount
    useEffect(() => {
        return () => {
            disconnect();
        };
    }, []);

    const connect = () => {
        try {
            if (socketRef.current && (socketRef.current.readyState === WebSocket.OPEN || socketRef.current.readyState === WebSocket.CONNECTING)) {
                console.log('WebSocket already connected or connecting');
                return;
            }
            
            console.log('🔌 Connecting to WebSocket...');

            const serverUrl = apiEndpoint?.replace('http://', 'ws://').replace('https://', 'wss://');
            
            const formattedPath = socketPath?.startsWith('/') ? socketPath : `/${socketPath}`;
            const wsUrl = `${serverUrl}${formattedPath}?sessionId=${sessionIdRef.current}&key=${options?.credentials?.apiKey}`;
            
            console.log('Connecting to WebSocket:', wsUrl);
            socketRef.current = new WebSocket(wsUrl);

            socketRef.current.onopen = () => {
                setIsConnected(true);
                setError(null);
                console.log('✅ Connected to TTS server');
            };
            
            socketRef.current.onclose = (event) => {
                setIsConnected(false);
                setLoading(false);
                console.log(`❌ Disconnected from TTS server: ${event.code} ${event.reason}`);
            };
            
            socketRef.current.onerror = (event) => {
                console.error('❌ WebSocket error:', event);
                setError('WebSocket connection error');
                setIsConnected(false);
            };
            
            socketRef.current.onmessage = async (event) => {
              try {
                  console.log('📨 Raw WebSocket message received:', {
                      dataType: typeof event.data,
                      isBlob: event.data instanceof Blob,
                      isArrayBuffer: event.data instanceof ArrayBuffer,
                      constructor: event.data?.constructor?.name,
                      size: event.data?.size || event.data?.byteLength || event.data?.length || 'unknown'
                  });

                  // Handle binary data (audio chunks) - prioritize binary handling
                  if (event.data instanceof Blob) {
                      console.log('🎵 Received Blob data:', event.data.size, 'bytes');
                          await handleBinaryAudioChunk(event.data);
                  }
                  // Handle ArrayBuffer (raw binary)
                  else if (event.data instanceof ArrayBuffer) {
                      console.log('🎵 Received ArrayBuffer:', event.data.byteLength, 'bytes');
                          const binary = new Uint8Array(event.data);
                          await processBinaryAudioData(binary);
                  }
                  // Handle text messages (JSON)
                  else if (typeof event.data === 'string') {
                      // Check if it might be binary data encoded as string
                      if (event.data.length > 1000 && !event.data.trim().startsWith('{')) {
                          console.log('🔍 Large string data detected, might be binary:', event.data.length, 'chars');
                          // Try to decode as binary
                          try {
                              const binary = new Uint8Array(event.data.length);
                              for (let i = 0; i < event.data.length; i++) {
                                  binary[i] = event.data.charCodeAt(i);
                              }
                              console.log('🔄 Converted large string to binary:', binary.length, 'bytes');
                              await processBinaryAudioData(binary);
                          } catch (convError) {
                              console.warn('Failed to convert string to binary, treating as text');
                              await handleTextMessage(event.data);
                          }
                      } else {
                      await handleTextMessage(event.data);
                      }
                  } else {
                      console.log('🤔 Received unknown data type:', typeof event.data);
                  }
              } catch (err) {
                  console.error('❌ Failed to process WebSocket message:', err);
                  setError('Invalid server response');
                  setLoading(false);
              }
            };

        } catch (err) {
            setError('Failed to connect to server');
            console.error('WebSocket connection error:', err);
        }
    };

    const handleBinaryAudioChunk = async (blob: Blob) => {
        try {
            console.log('🎵 Received audio blob chunk:', blob.size, 'bytes');
            console.log('🔍 Blob details:', {
                size: blob.size,
                type: blob.type,
                constructor: blob.constructor.name
            });
            
            try {
                // Method 1: Try as ArrayBuffer
                const arrayBuffer = await blob.arrayBuffer();
                console.log('📦 Converted blob to ArrayBuffer:', arrayBuffer.byteLength, 'bytes');
                
                if (arrayBuffer.byteLength > 0) {
                    const binary = new Uint8Array(arrayBuffer);
                    await processBinaryAudioData(binary);
                    return;
                }
                
                // Method 2: If ArrayBuffer is empty, try reading as text
                console.log('⚠️ ArrayBuffer is empty, trying to read as text...');
                const text = await blob.text();
                console.log('📝 Blob as text length:', text.length);
                
                if (text.length > 0) {
                    const binary = new Uint8Array(text.length);
                    for (let i = 0; i < text.length; i++) {
                        binary[i] = text.charCodeAt(i);
                    }
                    await processBinaryAudioData(binary);
                    return;
                }
                
                // Method 3: Try reading as stream
                console.log('⚠️ Text is also empty, trying stream...');
                const reader = blob.stream().getReader();
                const chunks = [];
                let totalSize = 0;
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    chunks.push(value);
                    totalSize += value.length;
                }
                
                if (totalSize > 0) {
                    const binary = new Uint8Array(totalSize);
                    let offset = 0;
                    for (const chunk of chunks) {
                        binary.set(chunk, offset);
                        offset += chunk.length;
                    }
                    await processBinaryAudioData(binary);
                    return;
                }
                
                console.warn('⚠️ All methods failed - Blob appears to be truly empty');
                
            } catch (blobError) {
                console.error('❌ Error processing blob:', blobError);
            }
        } catch (error) {
            console.error('❌ Error processing binary audio chunk:', error);
        }
    };

    const processBinaryAudioData = async (binary: Uint8Array) => {
        console.log('🔢 Processing binary audio data:', {
            length: binary.length,
            firstBytes: Array.from(binary.slice(0, 10))
        });
        
        if (storeAudio) {
            const bufferCopy = new Uint8Array(binary.length);
            bufferCopy.set(binary, 0);
            console.log('💾 Storing audio chunk:', bufferCopy.length, 'bytes');
            setAudioData(prev => {
                const newData = [...prev, bufferCopy];
                console.log('Updated audioData length:', newData.length);
                return newData;
            });
        }
        
        try {
            const blob = new Blob([binary], { type: 'audio/mpeg' });
            const url = URL.createObjectURL(blob);
            
            const audio = new Audio();
            
            audio.oncanplaythrough = () => {
                console.log('✅ Audio can play through');
                audio.play().catch(err => {
                    console.error('❌ Failed to play audio:', err);
                });
            };
            
            audio.onended = () => {
                console.log('✅ Audio playback completed');
                URL.revokeObjectURL(url);
            };
            
            audio.onerror = (error) => {
                console.error('❌ Audio playback error:', error);
                URL.revokeObjectURL(url);
            };
            
            audio.onloadeddata = () => {
                console.log('✅ Audio data loaded');
            };
            
            audio.src = url;
            audio.load();
            console.log('🔊 Loading audio for playback');
        } catch (err) {
            console.error('❌ Failed to process audio chunk:', err);
        }
    };

    const handleTextMessage = async (data: string) => {
        try {
            const message = JSON.parse(data);
            console.log('📝 Received JSON message:', message);
            
            if (message.event) {
                await handleAPIGatewayMessage(message);
            }
            else if (message.type) {
                await handleDirectTTSMessage(message);
            }
            else {
                console.log('❓ Unknown message format:', message);
            }
        } catch (parseError) {
            console.log('📝 Received non-JSON string data:', data);
            // Try to handle as potential binary data - from working version
            if (data.length > 0) {
                try {
                    const binaryString = atob(data);
                    const binary = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        binary[i] = binaryString.charCodeAt(i);
                    }
                    console.log('🔄 Decoded base64 string to binary:', binary.length, 'bytes');
                    await processBinaryAudioData(binary);
                } catch (base64Error) {
                    console.warn('Failed to decode as base64, treating as raw string');
                    const binary = new Uint8Array(data.length);
                    for (let i = 0; i < data.length; i++) {
                        binary[i] = data.charCodeAt(i);
                    }
                    console.log('🔄 Converted string to binary:', binary.length, 'bytes');
                    await processBinaryAudioData(binary);
                }
            }
        }
    };

    const handleDirectTTSMessage = async (message: any) => {
        switch(message.type) {
            case 'synthesis_start':
                console.log('🎬 Synthesis started:', message);
                setLoading(true);
                if (storeAudio) {
                    setAudioData([]);
                }
                audioQueueRef.current = [];
                break;
                
            case 'synthesis_complete':
                console.log('✅ TTS synthesis complete:', message);
                setLoading(false);
                break;
                
            case 'error':
                console.error('❌ TTS server error:', message.message);
                setError(message.message || 'TTS server error');
                setLoading(false);
                break;
                
            case 'echo':
            case 'pong':
                console.log('📡 TTS server message:', message);
                break;
                
            default:
                console.log('❓ Unknown TTS server message type:', message);
        }
    };

    const handleAPIGatewayMessage = async (message: any) => {
        const eventType = message.event;
        const data = message.data || message;
        
        console.log('📨 Processing API Gateway message:', { eventType, data });
        
        switch (eventType) {
            case 'synthesis_start':
                console.log('🎬 Synthesis started:', data);
                setLoading(true);
                if (storeAudio) {
                    setAudioData([]);
                }
                break;
                
            case 'audio_chunk':
                console.log('🎵 Received audio chunk:', {
                    chunkIndex: data.chunk_index,
                    chunkSize: data.chunk?.length || 0,
                    format: data.format || 'unknown'
                });
                if (data.chunk) {
                    const binary = hexToUint8Array(data.chunk);
                    await processBinaryAudioData(binary);
                }
                break;
                
            case 'synthesis_complete':
                console.log('✅ Synthesis complete - Received data:', data);
                setLoading(false);
                
                if (data.download_url) {
                    console.log('🔗 Setting download URL:', data.download_url);
                    setDownloadUrl(data.download_url);
                    console.log('💾 Download URL set for manual download');
                } else {
                    console.warn('⚠️ No download_url in synthesis_complete event');
                }
                break;
                
            case 'error':
                console.error('❌ TTS Error:', data.message || 'Unknown error');
                setError(data.message || 'TTS error occurred');
                setLoading(false);
                break;
                
            case 'external-disconnected':
                console.warn('⚠️ TTS service disconnected:', data);
                setError('TTS service temporarily unavailable');
                setLoading(false);
                break;
                
            default:
                console.log('❓ Unknown event:', eventType, data);
                break;
        }
    };

    const disconnect = () => {
        if (socketRef.current) {
            socketRef.current.close();
            socketRef.current = null;
        }
        
        if (audioContextRef.current) {
            audioContextRef.current.close();
            audioContextRef.current = null;
        }
    };

    const hexToUint8Array = (hexString: string): Uint8Array => {
        const bytes = new Uint8Array(hexString.length / 2);
        for (let i = 0; i < hexString.length; i += 2) {
            bytes[i / 2] = parseInt(hexString.substring(i, i + 2), 16);
        }
        return bytes;
    };

    const playNextChunk = async () => {
        if (!audioQueueRef.current.length) {
            isPlayingRef.current = false;
            return;
        }

        isPlayingRef.current = true;

        try {
            if (!audioContextRef.current) {
                audioContextRef.current = new AudioContext();
            }

            const chunk = audioQueueRef.current.shift();
            if (!chunk) return;

            const audioBuffer = await audioContextRef.current.decodeAudioData(chunk.buffer.slice(0));
            const source = audioContextRef.current.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(audioContextRef.current.destination);
            
            source.onended = () => {
                playNextChunk();
            };
            
            source.start();
        } catch (err) {
            console.error('❌ Audio playback error:', err);
            
            try {
                const chunk = audioQueueRef.current.shift();
                if (chunk) {
                    const blob = new Blob([chunk], { type: 'audio/wav' }); // Changed to WAV
                    const audioUrl = URL.createObjectURL(blob);
                    const audio = new Audio(audioUrl);
                    
                    audio.onended = () => {
                        URL.revokeObjectURL(audioUrl);
                        playNextChunk();
                    };
                    
                    audio.onerror = () => {
                        URL.revokeObjectURL(audioUrl);
                        console.error('❌ HTML5 Audio fallback failed');
                        isPlayingRef.current = false;
                    };
                    
                    await audio.play();
                }
            } catch (fallbackError) {
                console.error('❌ All audio playback methods failed:', fallbackError);
                setError('Failed to play audio');
                isPlayingRef.current = false;
            }
        }
    };

    const sendTextData = (text: string, options: {
        voice?: string;
        rate?: number;
        pitch?: string;
        style?: string;
        styleLevel?: number;
    } = {}) => {
        if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
            console.error('❌ WebSocket not connected');
            setError('WebSocket not connected');
            return;
        }
        
        // Reset download URL when starting a new synthesis
        setDownloadUrl(null);

        try {
            setError(null);
            setLoading(true);
            
            let voice = options.voice;
            if (!voice) {
                voice = LANGUAGE_VOICE_MAP[language || 'en'] || LANGUAGE_VOICE_MAP['en'];
            }
            
            // Clear previous audio data
            if (storeAudio) {
                setAudioData([]);
            }
            audioQueueRef.current = [];
            
            const message = {
                event: 'synthesize',
                sessionId: sessionIdRef.current,
                data: {
                    text: text,
                    voice: voice,
                    rate: options.rate || 1.0,
                    pitch: options.pitch || 'default',
                    style: options.style || null,
                    style_degree: options.styleLevel || 1.0,
                    stream: true // Request streaming audio
                }
            };
            
            socketRef.current.send(JSON.stringify(message));
            console.log('📤 Sent synthesis request:', message);
        } catch (err) {
            setError('Failed to send synthesis request');
            setLoading(false);
            console.error('TTS synthesis error:', err);
        }
    };

    const downloadAudio = async () => {
        console.log('📥 downloadAudio called, downloadUrl:', downloadUrl);
        
        // Priority 1: Use server-provided download URL
        if (downloadUrl) {
            console.log('💾 Using server download URL:', downloadUrl);
            
            try {
                // Make sure the download URL is absolute and points to the API server
                let fullDownloadUrl = downloadUrl;
                if (downloadUrl.startsWith('/')) {
                    // Relative URL - make it absolute by adding the API endpoint
                    fullDownloadUrl = `${apiEndpoint}${downloadUrl}`;
                }
                
                console.log('🔗 Full download URL:', fullDownloadUrl);
                
                // Try to download from server first
                const response = await fetch(fullDownloadUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'audio/mpeg,audio/*;q=0.9,*/*;q=0.8',
                        ...(options?.credentials?.apiKey && { 'x-api-key': options.credentials.apiKey })
                    }
                });
                
                if (response.ok) {
                    const audioBlob = await response.blob();
                    if (audioBlob.size > 0) {
                        console.log('✅ Downloaded from server:', audioBlob.size, 'bytes');
                        const url = URL.createObjectURL(audioBlob);
                        return url;
                    }
                }
                
                console.warn('⚠️ Server download failed, falling back to client-side blob');
            } catch (serverError) {
                console.warn('⚠️ Server download error, falling back to client-side blob:', serverError);
            }
        }
        
        // Priority 2: Create blob URL from stored audio data
        if (audioData && audioData.length > 0) {
            try {
                console.log('💾 Creating blob URL from stored audio data:', audioData.length, 'chunks');
                
                // Calculate total size
                const totalSize = audioData.reduce((total, chunk) => total + chunk.length, 0);
                console.log('📊 Total audio size:', totalSize, 'bytes');
                
                // Create a single continuous buffer
                const combinedBuffer = new Uint8Array(totalSize);
                let offset = 0;
                
                for (const chunk of audioData) {
                    combinedBuffer.set(chunk, offset);
                    offset += chunk.length;
                }
                
                console.log('🔗 Combined audio buffer created:', combinedBuffer.length, 'bytes');
                console.log('🔍 First 10 bytes:', Array.from(combinedBuffer.slice(0, 10)));
                
                // Create blob from the combined buffer
                const blob = new Blob([combinedBuffer], { type: 'audio/mpeg' });
                const blobUrl = URL.createObjectURL(blob);
                console.log('✅ Created blob URL:', blobUrl);
                return blobUrl;
            } catch (err) {
                console.error('❌ Error creating blob URL:', err);
                setError('Failed to create download URL from audio data');
                return null;
            }
        }
        
        console.log('⚠️ No download URL or audio data available');
        return null;
    };
    
    const play = () => {
        if (!isPlayingRef.current && audioQueueRef.current.length > 0) {
            playNextChunk();
        }
    };

    return [
        sendTextData,
        loading,
        error,
        connect,
        disconnect,
        isConnected,
        audioData,
        downloadAudio,
        downloadUrl,
        play
    ] as const;
}
