# Agentic SDK

A comprehensive TypeScript/JavaScript SDK for building medical conversation applications with enterprise-grade reliability, real-time speech processing, and cloud-first architecture.

## Table of Contents

- [Features](#features)
- [Quick Start](#quick-start)
- [Documentation](#documentation)
- [API Overview](#api-overview)
- [React Integration](#react-integration)
- [Support](#support)

## Features

- **Enterprise-grade reliability** - Production-ready with comprehensive error handling
- **Medical conversation focus** - Specialized for healthcare applications
- **Real-time speech processing** - Low-latency speech-to-text and text-to-speech
- **Cloud-first architecture** - Scalable and secure cloud integration
- **React hooks** - Declarative React integration with modern patterns
- **Multi-provider support** - Azure, Google Cloud, AWS speech services
- **Session management** - Complete medical session lifecycle management
- **Audio optimization** - Advanced audio processing with quality presets
- **TypeScript support** - Full type safety and excellent developer experience

## Quick Start

### Installation

```bash
npm install @arcaai/agentic-sdk
# or
pnpm add @arcaai/agentic-sdk
# or
yarn add @arcaai/agentic-sdk
```

### Basic Usage

```typescript
import { AgenticSDK } from '@arcaai/agentic-sdk';

// Initialize the SDK
const sdk = AgenticSDK.getInstance();

await sdk.initialize({
    apiEndpoint: 'https://your-api-endpoint.com',
    websocketUrl: 'wss://your-websocket-endpoint.com',
    sttProvider: 'azure',
    credentials: {
        apiKey: 'your-api-key',
    },
});

// Create and start a medical session
const session = await sdk.createMedicalSession({
    title: 'Patient Consultation',
    sessionType: 'consultation',
});

await sdk.startMedicalSession();
```

### React Usage

```tsx
import { useArcaSessionManager, useAudioCapture, useArcaSpeechToText } from '@arcaai/agentic-sdk';

function MedicalConsole() {
    const sessionManager = useArcaSessionManager({
        doctorId: 'dr-123',
        doctorName: 'Dr. Smith',
        patientId: 'pt-456',
        patientName: 'John Doe',
        options: {
            apiEndpoint: 'https://your-api.com',
            credentials: { apiKey: 'your-key' },
        },
    });

    const audioCapture = useAudioCapture({
        onAudioData: (data) => {
            // Process audio chunk
        },
    });

    const speechToText = useArcaSpeechToText({
        sessionId: sessionManager.session?.id || '',
        language: 'en',
        onTranscript: (text, isFinal) => {
            console.log('Transcript:', text);
        },
    });

    return (
        <div>
            <h2>Medical Console</h2>
            <p>Session: {sessionManager.session?.id || 'None'}</p>
            <p>Recording: {audioCapture.isRecording ? 'Active' : 'Inactive'}</p>
            <p>Transcript: {speechToText.transcript}</p>
        </div>
    );
}
```

## Examples

🚀 **[Interactive Examples](./examples/README.md)** - Live React applications demonstrating SDK features

The `examples/` directory contains fully functional React applications that showcase the SDK's capabilities:

- **STT Demo** - Real-time speech-to-text with session management
- **TTS Demo** - Text-to-speech synthesis with audio streaming

To run the examples:

```bash
cd examples
pnpm install
pnpm dev
```

## Documentation

📚 **[Complete Documentation](./docs/README.md)** - Comprehensive developer guide

### Quick Links

- **[API Reference](./docs/api-reference.md)** - Detailed API documentation
- **[React Hooks](./docs/react-hooks.md)** - Complete React integration guide
- **[Configuration](./docs/configuration.md)** - Configuration options and presets
- **[Examples](./docs/examples.md)** - Implementation examples and use cases

## API Overview

### Core Classes

- **`AgenticSDK`** - Main SDK singleton class
- **`SessionManager`** - Medical session lifecycle management
- **`AudioCaptureManager`** - Audio recording and device management
- **`WebSocketManager`** - Real-time communication

### React Hooks

- **`useArcaSessionManager`** - Complete session lifecycle management
- **`useAudioCapture`** - Audio recording and device status
- **`useArcaSpeechToText`** - Real-time speech-to-text transcription
- **`useArcaTextToSpeech`** - Text-to-speech synthesis and playback

The SDK provides React hooks for seamless integration with React applications. Each hook follows React best practices and provides declarative APIs for medical conversation functionality.

**Key React Features:**

- **Declarative APIs** - React-friendly hook patterns
- **Automatic cleanup** - Proper resource management
- **Error boundaries** - Comprehensive error handling
- **TypeScript support** - Full type safety
- **Performance optimized** - Memoized callbacks and efficient re-renders

See the [React Hooks Documentation](./docs/react-hooks.md) for complete usage examples and API reference.

## Configuration

The SDK supports flexible configuration for different environments and use cases:

```typescript
// Development configuration
const devConfig = {
    apiEndpoint: 'https://dev-api.example.com',
    environment: 'development',
    logging: { level: 'debug', console: true },
    audioSettings: { sampleRate: 16000, format: 'pcm' },
};

// Production configuration
const prodConfig = {
    apiEndpoint: 'https://api.example.com',
    environment: 'production',
    logging: { level: 'error', remote: true },
    audioSettings: { sampleRate: 48000, format: 'wav' },
};
```

**Configuration Features:**

- **Environment presets** - Development, staging, production
- **Audio quality presets** - High quality, standard, low bandwidth, real-time
- **Provider flexibility** - Azure, Google Cloud, AWS speech services
- **Security options** - Encryption, authentication, access control

See the [Configuration Documentation](./docs/configuration.md) for detailed configuration options.

## Support

- **📖 Documentation**: [./docs/README.md](./docs/README.md)
- **🐛 Issues**: [GitHub Issues](https://github.com/ArcaAI/Arca-AgenticSDK/issues)
- **💬 Discussions**: [GitHub Discussions](https://github.com/ArcaAI/Arca-AgenticSDK/discussions)

## License

MIT License - see [LICENSE](LICENSE) for details.

---

**Version**: 0.1.3
**Repository**: [GitHub](https://github.com/ArcaAI/Arca-AgenticSDK)
