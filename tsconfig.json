{"compilerOptions": {"inlineSources": false, "noUnusedLocals": false, "preserveWatchOutput": true, "outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "lib": ["ES2022", "DOM", "DOM.Iterable", "WebWorker"], "target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "useDefineForClassFields": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"]}