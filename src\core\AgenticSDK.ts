import { EventEmitter } from 'eventemitter3';
import type {
    AudioDeviceStatus,
    ConnectionStatus,
    MedicalSession,
    PerformanceMetrics,
    ProcessingStatus,
    SDKConfig,
    SDKEvents,
    SDKState,
    SessionManagerConfig,
    SessionMetadata,
} from '../types/index';
import { AudioCaptureManager } from './AudioCaptureManager';
import { CloudSTTService } from './CloudSTTService';
import { ConfigurationManager } from './ConfigurationManager';
import { ErrorHandler } from './ErrorHandler';
import { Logger } from './Logger';
import { SessionManager } from './SessionManager';
import { WebSocketManager } from './WebSocketManager';

export class AgenticSDK extends EventEmitter<SDKEvents> {
    private static instance: AgenticSDK | null = null;

    private configManager: ConfigurationManager;
    private connectionManager: WebSocketManager;
    private audioManager: AudioCaptureManager;
    private sttService: CloudSTTService;
    private errorHandler: ErrorHandler;
    private logger: Logger;
    private sessionManager: SessionManager;

    private currentState: SDKState = 'idle';
    private isInitialized = false;
    private performanceMetrics: PerformanceMetrics;

    private constructor() {
        super();

        // Initialize performance metrics
        this.performanceMetrics = {
            initializationTime: 0,
            audioCaptureStartTime: 0,
            connectionTime: 0,
            sttLatency: 0,
            memoryUsage: 0,
            cpuUsage: 0,
        };

        // Initialize core managers
        this.configManager = new ConfigurationManager();
        this.errorHandler = new ErrorHandler();
        this.logger = new Logger();
        this.connectionManager = new WebSocketManager();
        this.audioManager = new AudioCaptureManager();
        this.sttService = new CloudSTTService();
        this.sessionManager = new SessionManager();

        this.setupEventHandlers();
    }

    /**
     * Get singleton instance of AgenticSDK
     */
    public static getInstance(): AgenticSDK {
        if (!AgenticSDK.instance) {
            AgenticSDK.instance = new AgenticSDK();
        }
        return AgenticSDK.instance;
    }

    /**
     * Initialize the SDK with configuration
     */
    public async initialize(config: Partial<SDKConfig>): Promise<void> {
        const startTime = performance.now();

        try {
            this.setState('initializing');
            this.logger.info('Initializing AgenticSDK', { config });

            await this.configManager.initialize(config);
            const finalConfig = this.configManager.getConfig();

            await this.errorHandler.initialize(finalConfig.errorReporting);
            await this.logger.initialize(finalConfig.logging);
            await this.connectionManager.initialize(finalConfig);
            await this.audioManager.initialize(finalConfig.audioSettings);
            await this.sttService.initialize(finalConfig);

            const sessionConfig: SessionManagerConfig = {
                encryptionKey: finalConfig.credentials?.apiKey || '',
                maxStorageSize: 100 * 1024 * 1024, // 100MB
                compressionEnabled: true,
                backupInterval: 30000, // 30 seconds
                retentionPeriod: 24 * 60 * 60 * 1000, // 24 hours
                maxSessions: 50,
                autoCleanup: true,
                apiEndpoint: finalConfig.apiEndpoint,
            };

            if (finalConfig.credentials) {
                const credentials: any = {};
                if (finalConfig.credentials.apiKey) credentials.apiKey = finalConfig.credentials.apiKey;
                if (finalConfig.credentials.clientId) credentials.clientId = finalConfig.credentials.clientId;

                if (Object.keys(credentials).length > 0) {
                    sessionConfig.credentials = credentials;
                }
            }

            await this.sessionManager.initialize(sessionConfig);

            this.isInitialized = true;
            this.performanceMetrics.initializationTime = performance.now() - startTime;

            this.setState('idle');
            this.logger.info('AgenticSDK initialized successfully', { initTime: this.performanceMetrics.initializationTime });
        } catch (error) {
            this.setState('error');
            const errorInfo = this.errorHandler.handleError(error as Error, 'configuration');
            this.emit('error', errorInfo);
            throw error;
        }
    }

    /**
     * Connect to cloud services
     */
    public async connect(): Promise<void> {
        this.ensureInitialized();

        const startTime = performance.now();

        try {
            this.setState('connecting');
            this.logger.info('Connecting to cloud services');

            this.performanceMetrics.connectionTime = performance.now() - startTime;
            this.setState('connected');

            this.logger.info('Connected to cloud services', {
                connectionTime: this.performanceMetrics.connectionTime,
            });
        } catch (error) {
            this.setState('error');
            const errorInfo = this.errorHandler.handleError(error as Error, 'network');
            this.emit('error', errorInfo);
            throw error;
        }
    }

    /**
     * Disconnect from cloud services
     */
    public async disconnect(): Promise<void> {
        try {
            this.logger.info('Disconnecting from cloud services');

            await this.stopAudioCapture();
            await this.connectionManager.disconnect();

            this.setState('disconnected');
            this.logger.info('Disconnected from cloud services');
        } catch (error) {
            const errorInfo = this.errorHandler.handleError(error as Error, 'network');
            this.emit('error', errorInfo);
            throw error;
        }
    }

    /**
     * Start audio capture and processing
     */
    public async startAudioCapture(): Promise<void> {
        this.ensureInitialized();
        this.ensureConnected();

        const startTime = performance.now();

        try {
            this.setState('processing');
            this.logger.info('Starting audio capture');

            await this.audioManager.startCapture();

            this.performanceMetrics.audioCaptureStartTime = performance.now() - startTime;

            this.logger.info('Audio capture started', {
                startTime: this.performanceMetrics.audioCaptureStartTime,
            });
        } catch (error) {
            this.setState('error');
            const errorInfo = this.errorHandler.handleError(error as Error, 'audio');
            this.emit('error', errorInfo);
            throw error;
        }
    }

    /**
     * Stop audio capture and processing
     */
    public async stopAudioCapture(): Promise<void> {
        try {
            this.logger.info('Stopping audio capture');

            await this.audioManager.stopCapture();

            if (this.currentState === 'processing') {
                this.setState('connected');
            }

            this.logger.info('Audio capture stopped');
        } catch (error) {
            const errorInfo = this.errorHandler.handleError(error as Error, 'audio');
            this.emit('error', errorInfo);
            throw error;
        }
    }

    /**
     * Get current SDK state
     */
    public getState(): SDKState {
        return this.currentState;
    }

    /**
     * Get session manager instance
     */
    public getSessionManager(): SessionManager {
        return this.sessionManager;
    }

    /**
     * Get current medical session
     */
    public getCurrentSession(): MedicalSession | null {
        return this.sessionManager.getCurrentSession();
    }

    /**
     * Create a new medical session
     */
    public async createMedicalSession(metadata?: Partial<SessionMetadata>): Promise<MedicalSession> {
        this.ensureInitialized();
        return await this.sessionManager.createSession(metadata);
    }

    /**
     * Start the current medical session
     */
    public async startMedicalSession(): Promise<void> {
        this.ensureInitialized();
        await this.sessionManager.startSession();
    }

    /**
     * End the current medical session
     */
    public async endMedicalSession(): Promise<void> {
        this.ensureInitialized();
        await this.sessionManager.endSession();
    }

    /**
     * Get performance metrics
     */
    public getPerformanceMetrics(): PerformanceMetrics {
        return { ...this.performanceMetrics };
    }

    /**
     * Get audio device status
     */
    public async getAudioDeviceStatus(): Promise<AudioDeviceStatus> {
        return this.audioManager.getDeviceStatus();
    }

    /**
     * Get connection status
     */
    public getConnectionStatus(): ConnectionStatus {
        return this.connectionManager.getStatus();
    }

    /**
     * Get processing status
     */
    public getProcessingStatus(): ProcessingStatus {
        return this.sttService.getProcessingStatus();
    }

    /**
     * Cleanup and destroy SDK instance
     */
    public async cleanup(): Promise<void> {
        try {
            this.logger.info('Cleaning up AgenticSDK');

            await this.disconnect();
            await this.audioManager.cleanup();
            await this.connectionManager.cleanup();
            await this.sttService.cleanup();
            await this.sessionManager.cleanup();

            this.removeAllListeners();
            this.isInitialized = false;

            AgenticSDK.instance = null;

            this.logger.info('AgenticSDK cleanup completed');
        } catch (error) {
            const errorInfo = this.errorHandler.handleError(error as Error, 'processing');
            this.emit('error', errorInfo);
            throw error;
        }
    }

    private setupEventHandlers(): void {
        this.connectionManager.on('status-changed', (status: ConnectionStatus) => {
            this.emit('connection-changed', status);
        });

        this.audioManager.on('device-changed', (device: MediaDeviceInfo) => {
            this.emit('device-changed', device);
        });

        this.audioManager.on('audio-level', (level: number) => {
            this.emit('audio-level', level);
        });

        this.sttService.on('transcription', (result: any) => {
            this.emit('transcription', result);
        });

        this.sttService.on('processing-status', (status: ProcessingStatus) => {
            this.emit('processing-status', status);
        });

        this.errorHandler.on('error', (error: any) => {
            this.emit('error', error);
        });
    }

    private setState(newState: SDKState): void {
        if (this.currentState !== newState) {
            const previousState = this.currentState;
            this.currentState = newState;

            this.logger.debug('SDK state changed', {
                from: previousState,
                to: newState,
            });

            this.emit('state-changed', newState);
        }
    }

    private ensureInitialized(): void {
        if (!this.isInitialized) {
            throw new Error('SDK not initialized. Call initialize() first.');
        }
    }

    private ensureConnected(): void {
        const connectionStatus = this.connectionManager.getStatus();
        if (connectionStatus !== 'connected') {
            throw new Error('SDK not connected. Call connect() first.');
        }
    }
}
