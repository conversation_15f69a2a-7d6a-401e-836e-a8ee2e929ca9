# SDK Configuration Guide

This guide covers the centralized configuration approach for the Agentic Audio SDK, using the `SDK_CONFIG_OPTIONS` pattern for consistent and maintainable setup across your application.

## Overview

The Agentic Audio SDK uses a centralized configuration approach where all SDK settings are defined in a single `SDK_CONFIG_OPTIONS` object. This provides:

- **Single Source of Truth** - All configuration in one place
- **Environment Management** - Easy switching between environments
- **Type Safety** - Full TypeScript support
- **Consistency** - Same configuration across all hooks
- **Maintainability** - Easy updates and management

## Basic Configuration

### Creating the Configuration File

Create a configuration file in your project:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: "your-api-key-here",
  },
};
```

### Using the Configuration

Import and use the configuration in your components:

```typescript
import { useArcaSessionManager } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

function MyComponent() {
  const sessionManager = useArcaSessionManager({
    doctorId: "dr-123",
    doctorName: "Dr. Smith",
    patientId: "pt-456",
    patientName: "John Doe",
    options: SDK_CONFIG_OPTIONS,
  });

  // ... component logic
}
```

## Complete Configuration Reference

### Full Configuration Interface

```typescript
interface SDKConfig {
  // Required settings
  apiEndpoint: string;
  websocketUrl: string;
  credentials: {
    apiKey: string;
  };

  // Optional settings
  audioSettings?: AudioSettings;
  logging?: LoggingConfig;
  errorReporting?: ErrorReportingConfig;
  environment?: string;
  sttProvider?: string;
}
```

### Audio Settings

```typescript
interface AudioSettings {
  sampleRate: number; // 8000, 16000, 44100, 48000
  channels: 1 | 2; // 1=mono, 2=stereo
  bitDepth: 8 | 16 | 24 | 32; // Bit depth for audio samples
  format: "pcm" | "wav" | "mp3"; // Audio format
  chunkSize: number; // Size of audio chunks in samples
  noiseCancellation: boolean; // Enable noise cancellation
  echoCancellation: boolean; // Enable echo cancellation
  autoGainControl: boolean; // Enable automatic gain control
}
```

### Logging Configuration

```typescript
interface LoggingConfig {
  level: "debug" | "info" | "warn" | "error";
  console: boolean; // Log to browser console
  remote: boolean; // Send logs to remote server
}
```

### Error Reporting Configuration

```typescript
interface ErrorReportingConfig {
  enabled: boolean;
  includeStackTrace: boolean;
  maxErrorsPerSession: number;
}
```

## Environment-Specific Configuration

### Development vs Production

```typescript
// src/config/sdk-config.ts
const baseConfig = {
  credentials: {
    apiKey: process.env.REACT_APP_API_KEY || "your-default-api-key",
  },
  audioSettings: {
    sampleRate: 16000,
    channels: 1,
    bitDepth: 16,
    format: "pcm" as const,
    chunkSize: 1024,
    noiseCancellation: true,
    echoCancellation: true,
    autoGainControl: true,
  },
  errorReporting: {
    enabled: true,
    includeStackTrace: false,
    maxErrorsPerSession: 10,
  },
};

const developmentConfig = {
  ...baseConfig,
  apiEndpoint: "http://localhost:3333",
  websocketUrl: "ws://localhost:3333",
  logging: {
    level: "debug" as const,
    console: true,
    remote: false,
  },
  environment: "development",
  errorReporting: {
    ...baseConfig.errorReporting,
    includeStackTrace: true,
  },
};

const stagingConfig = {
  ...baseConfig,
  apiEndpoint: "https://staging-api.arcaai.com",
  websocketUrl: "wss://staging-api.arcaai.com",
  logging: {
    level: "info" as const,
    console: true,
    remote: true,
  },
  environment: "staging",
};

const productionConfig = {
  ...baseConfig,
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  logging: {
    level: "warn" as const,
    console: false,
    remote: true,
  },
  environment: "production",
};

export const SDK_CONFIG_OPTIONS = (() => {
  switch (process.env.NODE_ENV) {
    case "development":
      return developmentConfig;
    case "staging":
      return stagingConfig;
    case "production":
      return productionConfig;
    default:
      return developmentConfig;
  }
})();
```

## Environment Variables

### Setting Up Environment Variables

Create environment files for different stages:

```bash
# .env.development
REACT_APP_API_ENDPOINT=http://localhost:3333
REACT_APP_WS_ENDPOINT=ws://localhost:3333
REACT_APP_API_KEY=your-development-api-key

# .env.staging
REACT_APP_API_ENDPOINT=https://staging-api.arcaai.com
REACT_APP_WS_ENDPOINT=wss://staging-api.arcaai.com
REACT_APP_API_KEY=your-staging-api-key

# .env.production
REACT_APP_API_ENDPOINT=https://api.arcaai.com
REACT_APP_WS_ENDPOINT=wss://api.arcaai.com
REACT_APP_API_KEY=your-production-api-key
```

### Using Environment Variables in Configuration

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: process.env.REACT_APP_API_ENDPOINT || "https://api.arcaai.com",
  websocketUrl: process.env.REACT_APP_WS_ENDPOINT || "wss://api.arcaai.com",
  credentials: {
    apiKey: process.env.REACT_APP_API_KEY || "",
  },
  logging: {
    level: (process.env.REACT_APP_LOG_LEVEL as any) || "info",
    console: process.env.NODE_ENV === "development",
    remote: process.env.NODE_ENV === "production",
  },
  environment: process.env.NODE_ENV || "development",
};
```

## Advanced Configuration Patterns

### Feature-Specific Configuration

```typescript
// src/config/sdk-config.ts
const audioProfiles = {
  medical: {
    sampleRate: 16000,
    channels: 1,
    noiseCancellation: true,
    echoCancellation: true,
    autoGainControl: true,
  },
  highQuality: {
    sampleRate: 48000,
    channels: 2,
    bitDepth: 24,
    noiseCancellation: false,
    echoCancellation: false,
    autoGainControl: false,
  },
  realtime: {
    sampleRate: 16000,
    channels: 1,
    chunkSize: 512,
    noiseCancellation: true,
  },
};

export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: "your-api-key-here",
  },
  audioSettings: audioProfiles.medical,
  // ... other settings
};

// Export profiles for specific use cases
export const HIGH_QUALITY_CONFIG = {
  ...SDK_CONFIG_OPTIONS,
  audioSettings: audioProfiles.highQuality,
};

export const REALTIME_CONFIG = {
  ...SDK_CONFIG_OPTIONS,
  audioSettings: audioProfiles.realtime,
};
```

### Dynamic Configuration

```typescript
// src/config/sdk-config.ts
interface ConfigOptions {
  profile: "medical" | "research" | "demo";
  region: "us" | "eu" | "asia";
  features: string[];
}

export function createSDKConfig(options: ConfigOptions) {
  const baseConfig = {
    credentials: {
      apiKey: process.env.REACT_APP_API_KEY || "",
    },
    logging: {
      level: "info" as const,
      console: process.env.NODE_ENV === "development",
      remote: process.env.NODE_ENV === "production",
    },
  };

  // Region-specific endpoints
  const regionEndpoints = {
    us: {
      apiEndpoint: "https://us-api.arcaai.com",
      websocketUrl: "wss://us-api.arcaai.com",
    },
    eu: {
      apiEndpoint: "https://eu-api.arcaai.com",
      websocketUrl: "wss://eu-api.arcaai.com",
    },
    asia: {
      apiEndpoint: "https://asia-api.arcaai.com",
      websocketUrl: "wss://asia-api.arcaai.com",
    },
  };

  // Profile-specific settings
  const profileSettings = {
    medical: {
      audioSettings: {
        sampleRate: 16000,
        channels: 1,
        noiseCancellation: true,
        echoCancellation: true,
        autoGainControl: true,
      },
      errorReporting: {
        enabled: true,
        includeStackTrace: false,
        maxErrorsPerSession: 5,
      },
    },
    research: {
      audioSettings: {
        sampleRate: 48000,
        channels: 2,
        bitDepth: 24,
        noiseCancellation: false,
      },
      errorReporting: {
        enabled: true,
        includeStackTrace: true,
        maxErrorsPerSession: 20,
      },
    },
    demo: {
      audioSettings: {
        sampleRate: 16000,
        channels: 1,
        chunkSize: 512,
      },
      errorReporting: {
        enabled: false,
        includeStackTrace: false,
        maxErrorsPerSession: 0,
      },
    },
  };

  return {
    ...baseConfig,
    ...regionEndpoints[options.region],
    ...profileSettings[options.profile],
    environment: process.env.NODE_ENV || "development",
    features: options.features,
  };
}

// Default configuration
export const SDK_CONFIG_OPTIONS = createSDKConfig({
  profile: "medical",
  region: "us",
  features: ["stt", "tts", "session-management"],
});
```

## Configuration Validation

### Type-Safe Configuration

```typescript
// src/config/sdk-config.ts
import { z } from "zod";

const SDKConfigSchema = z.object({
  apiEndpoint: z.string().url(),
  websocketUrl: z.string().url(),
  credentials: z.object({
    apiKey: z.string().min(1),
  }),
  audioSettings: z
    .object({
      sampleRate: z.number().positive(),
      channels: z.union([z.literal(1), z.literal(2)]),
      bitDepth: z
        .union([z.literal(8), z.literal(16), z.literal(24), z.literal(32)])
        .optional(),
      format: z.enum(["pcm", "wav", "mp3"]).optional(),
      chunkSize: z.number().positive().optional(),
      noiseCancellation: z.boolean().optional(),
      echoCancellation: z.boolean().optional(),
      autoGainControl: z.boolean().optional(),
    })
    .optional(),
  logging: z
    .object({
      level: z.enum(["debug", "info", "warn", "error"]),
      console: z.boolean(),
      remote: z.boolean().optional(),
    })
    .optional(),
  environment: z.string().optional(),
});

const rawConfig = {
  apiEndpoint: process.env.REACT_APP_API_ENDPOINT || "https://api.arcaai.com",
  websocketUrl: process.env.REACT_APP_WS_ENDPOINT || "wss://api.arcaai.com",
  credentials: {
    apiKey: process.env.REACT_APP_API_KEY || "",
  },
  audioSettings: {
    sampleRate: 16000,
    channels: 1 as const,
    noiseCancellation: true,
    echoCancellation: true,
    autoGainControl: true,
  },
  logging: {
    level: "info" as const,
    console: process.env.NODE_ENV === "development",
  },
  environment: process.env.NODE_ENV || "development",
};

// Validate configuration
export const SDK_CONFIG_OPTIONS = SDKConfigSchema.parse(rawConfig);
```

### Runtime Configuration Validation

```typescript
// src/utils/configValidator.ts
export function validateSDKConfig(config: any): string[] {
  const errors: string[] = [];

  if (!config.apiEndpoint) {
    errors.push("API endpoint is required");
  }

  if (!config.websocketUrl) {
    errors.push("WebSocket URL is required");
  }

  if (!config.credentials?.apiKey) {
    errors.push("API key is required");
  }

  if (
    config.audioSettings?.sampleRate &&
    ![8000, 16000, 44100, 48000].includes(config.audioSettings.sampleRate)
  ) {
    errors.push("Invalid sample rate. Must be 8000, 16000, 44100, or 48000");
  }

  if (
    config.audioSettings?.channels &&
    ![1, 2].includes(config.audioSettings.channels)
  ) {
    errors.push("Invalid channel count. Must be 1 or 2");
  }

  return errors;
}

// Usage in configuration
import { validateSDKConfig } from "../utils/configValidator";

const config = {
  // ... your configuration
};

const validationErrors = validateSDKConfig(config);
if (validationErrors.length > 0) {
  console.error("Configuration errors:", validationErrors);
  throw new Error("Invalid SDK configuration");
}

export const SDK_CONFIG_OPTIONS = config;
```

## Best Practices

### 1. Security Best Practices

```typescript
// Never commit API keys to version control
// Use environment variables for sensitive data
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: process.env.REACT_APP_API_ENDPOINT!,
  websocketUrl: process.env.REACT_APP_WS_ENDPOINT!,
  credentials: {
    apiKey: process.env.REACT_APP_API_KEY!,
  },
  // ... other settings
};

// Add validation for required environment variables
if (!process.env.REACT_APP_API_KEY) {
  throw new Error("REACT_APP_API_KEY environment variable is required");
}
```

### 2. Configuration Organization

```typescript
// Organize configuration into logical groups
export const SDK_CONFIG_OPTIONS = {
  // Core connectivity
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: process.env.REACT_APP_API_KEY!,
  },

  // Audio processing settings
  audioSettings: {
    sampleRate: 16000,
    channels: 1,
    noiseCancellation: true,
    echoCancellation: true,
    autoGainControl: true,
  },

  // Development and debugging
  logging: {
    level: "info",
    console: process.env.NODE_ENV === "development",
  },

  // Error handling
  errorReporting: {
    enabled: true,
    includeStackTrace: process.env.NODE_ENV === "development",
    maxErrorsPerSession: 10,
  },

  // Environment
  environment: process.env.NODE_ENV || "development",
};
```

### 3. Configuration Testing

```typescript
// src/config/__tests__/sdk-config.test.ts
import { SDK_CONFIG_OPTIONS } from "../sdk-config";
import { validateSDKConfig } from "../../utils/configValidator";

describe("SDK Configuration", () => {
  test("should have valid configuration", () => {
    const errors = validateSDKConfig(SDK_CONFIG_OPTIONS);
    expect(errors).toHaveLength(0);
  });

  test("should have required fields", () => {
    expect(SDK_CONFIG_OPTIONS.apiEndpoint).toBeDefined();
    expect(SDK_CONFIG_OPTIONS.websocketUrl).toBeDefined();
    expect(SDK_CONFIG_OPTIONS.credentials.apiKey).toBeDefined();
  });

  test("should have valid audio settings", () => {
    const { audioSettings } = SDK_CONFIG_OPTIONS;
    if (audioSettings) {
      expect([8000, 16000, 44100, 48000]).toContain(audioSettings.sampleRate);
      expect([1, 2]).toContain(audioSettings.channels);
    }
  });
});
```

## Common Configuration Patterns

### 1. Medical Consultation Setup

```typescript
export const MEDICAL_CONSULTATION_CONFIG = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: process.env.REACT_APP_API_KEY!,
  },
  audioSettings: {
    sampleRate: 16000, // Optimal for speech recognition
    channels: 1, // Mono for consistency
    noiseCancellation: true, // Essential for clinical environments
    echoCancellation: true, // Prevent feedback
    autoGainControl: true, // Normalize voices
  },
  logging: {
    level: "info",
    console: false,
    remote: true,
  },
  errorReporting: {
    enabled: true,
    includeStackTrace: false, // Protect sensitive data
    maxErrorsPerSession: 5,
  },
};
```

### 2. Development Setup

```typescript
export const DEVELOPMENT_CONFIG = {
  apiEndpoint: "http://localhost:3333",
  websocketUrl: "ws://localhost:3333",
  credentials: {
    apiKey: "dev-api-key",
  },
  audioSettings: {
    sampleRate: 16000,
    channels: 1,
    chunkSize: 512, // Smaller chunks for faster feedback
    noiseCancellation: true,
  },
  logging: {
    level: "debug", // Verbose logging for development
    console: true,
    remote: false,
  },
  errorReporting: {
    enabled: true,
    includeStackTrace: true, // Full debug info
    maxErrorsPerSession: 50,
  },
};
```

### 3. High-Quality Recording Setup

```typescript
export const HIGH_QUALITY_CONFIG = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: process.env.REACT_APP_API_KEY!,
  },
  audioSettings: {
    sampleRate: 48000, // Professional quality
    channels: 2, // Stereo recording
    bitDepth: 24, // High dynamic range
    format: "wav", // Lossless format
    chunkSize: 4096, // Larger chunks for quality
    noiseCancellation: false, // Preserve natural audio
    echoCancellation: false,
    autoGainControl: false,
  },
};
```

## Troubleshooting Configuration Issues

### Common Issues and Solutions

1. **Missing API Key**

   ```typescript
   // Add validation
   if (!process.env.REACT_APP_API_KEY) {
     throw new Error(
       "API key is required. Set REACT_APP_API_KEY environment variable."
     );
   }
   ```

2. **Invalid Endpoints**

   ```typescript
   // Validate URLs
   try {
     new URL(SDK_CONFIG_OPTIONS.apiEndpoint);
     new URL(SDK_CONFIG_OPTIONS.websocketUrl);
   } catch (error) {
     throw new Error("Invalid endpoint URLs in configuration");
   }
   ```

3. **Environment Variable Issues**

   ```typescript
   // Debug environment variables
   console.log("Environment variables:", {
     NODE_ENV: process.env.NODE_ENV,
     API_ENDPOINT: process.env.REACT_APP_API_ENDPOINT,
     WS_ENDPOINT: process.env.REACT_APP_WS_ENDPOINT,
     API_KEY: process.env.REACT_APP_API_KEY ? "[SET]" : "[MISSING]",
   });
   ```

4. **Configuration Loading Issues**
   ```typescript
   // Add error handling
   try {
     const config = SDK_CONFIG_OPTIONS;
     console.log("Configuration loaded successfully");
   } catch (error) {
     console.error("Failed to load configuration:", error);
     throw error;
   }
   ```

## Migration Guide

### From Environment Variables to SDK_CONFIG_OPTIONS

If you're migrating from the old approach:

**Before:**

```typescript
const sessionManager = useArcaSessionManager({
  options: {
    apiEndpoint: process.env.REACT_APP_API_ENDPOINT,
    credentials: { apiKey: process.env.REACT_APP_API_KEY },
  },
});
```

**After:**

```typescript
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const sessionManager = useArcaSessionManager({
  options: SDK_CONFIG_OPTIONS,
});
```

### Migration Steps

1. Create the configuration file
2. Move all environment variable usage to the config file
3. Update all hook usage to use `SDK_CONFIG_OPTIONS`
4. Test the configuration in all environments
5. Remove old inline configuration

This centralized approach provides better maintainability, type safety, and consistency across your application.
