import { Logger } from '../core/Logger';

export interface ApiClientConfig {
    baseUrl: string;
    timeout?: number;
    defaultHeaders?: Record<string, string>;
    apiKey?: string;
    credentials?: RequestCredentials;
}

export interface RequestOptions {
    headers?: Record<string, string>;
    timeout?: number;
    signal?: AbortSignal;
    credentials?: RequestCredentials;
}

export interface ApiResponse<T = any> {
    data: T;
    status: number;
    statusText: string;
    headers: Headers;
}

export class ApiClientError extends Error {
    public readonly status: number;
    public readonly statusText: string;
    public readonly response?: Response | undefined;
    public readonly data?: any;

    constructor(message: string, status: number, statusText: string, response?: Response | undefined, data?: any) {
        super(message);
        this.name = 'ApiClientError';
        this.status = status;
        this.statusText = statusText;
        this.response = response;
        this.data = data;
    }
}

export class ApiClient {
    private config: ApiClientConfig;
    private logger: Logger;

    constructor(config: ApiClientConfig) {
        this.config = config;
        this.logger = new Logger();
    }

    public updateConfig(newConfig: Partial<ApiClientConfig>): void {
        this.config = { ...this.config, ...newConfig };
    }

    public getConfig(): ApiClientConfig {
        return { ...this.config };
    }

    public async get<T = any>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
        return this.request<T>('GET', url, undefined, options);
    }

    public async post<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
        return this.request<T>('POST', url, data, options);
    }

    public async put<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
        return this.request<T>('PUT', url, data, options);
    }

    public async delete<T = any>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
        return this.request<T>('DELETE', url, undefined, options);
    }

    public async patch<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
        return this.request<T>('PATCH', url, data, options);
    }

    private async request<T = any>(method: string, url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
        const fullUrl = this.buildUrl(url);
        const headers = this.buildHeaders(options?.headers);
        const timeout = options?.timeout || this.config.timeout || 30000;

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const signal = options?.signal || controller.signal;

        try {
            this.logger.debug(`API Request: ${method} ${fullUrl}`, {
                method,
                url: fullUrl,
                headers: this.sanitizeHeaders(headers),
                hasData: !!data,
            });

            const requestInit: RequestInit = {
                method,
                headers,
                signal,
                credentials: options?.credentials || this.config.credentials || 'same-origin',
            };

            if (data && ['POST', 'PUT', 'PATCH'].includes(method)) {
                if (data instanceof FormData) {
                    requestInit.body = data;
                    delete headers['Content-Type'];
                } else if (typeof data === 'string') {
                    requestInit.body = data;
                } else {
                    requestInit.body = JSON.stringify(data);
                    headers['Content-Type'] = 'application/json';
                }
            }

            const response = await fetch(fullUrl, requestInit);

            clearTimeout(timeoutId);

            return await this.handleResponse<T>(response);
        } catch (error) {
            clearTimeout(timeoutId);

            if (error instanceof DOMException && error.name === 'AbortError') {
                const timeoutError = new ApiClientError(`Request timeout after ${timeout}ms`, 408, 'Request Timeout');
                this.logger.error('API Request timeout', {
                    method,
                    url: fullUrl,
                    timeout,
                    error: timeoutError,
                });
                throw timeoutError;
            }

            this.logger.error('API Request failed', {
                method,
                url: fullUrl,
                error: error instanceof Error ? error.message : String(error),
            });

            if (error instanceof ApiClientError) {
                throw error;
            }

            throw new ApiClientError(`Network error: ${error instanceof Error ? error.message : String(error)}`, 0, 'Network Error');
        }
    }

    private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
        const { status, statusText, headers } = response;

        let data: T;
        const contentType = headers.get('content-type') || '';

        try {
            if (contentType.includes('application/json')) {
                data = await response.json();
            } else if (contentType.includes('text/')) {
                data = (await response.text()) as unknown as T;
            } else {
                data = (await response.blob()) as unknown as T;
            }
        } catch (parseError) {
            this.logger.error('Failed to parse response', {
                status,
                statusText,
                contentType,
                error: parseError instanceof Error ? parseError.message : String(parseError),
            });

            if (!response.ok) {
                throw new ApiClientError(`HTTP ${status}: ${statusText}`, status, statusText, response);
            }

            // If response is OK but can't parse, return empty data
            data = {} as T;
        }

        this.logger.debug('API Response received', {
            status,
            statusText,
            contentType,
            hasData: !!data,
        });

        if (!response.ok) {
            const errorMessage = this.extractErrorMessage(data, status, statusText);
            const apiError = new ApiClientError(errorMessage, status, statusText, response, data);

            this.logger.error('API Error response', {
                status,
                statusText,
                error: apiError,
                responseData: data,
            });

            throw apiError;
        }

        return {
            data,
            status,
            statusText,
            headers,
        };
    }

    private buildUrl(url: string): string {
        // If URL is already absolute, return as-is
        if (url.startsWith('http://') || url.startsWith('https://')) {
            return url;
        }

        const cleanUrl = url.startsWith('/') ? url.slice(1) : url;
        const cleanBaseUrl = this.config.baseUrl.endsWith('/') ? this.config.baseUrl.slice(0, -1) : this.config.baseUrl;

        return `${cleanBaseUrl}/${cleanUrl}`;
    }

    private buildHeaders(additionalHeaders?: Record<string, string>): Record<string, string> {
        const headers: Record<string, string> = {
            'Content-Type': 'application/json',
            ...this.config.defaultHeaders,
        };

        if (this.config.apiKey) {
            headers['x-api-key'] = this.config.apiKey;
        }

        if (additionalHeaders) {
            Object.assign(headers, additionalHeaders);
        }

        return headers;
    }

    private sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
        const sanitized = { ...headers };

        const sensitiveHeaders = ['authorization', 'x-api-key', 'cookie'];
        for (const key of Object.keys(sanitized)) {
            if (sensitiveHeaders.includes(key.toLowerCase())) {
                sanitized[key] = '***';
            }
        }

        return sanitized;
    }

    private extractErrorMessage(data: any, status: number, statusText: string): string {
        if (data && typeof data === 'object') {
            // Standard error formats
            if (data.message) return data.message;
            if (data.error && typeof data.error === 'string') return data.error;
            if (data.error && data.error.message) return data.error.message;
            if (data.errors && Array.isArray(data.errors) && data.errors.length > 0) {
                return data.errors.map((e: any) => (typeof e === 'string' ? e : e.message || e)).join(', ');
            }
            if (data.detail) return data.detail;
        }

        // Fallback to HTTP status
        return `HTTP ${status}: ${statusText}`;
    }

    public static isNetworkError(error: any): boolean {
        return error instanceof ApiClientError && error.status === 0;
    }

    public static isTimeoutError(error: any): boolean {
        return error instanceof ApiClientError && error.status === 408;
    }

    public static isClientError(error: any): boolean {
        return error instanceof ApiClientError && error.status >= 400 && error.status < 500;
    }

    public static isServerError(error: any): boolean {
        return error instanceof ApiClientError && error.status >= 500;
    }
}
