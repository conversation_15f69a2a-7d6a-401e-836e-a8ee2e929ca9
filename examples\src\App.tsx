import { useState } from 'react';
import { STTDemo } from './stt-demo';
import TTSExample  from './tts-example';
import { SummarizationDemo } from './summarization-demo';

type ExampleType = 'stt' | 'tts' | 'summarization';

const examples = [
  {
    id: 'stt' as ExampleType,
    title: 'STT Demo',
    description: 'Basic demonstration of useArcaSpeechToText and useAudioCapture hooks',
    difficulty: 'Beginner',
    component: STTDemo
  },
  {
    id: 'tts' as ExampleType,
    title: 'Text-to-Speech',
    description: 'Real-time text-to-speech synthesis with streaming audio',
    difficulty: 'Intermediate',
    component: TTSExample
  },
  {
    id: 'summarization' as ExampleType,
    title: 'Summarization',
    description: 'Text summarization with automatic chunking for long conversations',
    difficulty: 'Advanced',
    component: SummarizationDemo
  },
];

function App() {
  const [selectedExample, setSelectedExample] = useState<string>('stt');

  const currentExample = examples.find(ex => ex.id === selectedExample);
  const CurrentComponent = currentExample?.component;

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      {/* Header */}
      <header style={{
        backgroundColor: '#007bff',
        color: 'white',
        padding: '20px 0',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
          <h1 style={{ margin: '0 0 10px 0', fontSize: '28px' }}>
            🎤 Agentic Audio SDK Examples
          </h1>
          <p style={{ margin: 0, opacity: 0.9 }}>
            Interactive examples demonstrating the capabilities of the Agentic Audio SDK
          </p>
        </div>
      </header>

      {/* Navigation */}
      <nav style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #dee2e6',
        padding: '15px 0'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
          <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
            {examples.map(example => (
              <button
                key={example.id}
                onClick={() => setSelectedExample(example.id)}
                style={{
                  padding: '10px 20px',
                  border: `2px solid ${selectedExample === example.id ? '#007bff' : '#dee2e6'}`,
                  backgroundColor: selectedExample === example.id ? '#007bff' : 'white',
                  color: selectedExample === example.id ? 'white' : '#333',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: selectedExample === example.id ? 'bold' : 'normal',
                  transition: 'all 0.2s ease'
                }}
              >
                <div>{example.title}</div>
                <div style={{
                  fontSize: '12px',
                  opacity: 0.8,
                  marginTop: '2px'
                }}>
                  {example.difficulty}
                </div>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Example Info */}
      {currentExample && (
        <div style={{
          backgroundColor: '#e9ecef',
          padding: '15px 0',
          borderBottom: '1px solid #dee2e6'
        }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
            <h2 style={{ margin: '0 0 5px 0', color: '#333' }}>
              {currentExample.title}
            </h2>
            <p style={{ margin: 0, color: '#666' }}>
              {currentExample.description}
            </p>
          </div>
        </div>
      )}

      {/* Example Content */}
      <main style={{ padding: '20px 0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          {CurrentComponent && <CurrentComponent />}
        </div>
      </main>

      {/* Footer */}
      <footer style={{
        backgroundColor: '#343a40',
        color: 'white',
        padding: '20px 0',
        marginTop: '40px',
        textAlign: 'center'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
          <p style={{ margin: '0 0 10px 0' }}>
            Agentic Audio SDK Examples
          </p>
          <p style={{ margin: 0, fontSize: '14px', opacity: 0.8 }}>
            Built with React, TypeScript, and Vite
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;