import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SessionManager } from '../core/SessionManager';
import type { SessionStorageConfig } from '../types/index';

// Mock the storage and recovery managers to avoid complex async issues
vi.mock('../core/SessionStorageManager', () => ({
    SessionStorageManager: vi.fn().mockImplementation(() => ({
        initialize: vi.fn().mockResolvedValue(undefined),
        storeSession: vi.fn().mockResolvedValue(undefined),
        getSession: vi.fn().mockResolvedValue(null),
        createBackup: vi.fn().mockResolvedValue('backup-123'),
        cleanup: vi.fn().mockResolvedValue({ removedSessions: 0, freedSpace: 0 }),
        on: vi.fn(),
    })),
}));

vi.mock('../core/SessionRecoveryManager', () => ({
    SessionRecoveryManager: vi.fn().mockImplementation(() => ({
        initialize: vi.fn().mockResolvedValue(undefined),
        findRecoverableSessions: vi.fn().mockResolvedValue([]),
        recoverSession: vi.fn().mockResolvedValue(null),
        on: vi.fn(),
    })),
}));

vi.mock('../utils/DeviceInfoCollector', () => ({
    DeviceInfoCollector: vi.fn().mockImplementation(() => ({
        collect: vi.fn().mockResolvedValue({
            deviceId: 'test-device-123',
            deviceType: 'desktop',
            browser: 'Chrome',
            browserVersion: '120.0',
            platform: 'macOS',
            userAgent: 'Mozilla/5.0 Test',
            screenResolution: '1920x1080',
            timezone: 'America/New_York',
            lastSeen: new Date(),
        }),
    })),
}));

vi.mock('../core/Logger', () => ({
    Logger: vi.fn().mockImplementation(() => ({
        info: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
    })),
}));

// Mock crypto for checksum calculation
Object.defineProperty(global, 'crypto', {
    value: {
        subtle: {
            digest: vi.fn().mockResolvedValue(new ArrayBuffer(32)),
        },
    },
    writable: true,
});

describe('SessionManager', () => {
    let sessionManager: SessionManager;
    let testConfig: SessionStorageConfig;

    beforeEach(() => {
        sessionManager = new SessionManager();
        testConfig = {
            encryptionKey: 'test-encryption-key-32-characters!',
            backupInterval: 5 * 60 * 1000,
            maxStorageSize: 10 * 1024 * 1024,
            compressionEnabled: true,
            retentionPeriod: 24 * 60 * 60 * 1000,
            maxSessions: 10,
            autoCleanup: true,
        };
        vi.clearAllMocks();
    });

    describe('Initialization', () => {
        it('should initialize with valid configuration', async () => {
            await expect(sessionManager.initialize(testConfig)).resolves.not.toThrow();
        });
    });

    describe('Session Lifecycle', () => {
        beforeEach(async () => {
            await sessionManager.initialize(testConfig);
        });

        it('should create a new session with default metadata', async () => {
            const session = await sessionManager.createSession();

            expect(session).toBeDefined();
            expect(session.id).toMatch(/^session_\d+_[a-z0-9]+$/);
            expect(session.status).toBe('idle');
            expect(session.metadata.priority).toBe('medium');
            expect(session.metadata.sessionType).toBe('consultation');
            expect(session.audioData.chunks).toHaveLength(0);
            expect(session.transcriptData.segments).toHaveLength(0);
        });

        it('should create a session with custom metadata', async () => {
            const customMetadata = {
                title: 'Patient Consultation',
                description: 'Initial consultation session',
                sessionType: 'consultation' as const,
                priority: 'high' as const,
                tags: ['initial', 'consultation'],
            };

            const session = await sessionManager.createSession(customMetadata);

            expect(session.metadata.title).toBe('Patient Consultation');
            expect(session.metadata.description).toBe('Initial consultation session');
            expect(session.metadata.priority).toBe('high');
            expect(session.metadata.tags).toEqual(['initial', 'consultation']);
        });

        it('should start a session', async () => {
            await sessionManager.createSession();
            await sessionManager.startSession();

            const currentSession = sessionManager.getCurrentSession();
            expect(currentSession?.status).toBe('active');
        });

        it('should pause a session', async () => {
            await sessionManager.createSession();
            await sessionManager.startSession();
            await sessionManager.pauseSession('User requested');

            const currentSession = sessionManager.getCurrentSession();
            expect(currentSession?.status).toBe('paused');
        });

        it('should resume a session', async () => {
            await sessionManager.createSession();
            await sessionManager.startSession();
            await sessionManager.pauseSession();
            await sessionManager.resumeSession();

            const currentSession = sessionManager.getCurrentSession();
            expect(currentSession?.status).toBe('active');
        });

        it('should end a session', async () => {
            await sessionManager.createSession();
            await sessionManager.startSession();
            await sessionManager.endSession();

            const currentSession = sessionManager.getCurrentSession();
            expect(currentSession).toBeNull();
        });

        it('should throw error when trying to start without creating session', async () => {
            await expect(sessionManager.startSession()).rejects.toThrow('No active session to start');
        });

        it('should throw error when trying to pause without active session', async () => {
            await expect(sessionManager.pauseSession()).rejects.toThrow('No active session to pause');
        });
    });

    describe('Session Metadata Management', () => {
        beforeEach(async () => {
            await sessionManager.initialize(testConfig);
            await sessionManager.createSession();
        });

        it('should set patient information', async () => {
            const patientInfo = {
                id: 'patient-123',
                mrn: 'MRN-456',
                name: 'John Doe',
                dateOfBirth: new Date('1990-01-01'),
                gender: 'male' as const,
            };

            await sessionManager.setPatientInfo(patientInfo);

            const currentSession = sessionManager.getCurrentSession();
            expect(currentSession?.patientId).toBe('patient-123');
            expect(currentSession?.metadata.patientInfo).toEqual(patientInfo);
        });

        it('should set provider information', async () => {
            const providerInfo = {
                id: 'provider-456',
                name: 'Dr. Jane Smith',
                role: 'physician' as const,
                department: 'Cardiology',
                licenseNumber: 'LIC-789',
            };

            await sessionManager.setProviderInfo(providerInfo);

            const currentSession = sessionManager.getCurrentSession();
            expect(currentSession?.metadata.providerInfo).toEqual(providerInfo);
        });

        it('should update session metadata', async () => {
            const updates = {
                title: 'Updated Title',
                priority: 'critical' as const,
                tags: ['urgent', 'cardiology'],
            };

            await sessionManager.updateSessionMetadata(updates);

            const currentSession = sessionManager.getCurrentSession();
            expect(currentSession?.metadata.title).toBe('Updated Title');
            expect(currentSession?.metadata.priority).toBe('critical');
            expect(currentSession?.metadata.tags).toEqual(['urgent', 'cardiology']);
        });
    });

    describe('Performance Metrics', () => {
        beforeEach(async () => {
            await sessionManager.initialize(testConfig);
        });

        it('should track session performance metrics', async () => {
            const session = await sessionManager.createSession();
            const metrics = sessionManager.getSessionMetrics(session.id);

            expect(metrics).toBeDefined();
            expect(metrics?.sessionId).toBe(session.id);
            expect(metrics?.creationTime).toBeGreaterThan(0);
            expect(metrics?.lastBackupTime).toBeGreaterThan(0);
        });

        it('should return undefined for non-existent session metrics', () => {
            const metrics = sessionManager.getSessionMetrics('non-existent-session');
            expect(metrics).toBeUndefined();
        });
    });

    describe('Recovery Operations', () => {
        beforeEach(async () => {
            await sessionManager.initialize(testConfig);
        });

        it('should check for recoverable sessions', async () => {
            const recoverableSessions = await sessionManager.checkForRecoverableSessions();
            expect(Array.isArray(recoverableSessions)).toBe(true);
        });

        it('should recover session successfully', async () => {
            // Create a session first
            const session = await sessionManager.createSession();

            const recoveredSession = await sessionManager.recoverSession(session.id, {
                autoRecover: true,
                showRecoveryModal: false,
                maxRecoveryAttempts: 3,
                recoveryTimeout: 5000,
                fallbackToPartialRecovery: true,
                userConfirmationRequired: false,
            });

            expect(recoveredSession).toBeDefined();
        });
    });

    describe('Network Failure Recovery', () => {
        beforeEach(async () => {
            await sessionManager.initialize(testConfig);
        });

        it('should get sync status', () => {
            const syncStatus = sessionManager.getSyncStatus();

            expect(syncStatus).toBeDefined();
            expect(syncStatus).toHaveProperty('isOnline');
            expect(syncStatus).toHaveProperty('syncInProgress');
            expect(syncStatus).toHaveProperty('pendingActions');

            // In test environment, isOnline should be true (default when navigator is undefined)
            expect(typeof syncStatus.isOnline).toBe('boolean');
            expect(typeof syncStatus.syncInProgress).toBe('boolean');
            expect(typeof syncStatus.pendingActions).toBe('number');
        });

        it('should add actions to offline queue', () => {
            const action = {
                sessionId: 'test-session',
                action: 'create' as const,
                data: { test: 'data' },
                priority: 'medium' as const,
                maxRetries: 3,
                dependencies: [],
            };

            sessionManager.addToOfflineQueue(action);

            const syncStatus = sessionManager.getSyncStatus();
            expect(syncStatus.pendingActions).toBeGreaterThan(0);
        });

        it('should handle multiple offline actions', () => {
            const actions = [
                {
                    sessionId: 'test-session-1',
                    action: 'create' as const,
                    data: { test: 'data1' },
                    priority: 'high' as const,
                    maxRetries: 3,
                    dependencies: [],
                },
                {
                    sessionId: 'test-session-2',
                    action: 'update' as const,
                    data: { test: 'data2' },
                    priority: 'low' as const,
                    maxRetries: 2,
                    dependencies: [],
                },
            ];

            actions.forEach(action => sessionManager.addToOfflineQueue(action));

            const syncStatus = sessionManager.getSyncStatus();
            expect(syncStatus.pendingActions).toBe(actions.length);
        });
    });

    describe('Cleanup', () => {
        beforeEach(async () => {
            await sessionManager.initialize(testConfig);
        });

        it('should cleanup sessions and return statistics', async () => {
            const result = await sessionManager.cleanup();

            expect(result).toBeDefined();
            expect(typeof result.removedSessions).toBe('number');
            expect(typeof result.freedSpace).toBe('number');
        });
    });
});