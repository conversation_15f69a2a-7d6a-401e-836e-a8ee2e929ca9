# useArcaSessionManager

The `useArcaSessionManager` hook provides complete lifecycle management for medical consultation sessions. It handles session creation, state management, data persistence, and automatic recovery.

## Overview

This hook manages the entire consultation session workflow from creation to completion, including automatic data synchronization and error recovery mechanisms.

## Import

```typescript
import { useArcaSessionManager } from "@arcaai/agentic-sdk";
```

## Basic Usage

```typescript
import React, { useState } from "react";
import { useArcaSessionManager } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

function ConsultationApp() {
  const [doctorName] = useState("Dr. Smith");
  const [patientName] = useState("John Doe");

  const {
    session,
    isLoading,
    error,
    createSession,
    startSession,
    pauseSession,
    resumeSession,
    endSession,
    updateSession,
    loadSession,
    clearError,
  } = useArcaSessionManager({
    doctorId: "dr-123",
    doctorName,
    patientId: "pt-456",
    patientName,
    options: SDK_CONFIG_OPTIONS,
    onError: (error) => {
      console.error("Session error:", error);
    },
  });

  const handleStartConsultation = async () => {
    try {
      await createSession({
        sessionType: "consultation",
        priority: "medium",
        tags: ["routine-checkup"],
      });
      await startSession();
    } catch (err) {
      console.error("Failed to start consultation:", err);
    }
  };

  if (isLoading) return <div>Initializing session manager...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h1>Medical Consultation</h1>
      {!session ? (
        <button onClick={handleStartConsultation}>Start Consultation</button>
      ) : (
        <div>
          <p>Session ID: {session.id}</p>
          <p>Status: {session.status}</p>
          <p>Doctor: {session.provider.name}</p>
          <p>Patient: {session.patient.name}</p>
        </div>
      )}
    </div>
  );
}
```

## Configuration Setup

Before using the hook, ensure you have your SDK configuration ready:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: "your-api-key-here",
  },
  // Optional: Additional configuration
  logging: {
    level: "info",
    console: true,
  },
  environment: "production",
};
```

## API Reference

### Parameters

The hook accepts a single configuration object with the following properties:

#### `IArcaSessionOptions`

| Property      | Type                         | Required | Description                                                |
| ------------- | ---------------------------- | -------- | ---------------------------------------------------------- |
| `doctorId`    | `string`                     | ✅       | Unique identifier for the healthcare provider               |
| `doctorName`  | `string`                     | ✅       | Display name of the healthcare provider                    |
| `patientId`   | `string`                     | ✅       | Unique identifier for the patient                           |
| `patientName` | `string`                     | ✅       | Display name of the patient                                |
| `sessionId`   | `string`                     | ❌       | Optional session ID for recovery/loading existing sessions |
| `options`     | `Partial<SDKConfig>`          | ❌       | SDK configuration options (use `SDK_CONFIG_OPTIONS`)        |
| `onError`     | `(error: ErrorInfo) => void` | ❌       | Error callback function                                    |

### Return Value

The hook returns an object with the following properties:

#### `IArcaSessionManagerReturn`

| Property        | Type                                                               | Description                         |
| --------------- | ------------------------------------------------------------------ | ----------------------------------- |
| `session`       | `MedicalSession \| null`                                           | Current active session or null      |
| `isLoading`     | `boolean`                                                          | Loading state during initialization |
| `error`         | `ErrorInfo \| null`                                                | Current error state                 |
| `createSession` | `(metadata?: Partial<SessionMetadata>) => Promise<MedicalSession>` | Creates a new session               |
| `updateSession` | `(data?: Record<string, unknown>) => Promise<void>`                | Updates session with custom data    |
| `loadSession`   | `(sessionId: string) => Promise<MedicalSession>`                   | Loads an existing session           |
| `startSession`  | `() => Promise<void>`                                              | Starts the current session          |
| `pauseSession`  | `(reason?: string) => Promise<void>`                               | Pauses the current session          |
| `resumeSession` | `() => Promise<void>`                                              | Resumes a paused session            |
| `endSession`    | `() => Promise<void>`                                              | Ends the current session            |
| `clearError`    | `() => void`                                                       | Clears the current error state      |

## Session States

Sessions progress through different states during their lifecycle:

- **`IDLE`**: Session created but not started
- **`ACTIVE`**: Session is active and recording
- **`PAUSED`**: Session temporarily paused
- **`ENDED`**: Session completed

## Advanced Usage

### Custom Session Metadata

```typescript
import { useArcaSessionManager } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const { createSession } = useArcaSessionManager({
  doctorId: "dr-123",
  doctorName: "Dr. Smith",
  patientId: "pt-456",
  patientName: "John Doe",
  options: SDK_CONFIG_OPTIONS,
});

const handleCreateCustomSession = async () => {
  await createSession({
    sessionType: "consultation",
    priority: "high",
    tags: ["follow-up", "chronic-condition"],
    customFields: {
      appointmentId: "apt-123",
      consultationType: "telehealth",
      specialization: "cardiology",
      urgency: "routine",
    },
  });
};
```

### Automatic Session Updates

The hook automatically updates sessions when integrated with other SDK hooks:

```typescript
import {
  useArcaSessionManager,
  useArcaSpeechToText,
} from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

// Session management
const sessionManager = useArcaSessionManager({
  doctorId: "dr-123",
  doctorName: "Dr. Smith",
  patientId: "pt-456",
  patientName: "John Doe",
  options: SDK_CONFIG_OPTIONS,
});

const speechToText = useArcaSpeechToText({
  sessionId: sessionManager.session?.id || "",
  options: SDK_CONFIG_OPTIONS,
  onTranscript: (text, isFinal) => {
    // Session automatically updated with transcript data
    if (isFinal) {
      console.log("Transcript saved to session:", text);
    }
  },
});
```

### Session Recovery

```typescript
import { useArcaSessionManager } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const { loadSession, session } = useArcaSessionManager({
  doctorId: "dr-123",
  doctorName: "Dr. Smith",
  patientId: "pt-456",
  patientName: "John Doe",
  options: SDK_CONFIG_OPTIONS,
});

const handleRecoverSession = async () => {
  try {
    const recoveredSession = await loadSession("session-id-123");
    console.log("Session recovered:", recoveredSession.status);
  } catch (error) {
    console.error("Recovery failed:", error);
  }
};
```

### Error Handling

```typescript
import { useArcaSessionManager } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const sessionManager = useArcaSessionManager({
  doctorId: "dr-123",
  doctorName: "Dr. Smith",
  patientId: "pt-456",
  patientName: "John Doe",
  options: SDK_CONFIG_OPTIONS,
  onError: (error) => {
    switch (error.code) {
      case "SESSION_CREATE_FAILED":
        // Handle creation failures
        showNotification("Failed to create session", "error");
        break;
      case "SESSION_START_FAILED":
        // Handle start failures
        showNotification("Failed to start session", "error");
        break;
      case "NETWORK_ERROR":
        // Handle network issues
        showNotification("Network connection error", "warning");
        break;
      default:
        console.error("Unknown error:", error);
    }
  },
});
```

## Session Data Structure

### MedicalSession

```typescript
interface MedicalSession {
  id: string;
  status: "IDLE" | "ACTIVE" | "PAUSED" | "ENDED";
  provider: ProviderInfo;
  patient: PatientInfo;
  metadata: SessionMetadata;
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  endedAt?: string;
  duration?: number;
}
```

### ProviderInfo

```typescript
interface ProviderInfo {
  id: string;
  name: string;
  role: string;
  specialization?: string;
  credentials?: string[];
}
```

### PatientInfo

```typescript
interface PatientInfo {
  id: string;
  name: string;
  age?: number;
  gender?: string;
  medicalRecordNumber?: string;
}
```

### SessionMetadata

```typescript
interface SessionMetadata {
  sessionType: "consultation" | "follow-up" | "emergency" | "routine";
  priority: "low" | "medium" | "high" | "critical";
  tags: string[];
  customFields: Record<string, any>;
  transcript?: string;
  audioData?: string;
  summary?: string;
  diagnosis?: string[];
  prescriptions?: string[];
}
```

## Best Practices

### 1. Session Lifecycle Management

```typescript
import { useArcaSessionManager } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const ConsultationFlow = () => {
  const sessionManager = useArcaSessionManager({
    doctorId: "dr-123",
    doctorName: "Dr. Smith",
    patientId: "pt-456",
    patientName: "John Doe",
    options: SDK_CONFIG_OPTIONS,
  });

  useEffect(() => {
    // Auto-pause session when user navigates away
    const handleBeforeUnload = async () => {
      if (sessionManager.session?.status === "ACTIVE") {
        await sessionManager.pauseSession("User navigated away");
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [sessionManager]);

  // ... component logic
};
```

### 2. Data Persistence

```typescript
// Automatically save important data to session
const saveToSession = useCallback(
  async (data: any) => {
    if (sessionManager.session?.status === "ACTIVE") {
      await sessionManager.updateSession({
        lastActivity: new Date().toISOString(),
        customData: data,
      });
    }
  },
  [sessionManager]
);
```

### 3. Error Recovery

```typescript
const handleSessionError = (error: ErrorInfo) => {
  if (error.code === "SESSION_MANAGER_INIT_FAILED") {
    // Retry initialization
    setTimeout(() => {
      window.location.reload();
    }, 3000);
  }
};
```

## Events

The session manager emits various events that you can listen to:

- `session:created` - New session created
- `session:loaded` - Existing session loaded
- `session:started` - Session started
- `session:paused` - Session paused
- `session:resumed` - Session resumed
- `session:ended` - Session ended
- `session:recovery-completed` - Session recovery completed

## Configuration Options

The hook uses your centralized `SDK_CONFIG_OPTIONS` configuration:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: "your-api-key-here",
  },
  logging: {
    level: "info",
    console: true,
  },
  errorReporting: {
    enabled: true,
    maxErrorsPerSession: 10,
  },
  environment: "production",
};

const sessionManager = useArcaSessionManager({
  doctorId: "dr-123",
  doctorName: "Dr. Smith",
  patientId: "pt-456",
  patientName: "John Doe",
  options: SDK_CONFIG_OPTIONS, // Use centralized configuration
});
```

## Troubleshooting

### Common Issues

1. **Session Manager Not Initializing**

   - Check API endpoint configuration in `SDK_CONFIG_OPTIONS`
   - Verify API key is valid
   - Ensure network connectivity

2. **Session Creation Fails**

   - Validate doctor and patient IDs
   - Check API permissions
   - Review server logs

3. **Session Recovery Issues**
   - Verify session ID exists
   - Check session hasn't expired
   - Ensure proper credentials in configuration

### Debug Mode

Enable debug logging in your configuration:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: "your-api-key-here",
  },
  logging: {
    level: "debug",
    console: true,
  },
};
```

## Examples

- [Complete Demo](../examples/README.md) - Demo of the SDK
