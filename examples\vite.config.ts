import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';

// https://vite.dev/config/
export default defineConfig({
    plugins: [react()],
    define: {
        global: 'globalThis',
    },
    server: {
        hmr: {
            overlay: false
        }
    },
    optimizeDeps: {
        esbuildOptions: {
            define: {
                global: 'globalThis'
            },
        },
    },
    build: {
        rollupOptions: {
            output: {
                manualChunks: {}
            }
        },
        commonjsOptions: {
            transformMixedEsModules: true,
        },
    },
});
