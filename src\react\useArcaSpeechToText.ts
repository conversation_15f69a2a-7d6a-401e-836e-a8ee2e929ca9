import { useCallback, useEffect, useRef, useState } from 'react';
import type { ErrorInfo, SDKConfig } from '../types/index';
import { createError } from '../utils/createError';

const DEFAULT_SDK_CONFIG: SDKConfig = {
    apiEndpoint: 'http://api.agentic.ai',
    websocketUrl: 'ws://api.agentic.ai',
    sttProvider: 'azure',
    audioSettings: {
        sampleRate: 16000,
        format: 'pcm',
        channels: 1,
        bitDepth: 16,
        chunkSize: 1024,
        noiseCancellation: true,
        echoCancellation: true,
        autoGainControl: true,
    },
    errorReporting: {
        enabled: true,
        includeStackTrace: false,
        maxErrorsPerSession: 10,
    },
    logging: {
        level: 'info',
        console: true,
        remote: false,
    },
    environment: 'development',
};

interface ArcaSpeechToTextSession {
    id: string;
    startTime: number;
    endTime?: number;
    transcript: string;
    audioChunks: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>[];
}

interface TranscriptionTask {
    task_id: string
    session_id: string
    status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILURE'
    started_at: string | null
    completed_at: string | null
    language: string | null
    provider: string | null
    result: any | null
    error: string | null
    message: string | null
}

interface UseArcaSpeechToTextProps {
    sessionId: string;
    language: string;
    options?: Partial<SDKConfig>;
    transcriptTemplate?: string; // e.g. "{timestamp} {speaker_id}: {text}"
    onTranscript: (text: string, isFinal: boolean) => void;
    onError?: (error: ErrorInfo) => void;
}

interface UseArcaSpeechToTextReturn {
    transcript: string;
    error: ErrorInfo | null;
    startTranscription: () => Promise<void>;
    stopTranscription: () => Promise<void>;
    sendAudioData: (audioData: ArrayBuffer) => void;
    uploadAudioFile: (file: File, language: string) => Promise<string>; // Returns task_id
    getTranscriptionStatus: (taskId: string) => Promise<TranscriptionTask>;
    isUploading: boolean;
    uploadProgress: number;
}

export function useArcaSpeechToText(props: UseArcaSpeechToTextProps): UseArcaSpeechToTextReturn {
    const [transcript, setTranscript] = useState('');
    const [error, setError] = useState<ErrorInfo | null>(null);
    const [session, setSession] = useState<ArcaSpeechToTextSession | null>(null);
    
    const [isUploading, setIsUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);

    const websocketRef = useRef<WebSocket | null>(null);
    const configRef = useRef<SDKConfig | null>(null);
    const { sessionId, language, transcriptTemplate, options, onTranscript, onError } = props;

    useEffect(() => {
        configRef.current = {
            ...DEFAULT_SDK_CONFIG,
            ...options,
            audioSettings: {
                ...DEFAULT_SDK_CONFIG.audioSettings,
                ...(options?.audioSettings || {}),
            },
        };
    }, [options]);

    const connectWebSocket = useCallback(() => {
        if (!configRef.current) return;

        try {
            const sdkConfig = configRef.current;
            const ws = new WebSocket(`${sdkConfig.websocketUrl}/stt?sessionId=${sessionId}&key=${sdkConfig.credentials?.apiKey}`);

            ws.onopen = () => setError(null);

            ws.onmessage = (event) => {
                try {
                    const { data } = JSON.parse(event.data);
                    if (data.type === 'transcription') {
                        const newText = data.text || '';
                        const isFinal = data.is_final || false;
                        const speaker_id = data.speaker_id || '';
                        const timestamp = data.timestamp || '';
                        const template = transcriptTemplate || '{timestamp} {speaker_id}: {text}';

                        const transcript = template
                            .replace('{timestamp}', timestamp)
                            .replace('{speaker_id}', speaker_id)
                            .replace('{text}', newText);

                        if (isFinal) {
                            setTranscript((prev) => prev + transcript + '\\n');
                        }
                        onTranscript(transcript, isFinal);
                    }
                } catch (err) {
                    console.error('WebSocket message error:', err);
                }
            };

            ws.onerror = () => {
                const errorInfo = createError('WEBSOCKET_FAILED', 'WebSocket connection failed', 'network');
                setError(errorInfo);
                onError?.(errorInfo);
            };

            ws.onclose = () => {
                websocketRef.current = null;
            };

            websocketRef.current = ws;
        } catch (err) {
            const errorInfo = createError('WEBSOCKET_SETUP_FAILED', 'Failed to setup WebSocket', 'network');
            setError(errorInfo);
            onError?.(errorInfo);
        }
    }, [onTranscript, onError]);

    const startTranscription = useCallback(async () => {
        try {
            if (!configRef.current) {
                throw new Error('Configuration not initialized');
            }

            const sdkConfig = configRef.current;

            const response = await fetch(`${sdkConfig.apiEndpoint}/api/stt/start_session`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': `${sdkConfig.credentials?.apiKey}`

                },
                body: JSON.stringify({
                    session_id: sessionId,
                    language: language,
                    audioSettings: sdkConfig.audioSettings,
                }),
            });

            if (!response.ok) {
                throw new Error(`Session start failed: ${response.statusText}`);
            }

            connectWebSocket();

            const newSession: ArcaSpeechToTextSession = session || {
                id: sessionId,
                startTime: Date.now(),
                transcript: '',
                audioChunks: [],
            };

            setSession(newSession);
            setError(null);
        } catch (err) {
            const errorInfo = createError('SESSION_START_FAILED', err instanceof Error ? err.message : 'Failed to start session');
            setError(errorInfo);
            onError?.(errorInfo);
        }
    }, [connectWebSocket, onError, session]);

    const stopTranscription = useCallback(async () => {
        if (!session || !configRef.current) return;

        try {
            const sdkConfig = configRef.current;

            // Check if we have audio chunks to send
            if (session.audioChunks.length === 0) {
                console.warn('No audio chunks to send');
                const response = await fetch(`${sdkConfig.apiEndpoint}/api/stt/stop_session`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-api-key': `${sdkConfig.credentials?.apiKey}`
                    },
                    body: JSON.stringify({
                        session_id: session.id,
                    }),
                });

                if (!response.ok) {
                    console.warn(`Session stop warning: ${response.statusText}`);
                }
            } else {
                // Create blob from audio chunks - use generic binary type since we're sending raw PCM data
                const audioBlob = new Blob(session.audioChunks, { type: 'application/octet-stream' });

                const formData = new FormData();
                formData.append('session_id', session.id);
                formData.append('audio_file', audioBlob, `session_${session.id}.pcm`);

                const response = await fetch(`${sdkConfig.apiEndpoint}/api/stt/stop_session`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'x-api-key': `${sdkConfig.credentials?.apiKey}`
                    }
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`Session stop failed: ${response.status} ${response.statusText}`, errorText);
                    throw new Error(`Session stop failed: ${response.status} ${response.statusText}`);
                }
            }

            if (websocketRef.current?.readyState === WebSocket.OPEN) {
                websocketRef.current.close();
            }

            const updatedSession = {
                ...session,
                endTime: Date.now(),
                transcript,
            };

            setSession(updatedSession);
        } catch (err) {
            const errorInfo = createError('SESSION_STOP_FAILED', err instanceof Error ? err.message : 'Failed to stop session');
            setError(errorInfo);
            onError?.(errorInfo);
        }
    }, [session, transcript, onError]);

    const sendAudioData = useCallback(
        (audioData: ArrayBuffer) => {
            if (!session || !websocketRef.current) return;

            try {
                if (websocketRef.current.readyState === WebSocket.OPEN) {
                    websocketRef.current.send(audioData);

                    setSession((prev) =>
                        prev
                            ? {
                                  ...prev,
                                  audioChunks: [...prev.audioChunks, audioData],
                              }
                            : null,
                    );
                }
            } catch (err) {
                const errorInfo = createError('AUDIO_SEND_FAILED', 'Failed to send audio data', 'audio');
                setError(errorInfo);
                onError?.(errorInfo);
            }
        },
        [session, onError],
    );

    const uploadAudioFile = useCallback(async (file: File, language: string = 'en-US'): Promise<string> => {
        if (!configRef.current) {
            throw new Error('Configuration not initialized');
        }

        try {
            setIsUploading(true);
            setUploadProgress(0);
            setError(null);

            const sdkConfig = configRef.current;
            const formData = new FormData();
            formData.append('audio_file', file);
            formData.append('language', language);

            const xhr = new XMLHttpRequest();
            
            return new Promise((resolve, reject) => {
                xhr.upload.addEventListener('progress', (event) => {
                    if (event.lengthComputable) {
                        const progress = Math.round((event.loaded * 100) / event.total);
                        setUploadProgress(progress);
                    }
                });

                xhr.addEventListener('load', () => {
                    setIsUploading(false);
                    setUploadProgress(100);

                    if (xhr.status === 200 || xhr.status === 202) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response.task_id);
                        } catch (err) {
                            const errorInfo = createError('UPLOAD_RESPONSE_PARSE_FAILED', 'Failed to parse upload response');
                            setError(errorInfo);
                            onError?.(errorInfo);
                            reject(errorInfo);
                        }
                    } else {
                        const errorInfo = createError('UPLOAD_FAILED', `Upload failed: ${xhr.status} ${xhr.statusText}`);
                        setError(errorInfo);
                        onError?.(errorInfo);
                        reject(errorInfo);
                    }
                });

                xhr.addEventListener('error', () => {
                    setIsUploading(false);
                    const errorInfo = createError('UPLOAD_NETWORK_ERROR', 'Network error during file upload');
                    setError(errorInfo);
                    onError?.(errorInfo);
                    reject(errorInfo);
                });

                xhr.open('POST', `${sdkConfig.apiEndpoint}/api/stt/audio/transcriptions`);
                xhr.setRequestHeader('x-api-key', sdkConfig.credentials?.apiKey || '');
                xhr.send(formData);
            });
        } catch (err) {
            setIsUploading(false);
            const errorInfo = createError('UPLOAD_SETUP_FAILED', err instanceof Error ? err.message : 'Failed to setup file upload');
            setError(errorInfo);
            onError?.(errorInfo);
            throw errorInfo;
        }
    }, [language, onError]);

    const getTranscriptionStatus = useCallback(async (taskId: string): Promise<TranscriptionTask> => {
        if (!configRef.current) {
            throw new Error('Configuration not initialized');
        }

        try {
            const sdkConfig = configRef.current;
            const response = await fetch(`${sdkConfig.apiEndpoint}/api/stt/audio/transcriptions/status/${taskId}`, {
                method: 'GET',
                headers: {
                    'x-api-key': sdkConfig.credentials?.apiKey || ''
                }
            });

            if (!response.ok) {
                throw new Error(`Status check failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            return data;
        } catch (err) {
            const errorInfo = createError('STATUS_CHECK_FAILED', err instanceof Error ? err.message : 'Failed to check transcription status');
            setError(errorInfo);
            onError?.(errorInfo);
            throw errorInfo;
        }
    }, [onError]);

    useEffect(() => {
        return () => {
            if (websocketRef.current) {
                websocketRef.current.close();
            }
        };
    }, []);

    return {
        transcript,
        error,
        startTranscription,
        stopTranscription,
        sendAudioData,
        uploadAudioFile,
        getTranscriptionStatus,
        isUploading,
        uploadProgress,
    };
}
