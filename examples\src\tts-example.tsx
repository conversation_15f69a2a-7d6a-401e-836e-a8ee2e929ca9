/* eslint-disable */
import { useState, useEffect } from 'react';
import { useArcaTextToSpeech, useArcaSessionManager, useSMR } from '@arcaai/agentic-sdk';
import type { ErrorInfo } from '@arcaai/agentic-sdk';
import { SDK_CONFIG_OPTIONS } from './assets/constants';

// Language to voice mapping
const LANGUAGE_VOICE_MAP: Record<string, string> = {
    en: 'en-US-JennyNeural',
    hi: 'hi-IN-SwaraNeural',
    ta: 'ta-IN-PallaviNeural',
    ml: 'ml-IN-SobhanaNeural',
};

// eslint-disable-next-line react-refresh/only-export-components
const TTSExample = () => {
  const [text, setText] = useState<string>('Hello! This is a test of the Text-to-Speech service.');
  const [language, setLanguage] = useState<string>('en');
  const [showDownloadButton, setShowDownloadButton] = useState<boolean>(false);

  // Session Manager state
  const [doctorName, setDoctorName] = useState<string>('Dr. <PERSON>');
  const [patientName, setPatientName] = useState<string>('John Doe');
  const [sessionIdInput, setSessionIdInput] = useState<string>('');
  const [lastProcessedAudioCount, setLastProcessedAudioCount] = useState<number>(0);
  const [isUpdatingSession, setIsUpdatingSession] = useState<boolean>(false);
  
  // SMR state
  const [summaryText, setSummaryText] = useState<string>('This is a medical conversation summary that will be read aloud using text-to-speech.');
  const [currentSummary, setCurrentSummary] = useState<any>(null);
  const [summaryProgress, setSummaryProgress] = useState<number>(0);
  const [readSummaryAloud, setReadSummaryAloud] = useState<boolean>(false);

  // Use Session Manager hook
  const {
    session,
    isLoading: sessionLoading,
    error: sessionError,
    createSession,
    startSession,
    pauseSession,
    resumeSession,
    endSession,
    loadSession,
    clearError,
        updateSession,
  } = useArcaSessionManager({
    doctorId: 'doctor-123',
    doctorName: doctorName,
    patientId: 'patient-456',
    patientName: patientName,
    options: {
      apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
      credentials: SDK_CONFIG_OPTIONS.credentials
    },
    onError: (error: ErrorInfo) => {
      console.error('Session Manager Error:', error);
        },
  });

  // SMR (Summarization) hook
  const smr = useSMR({
    apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
    sessionId: session?.id,
    options: {
      credentials: SDK_CONFIG_OPTIONS.credentials
    },
    onProgress: (progress: number) => {
      setSummaryProgress(progress);
    },
    onComplete: (summary: any) => {
      setCurrentSummary(summary);
      setSummaryProgress(100);
      
      // Auto-read summary aloud if enabled
      if (readSummaryAloud && summary.summary) {
        handleReadSummary(summary.summary);
      }
    },
    onError: (error: ErrorInfo) => {
      console.error('SMR Error:', error);
    }
  });

  // Use the TTS hook from agentic-sdk with all its features
  // Use type assertion to handle the downloadUrl that TypeScript doesn't know about
  const ttsHook = useArcaTextToSpeech({
        apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
        socketPath: '/tts', // WebSocket path for TTS gateway
        language: language,
        storeAudio: true, // Enable audio storage for potential download
        options: {
            credentials: SDK_CONFIG_OPTIONS.credentials
        }
  });

  // Extract values from the TTS hook
  const sendTextData = ttsHook[0];
  const loading = ttsHook[1];
  const error = ttsHook[2];
  const connect = ttsHook[3];
  const disconnect = ttsHook[4];
  const isConnected = ttsHook[5];
  const audioData = ttsHook[6];
  const downloadAudio = ttsHook[7];
    const downloadUrl = ttsHook[8]; // Extract downloadUrl

  // Check if TTS can be used (session must be active)
  const canUseTTS = session?.status === 'ACTIVE' && isConnected;

  // Check if TTS connection is allowed (session must be active)
  const canConnectTTS = session?.status === 'ACTIVE';

  // Handle session creation
  const handleCreateSession = async () => {
    try {
      await createSession({
        sessionType: 'consultation',
        priority: 'medium',
        tags: ['tts-session', 'medical-consultation'],
        customFields: {
          ttsLanguage: language,
                    createdFrom: 'tts-example',
                },
      });
      console.log('✅ Session created successfully');
    } catch (error) {
      console.error('❌ Failed to create session:', error);
    }
  };

  // Handle session start
  const handleStartSession = async () => {
    try {
      await startSession();
      console.log('✅ Session started successfully');
    } catch (error) {
      console.error('❌ Failed to start session:', error);
    }
  };

  // Handle session load
  const handleLoadSession = async () => {
    if (!sessionIdInput.trim()) {
      alert('Please enter a session ID');
      return;
    }

    try {
      await loadSession(sessionIdInput.trim());
      console.log('✅ Session loaded successfully:', sessionIdInput);
    } catch (error) {
      console.error('❌ Failed to load session:', error);
      alert('Failed to load session. Check console for details.');
    }
  };

  // Auto-update session when new audio data is available
  useEffect(() => {
    const updateSessionWithAudioData = async () => {
      if (
        session?.status === 'ACTIVE' &&
        audioData &&
        audioData.length > 0 &&
        audioData.length > lastProcessedAudioCount &&
        !loading // Only update when TTS is not currently processing
      ) {
        try {
          setIsUpdatingSession(true);
          const totalAudioBytes = audioData.reduce((total: number, chunk: Uint8Array) => total + chunk.length, 0);

          // Convert audio data to base64 for storage
          const audioBlob = new Blob(audioData, { type: 'audio/wav' });
          const arrayBuffer = await audioBlob.arrayBuffer();
          const uint8Array = new Uint8Array(arrayBuffer);

          // Safe base64 conversion that works with large files
          let base64Audio = '';
          // Process in chunks to avoid call stack size exceeded
          const chunkSize = 8192;
          for (let i = 0; i < uint8Array.length; i += chunkSize) {
            const chunk = uint8Array.subarray(i, i + chunkSize);
            base64Audio += String.fromCharCode.apply(null, Array.from(chunk));
          }
          base64Audio = btoa(base64Audio);

          const sessionUpdateData = {
            ttsData: {
              lastSynthesisText: text,
              lastSynthesisLanguage: language,
              lastSynthesisVoice: LANGUAGE_VOICE_MAP[language],
              lastSynthesisTime: new Date().toISOString(),
              audioChunksCount: audioData.length,
              totalAudioBytes: totalAudioBytes,
              audioFormat: 'wav',
              audioData: base64Audio, // Store the actual audio data
              audioMetadata: {
                duration: audioBlob.size > 0 ? 'estimated' : 'unknown',
                encoding: 'base64',
                                mimeType: 'audio/wav',
                            },
            },
            lastActivity: new Date().toISOString(),
            activityLog: [
              ...(Array.isArray(session?.metadata?.customFields?.activityLog) ? session.metadata.customFields.activityLog : []),
              {
                type: 'tts_synthesis',
                timestamp: new Date().toISOString(),
                text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
                language: language,
                audioSize: totalAudioBytes,
                chunksCount: audioData.length,
                                hasAudioData: true,
                            },
                        ],
          };

          await updateSession(sessionUpdateData);
          setLastProcessedAudioCount(audioData.length);
          console.log('✅ Session auto-updated with TTS data and audio:', {
            audioChunks: audioData.length,
            totalBytes: totalAudioBytes,
            audioDataSize: base64Audio.length,
                        text: text.substring(0, 50) + '...',
          });
        } catch (updateError) {
          console.error('❌ Failed to auto-update session with TTS data:', updateError);
        } finally {
          setIsUpdatingSession(false);
        }
      }
    };

    // Debounce the update to avoid too frequent calls
    const timeoutId = setTimeout(updateSessionWithAudioData, 1000);
    return () => clearTimeout(timeoutId);
  }, [audioData, session, loading, text, language, lastProcessedAudioCount, updateSession]);

  // SMR helper functions
  const handleSummarize = async () => {
    if (!summaryText.trim()) {
      alert('No text available to summarize');
      return;
    }

    try {
      setSummaryProgress(0);
      const summary = await smr.summarizeSync({
        text: summaryText,
        sessionId: session?.id,
        provider: 'ollama',
        model: 'qwen2.5:0.5b',
        language: language
      });
      
      console.log('Summarization completed:', summary);
      setSummaryProgress(100);
    } catch (error) {
      console.error('Summarization error:', error);
    }
  };

  const handleReadSummary = async (text: string) => {
    try {
      console.log('🔊 Reading summary aloud:', text.substring(0, 50) + '...');
      
      // Send the summary text to TTS
      sendTextData(text, {
        voice: LANGUAGE_VOICE_MAP[language]
      });
      
      console.log('📤 Summary TTS request sent successfully');
    } catch (error) {
      console.error('❌ Summary TTS failed:', error);
    }
  };

  // Handle form submission for TTS
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!text || !canUseTTS) {
        console.log('❌ Cannot submit:', { text: !!text, canUseTTS });
        return;
    }

    try {
        // Show download button immediately after clicking Stream Speech
        setShowDownloadButton(true);
        console.log('💾 Setting showDownloadButton to true');

        console.log('🎤 Starting TTS synthesis with text:', text.substring(0, 50) + '...');
        console.log('📊 Current state:', { isConnected, loading, canUseTTS });

        // Send the text to the TTS service
        sendTextData(text, {
                voice: LANGUAGE_VOICE_MAP[language],
        });

        console.log('📤 TTS request sent successfully');
    } catch (error) {
        console.error('❌ TTS synthesis failed:', error);
        // Hide download button if there's an error
        setShowDownloadButton(false);
    }
  };

  // Handle download
    const handleDownload = async () => {
    console.log('📥 Download requested. Current state:', {
      hasAudioData: !!audioData,
            audioDataLength: audioData?.length || 0,
            downloadUrl: downloadUrl,
            hasDownloadAudioFunction: typeof downloadAudio === 'function',
    });

    if (!audioData || audioData.length === 0) {
      alert('No audio available to download. Please generate speech first.');
      return;
    }

    try {
      console.log('💾 Preparing audio for download...');
            console.log('📊 Audio data details:', {
                chunks: audioData.length,
                totalBytes: audioData.reduce((total: number, chunk: Uint8Array) => total + chunk.length, 0),
                firstChunkType: audioData[0]?.constructor?.name,
                firstChunkSize: audioData[0]?.length,
                firstChunkMagicBytes: Array.from(audioData[0]?.slice(0, 8) || []).map(b => b.toString(16).padStart(2, '0')).join(' ')
            });

            // Use the enhanced SDK downloadAudio function
      const url = await downloadAudio();
            console.log('🔗 downloadAudio() returned:', url);

      if (!url) {
        console.error('❌ Failed to generate download URL');
                alert('Failed to prepare audio for download. Please try again.');
        return;
      }

      console.log('✅ Generated download URL:', url);

            // Extract file extension from the URL or use default
            let fileExtension = 'mp3';
            try {
                const urlObj = new URL(url, window.location.href);
                const pathname = urlObj.pathname;
                const lastDot = pathname.lastIndexOf('.');
                if (lastDot > -1) {
                    fileExtension = pathname.substring(lastDot + 1);
                }
            } catch (urlError) {
                console.log('Could not parse URL for extension, using default');
            }

      // Create download link
      const link = document.createElement('a');
      link.href = url;
            link.download = `tts-${language}-${Date.now()}.${fileExtension}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
            if (url.startsWith('blob:')) {
      URL.revokeObjectURL(url);
            }
            console.log('✅ Download initiated with detected extension:', fileExtension);
    } catch (error) {
      console.error('❌ Download failed:', error);
            alert('Failed to download audio. Check the browser console for detailed error information.');
    }
  };

  // Debug info logging
  useEffect(() => {
    console.log('🔄 TTS Hook State Updated:', {
      isConnected,
      loading,
      error,
      audioDataChunks: audioData?.length || 0,
      totalAudioBytes: audioData?.reduce((total: number, chunk: Uint8Array) => total + chunk.length, 0) || 0,
            showDownloadButton,
    });

    if (audioData && audioData.length > 0) {
      console.log('🎵 Audio Data Available:', {
        chunks: audioData.length,
        totalBytes: audioData.reduce((total: number, chunk: Uint8Array) => total + chunk.length, 0),
        firstChunkSize: audioData[0]?.length || 0,
                lastChunkSize: audioData[audioData.length - 1]?.length || 0,
      });
    }
  }, [isConnected, loading, error, audioData, showDownloadButton]);

  // Session debug logging
  useEffect(() => {
    console.log('🔄 Session State Updated:', {
      sessionId: session?.id,
      status: session?.status,
      sessionLoading,
      sessionError,
      canConnectTTS,
            canUseTTS,
    });
  }, [session, sessionLoading, sessionError, canConnectTTS, canUseTTS]);

  if (sessionLoading) {
    return (
            <div
                style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '20px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        maxWidth: '800px',
        margin: '0 auto',
                    textAlign: 'center',
                }}
            >
        <h2>Initializing Session Manager...</h2>
        <p>Please wait while we set up the session manager.</p>
                <div
                    style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #007bff',
          animation: 'spin 1s linear infinite',
                        margin: '20px auto',
                    }}
                />
      </div>
    );
  }

  return (
        <div
            style={{
      backgroundColor: 'white',
      borderRadius: '8px',
      padding: '20px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      maxWidth: '800px',
                margin: '0 auto',
            }}
        >
      <h2 style={{ marginTop: 0, color: '#333' }}>Text-to-Speech with Session Management</h2>

      {/* Session Management Section */}
            <div
                style={{
        backgroundColor: '#f8f9fa',
        border: '1px solid #dee2e6',
        borderRadius: '6px',
        padding: '15px',
                    marginBottom: '20px',
                }}
            >
        <h3 style={{ marginTop: 0, color: '#495057' }}>Session Management</h3>

        {/* Session Status */}
        <div style={{ marginBottom: '15px' }}>
          <span style={{ fontWeight: 'bold' }}>Session Status: </span>
                    <span
                        style={{
                            color:
                                session?.status === 'ACTIVE'
                                    ? '#28a745'
                                    : session?.status === 'PAUSED'
                                      ? '#ffc107'
                                      : session?.status === 'IDLE'
                                        ? '#17a2b8'
                                        : '#6c757d',
                            fontWeight: 'bold',
                        }}
                    >
            {session?.status || 'No Session'}
          </span>
                    {session && <span style={{ marginLeft: '10px', fontSize: '0.9em', color: '#6c757d' }}>ID: {session.id.substring(0, 8)}...</span>}
        </div>

        {/* Doctor and Patient Info */}
                <div
                    style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '10px',
                        marginBottom: '15px',
                    }}
                >
          <div>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Doctor Name:</label>
            <input
              type="text"
              value={doctorName}
              onChange={(e) => setDoctorName(e.target.value)}
              disabled={!!session}
              style={{
                width: '100%',
                padding: '8px',
                borderRadius: '4px',
                border: '1px solid #ced4da',
                                backgroundColor: session ? '#e9ecef' : 'white',
              }}
            />
          </div>
          <div>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Patient Name:</label>
            <input
              type="text"
              value={patientName}
              onChange={(e) => setPatientName(e.target.value)}
              disabled={!!session}
              style={{
                width: '100%',
                padding: '8px',
                borderRadius: '4px',
                border: '1px solid #ced4da',
                                backgroundColor: session ? '#e9ecef' : 'white',
              }}
            />
          </div>
        </div>

        {/* Session Actions */}
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          {!session && (
            <button
              onClick={handleCreateSession}
              disabled={sessionLoading}
              style={{
                padding: '8px 15px',
                borderRadius: '4px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
              }}
            >
              Create Session
            </button>
          )}

          {session && session.status === 'IDLE' && (
            <button
              onClick={handleStartSession}
              disabled={sessionLoading}
              style={{
                padding: '8px 15px',
                borderRadius: '4px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
              }}
            >
              Start Session
            </button>
          )}

          {session && session.status === 'ACTIVE' && (
            <button
              onClick={() => pauseSession('User requested pause')}
              disabled={sessionLoading}
              style={{
                padding: '8px 15px',
                borderRadius: '4px',
                backgroundColor: '#ffc107',
                color: 'black',
                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
              }}
            >
              Pause Session
            </button>
          )}

          {session && session.status === 'PAUSED' && (
            <button
              onClick={resumeSession}
              disabled={sessionLoading}
              style={{
                padding: '8px 15px',
                borderRadius: '4px',
                backgroundColor: '#17a2b8',
                color: 'white',
                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
              }}
            >
              Resume Session
            </button>
          )}

          {session && (
            <button
              onClick={endSession}
              disabled={sessionLoading}
              style={{
                padding: '8px 15px',
                borderRadius: '4px',
                backgroundColor: '#dc3545',
                color: 'white',
                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
              }}
            >
              End Session
            </button>
          )}
        </div>

        {/* Load Existing Session */}
        <div style={{ marginTop: '15px', paddingTop: '15px', borderTop: '1px solid #dee2e6' }}>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Load Existing Session:</label>
          <div style={{ display: 'flex', gap: '10px' }}>
            <input
              type="text"
              value={sessionIdInput}
              onChange={(e) => setSessionIdInput(e.target.value)}
              placeholder="Enter session ID..."
              disabled={!!session}
              style={{
                flex: 1,
                padding: '8px',
                borderRadius: '4px',
                border: '1px solid #ced4da',
                                backgroundColor: session ? '#e9ecef' : 'white',
              }}
            />
            <button
              onClick={handleLoadSession}
              disabled={!!session || !sessionIdInput.trim() || sessionLoading}
              style={{
                padding: '8px 15px',
                borderRadius: '4px',
                backgroundColor: '#6f42c1',
                color: 'white',
                border: 'none',
                                cursor: !!session || !sessionIdInput.trim() || sessionLoading ? 'not-allowed' : 'pointer',
              }}
            >
              Load
            </button>
          </div>
        </div>

        {/* Session Error */}
        {sessionError && (
                    <div
                        style={{
            marginTop: '10px',
            padding: '10px',
            backgroundColor: '#f8d7da',
            color: '#721c24',
            borderRadius: '4px',
                            border: '1px solid #f5c6cb',
                        }}
                    >
            <strong>Session Error:</strong> {sessionError.message}
            <button
              onClick={clearError}
              style={{
                marginLeft: '10px',
                padding: '2px 8px',
                fontSize: '0.8em',
                backgroundColor: 'transparent',
                border: '1px solid #721c24',
                borderRadius: '3px',
                color: '#721c24',
                                cursor: 'pointer',
              }}
            >
              Clear
            </button>
          </div>
        )}
      </div>

      {/* TTS Section - Only enabled when session is active */}
            <div
                style={{
        opacity: canConnectTTS ? 1 : 0.5,
                    pointerEvents: canConnectTTS ? 'auto' : 'none',
                }}
            >
        <h3 style={{ color: '#495057' }}>Text-to-Speech</h3>

        {!canConnectTTS && (
                    <div
                        style={{
            marginBottom: '15px',
            padding: '10px',
            backgroundColor: '#fff3cd',
            color: '#856404',
            borderRadius: '4px',
                            border: '1px solid #ffeaa7',
                        }}
                    >
            <strong>Notice:</strong> Please create and start a session to use Text-to-Speech.
          </div>
        )}

        {canConnectTTS && !isConnected && (
                    <div
                        style={{
            marginBottom: '15px',
            padding: '10px',
            backgroundColor: '#d1ecf1',
            color: '#0c5460',
            borderRadius: '4px',
                            border: '1px solid #bee5eb',
                        }}
                    >
            <strong>Ready:</strong> Session is active! Please connect to TTS server to start speech synthesis.
          </div>
        )}

        <div style={{ marginBottom: '20px' }}>
          <div className="connection-status">
            <span>TTS Status: </span>
                        <span className={`status ${isConnected ? 'connected' : 'disconnected'}`}>{isConnected ? 'Connected' : 'Disconnected'}</span>
                        <button className="connection-button" onClick={isConnected ? disconnect : connect} disabled={loading || !canConnectTTS}>
              {isConnected ? 'Disconnect' : 'Connect'}
            </button>
          </div>

          <textarea
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="Enter text to synthesize..."
            style={{
              width: '100%',
              minHeight: '100px',
              padding: '10px',
              borderRadius: '4px',
              border: '1px solid #ced4da',
              marginBottom: '10px',
                            fontFamily: 'inherit',
            }}
          />
        </div>

                <div
                    style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '15px',
                        marginBottom: '20px',
                    }}
                >
          <div>
            <label htmlFor="language-select" style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              Language:
            </label>
            <select
              id="language-select"
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                borderRadius: '4px',
                                border: '1px solid #ced4da',
              }}
            >
              <option value="en">English</option>
              <option value="hi">Hindi</option>
              <option value="ta">Tamil</option>
              <option value="ml">Malayalam</option>
            </select>
          </div>
        </div>

        <div style={{ display: 'flex', gap: '10px' }}>
          <button
            type="button"
            disabled={loading || !canUseTTS}
            style={{
              flex: 1,
              padding: '10px',
              borderRadius: '4px',
              backgroundColor: loading || !canUseTTS ? '#6c757d' : '#007bff',
              color: 'white',
              border: 'none',
                            cursor: loading || !canUseTTS ? 'not-allowed' : 'pointer',
            }}
            onClick={handleSubmit}
          >
            {loading ? 'Streaming...' : 'Stream Speech'}
          </button>

            <button
              type="button"
              disabled={!audioData || audioData.length === 0}
              style={{
                            padding: '10px 20px',
                borderRadius: '4px',
                            backgroundColor: !audioData || audioData.length === 0 ? '#6c757d' : '#28a745',
                color: 'white',
                border: 'none',
                            cursor: !audioData || audioData.length === 0 ? 'not-allowed' : 'pointer',
                            fontWeight: 'bold',
                            fontSize: '14px',
                            transition: 'all 0.2s ease',
                            boxShadow: !audioData || audioData.length === 0 ? 'none' : '0 2px 4px rgba(40, 167, 69, 0.3)',
              }}
              onClick={handleDownload}
                        onMouseOver={(e) => {
                            if (audioData && audioData.length > 0) {
                                e.currentTarget.style.backgroundColor = '#218838';
                                e.currentTarget.style.transform = 'translateY(-1px)';
                            }
                        }}
                        onMouseOut={(e) => {
                            if (audioData && audioData.length > 0) {
                                e.currentTarget.style.backgroundColor = '#28a745';
                                e.currentTarget.style.transform = 'translateY(0)';
                            }
                        }}
            >
                        {!audioData || audioData.length === 0 ? '⚠️ No Audio' : '📥 Download Audio'}
            </button>

                    <button
                        type="button"
                        disabled={!audioData || audioData.length === 0}
                        style={{
                            padding: '10px 20px',
                            borderRadius: '4px',
                            backgroundColor: !audioData || audioData.length === 0 ? '#6c757d' : '#17a2b8',
                            color: 'white',
                            border: 'none',
                            cursor: !audioData || audioData.length === 0 ? 'not-allowed' : 'pointer',
                            fontWeight: 'bold',
                            fontSize: '14px',
                            transition: 'all 0.2s ease',
                        }}
                        onClick={async () => {
                            if (!audioData || audioData.length === 0) return;
                            
                            console.log('🧪 Testing audio data...');
                            
                            // Create different format blobs to test
                            const formats = [
                                { type: 'audio/mpeg', ext: 'mp3' },
                                { type: 'audio/mp3', ext: 'mp3' },
                                { type: 'audio/wav', ext: 'wav' },
                                { type: 'audio/ogg', ext: 'ogg' },
                                { type: 'audio/webm', ext: 'webm' }
                            ];
                            
                            const firstChunk = audioData[0];
                            const magicBytes = Array.from(firstChunk?.slice(0, 8) || []);
                            console.log('🔍 Magic bytes:', magicBytes.map(b => b.toString(16).padStart(2, '0')).join(' '));
                            
                            // Create combined audio data
                            const totalSize = audioData.reduce((total, chunk) => total + chunk.length, 0);
                            console.log('📊 Total audio size:', totalSize, 'bytes');
                            
                            for (const format of formats) {
                                const blob = new Blob(audioData, { type: format.type });
                                const url = URL.createObjectURL(blob);
                                const audio = new Audio();
                                
                                console.log(`🧪 Testing format: ${format.type}`);
                                
                                const testPromise = new Promise((resolve) => {
                                    const timeout = setTimeout(() => {
                                        audio.src = '';
                                        URL.revokeObjectURL(url);
                                        resolve({ format: format.type, status: 'timeout' });
                                    }, 2000);
                                    
                                    audio.oncanplaythrough = () => {
                                        clearTimeout(timeout);
                                        console.log(`✅ ${format.type}: Can play through, duration: ${audio.duration}s`);
                                        URL.revokeObjectURL(url);
                                        resolve({ format: format.type, status: 'success', duration: audio.duration });
                                    };
                                    
                                    audio.onerror = (error) => {
                                        clearTimeout(timeout);
                                        console.log(`❌ ${format.type}: Error`, error);
                                        URL.revokeObjectURL(url);
                                        resolve({ format: format.type, status: 'error', error });
                                    };
                                    
                                    audio.onloadeddata = () => {
                                        console.log(`📊 ${format.type}: Data loaded`);
                                    };
                                    
                                    audio.onloadedmetadata = () => {
                                        console.log(`📊 ${format.type}: Metadata loaded - duration: ${audio.duration}s`);
                                    };
                                    
                                    audio.src = url;
                                    audio.load();
                                });
                                
                                const result = await testPromise as { format: string; status: string; duration?: number; error?: any };
                                console.log(`🧪 Test result for ${format.type}:`, result);
                                
                                if (result.status === 'success' && result.duration && result.duration > 0) {
                                    console.log(`✅ Found working format: ${format.type}`);
                                    alert(`Audio test successful! Format: ${format.type}, Duration: ${result.duration.toFixed(2)}s. The audio should be playable.`);
                                    return;
                                }
                            }
                            
                            alert('Audio test completed. Check browser console for detailed results. The audio may not be in a supported format.');
                        }}
                    >
                        🧪 Test Audio
                    </button>
        </div>

        {/* Audio Data Status */}
        {audioData && audioData.length > 0 && (
                    <div
                        style={{
            padding: '10px',
            backgroundColor: '#e8f5e8',
            border: '1px solid #28a745',
                            borderRadius: '4px',
                        }}
                    >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span style={{ fontWeight: 'bold', color: '#155724' }}>🎵 Audio Ready</span>
              <span style={{ fontSize: '0.9em', color: '#155724' }}>
                                {audioData.length} chunks •{' '}
                                {Math.round(audioData.reduce((total: number, chunk: Uint8Array) => total + chunk.length, 0) / 1024)} KB
              </span>
            </div>
            {isUpdatingSession && (
                            <div
                                style={{
                marginTop: '8px',
                fontSize: '0.85em',
                color: '#155724',
                display: 'flex',
                alignItems: 'center',
                                    gap: '5px',
                                }}
                            >
                                <div
                                    style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: '#28a745',
                                        animation: 'pulse 1s infinite',
                                    }}
                                />
                Updating session with audio data...
              </div>
            )}
          </div>
        )}

        {loading && (
          <div
            style={{
              width: '20px',
              height: '20px',
              borderRadius: '50%',
              backgroundColor: '#007bff',
              animation: 'pulse 1s infinite',
              marginLeft: '10px',
              marginTop: '10px',
            }}
          />
        )}

                {error && <div className="error-message">{error || 'An error occurred'}</div>}
      </div>
    </div>
  );
};

// Add CSS styles
const styles = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }

  .connection-status {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .connection-status .status {
    font-weight: bold;
    margin-left: 5px;
  }

  .connection-status .status.connected {
    color: green;
  }

  .connection-status .status.disconnected {
    color: red;
  }

  .connection-button {
    margin-left: 10px;
    padding: 3px 8px;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
  }

  .connection-button:hover {
    background-color: #357ae8;
  }

  .connection-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }

  .error-message {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8d7da;
    color: #721c24;
    border-radius: 4px;
  }

  .info-message {
    margin-top: 10px;
    padding: 10px;
    background-color: #cce5ff;
    color: #004085;
    border-radius: 4px;
  }
`;

export default () => {
  // Inject styles
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.innerHTML = styles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  return <TTSExample />;
};
