import type { MedicalSession, RecoveryProgress, SessionRecoveryInfo } from '../types/index';

export interface SessionRecoveryModalOptions {
    title?: string;
    showProgress?: boolean;
    allowSkip?: boolean;
    allowPartialRecovery?: boolean;
    theme?: 'light' | 'dark' | 'auto';
    position?: 'center' | 'top' | 'bottom';
    maxWidth?: string;
    autoClose?: boolean;
    timeout?: number;
}

export interface SessionRecoveryModalCallbacks {
    onRecover: (sessionId: string, options: RecoverySessionOptions) => Promise<MedicalSession>;
    onSkip: () => void;
    onCancel: () => void;
    onProgress?: (progress: RecoveryProgress) => void;
}

export interface RecoverySessionOptions {
    recoveryType: 'full' | 'partial' | 'metadata-only';
    autoRecover: boolean;
    showProgress: boolean;
}

export class SessionRecoveryModal {
    private container: HTMLElement | null = null;
    private overlay: HTMLElement | null = null;
    private modal: HTMLElement | null = null;
    private isVisible: boolean = false;
    private isRecovering: boolean = false;
    private options: SessionRecoveryModalOptions;
    private callbacks: SessionRecoveryModalCallbacks;
    private currentRecoveryInfo: SessionRecoveryInfo | null = null;

    constructor(options: SessionRecoveryModalOptions = {}, callbacks: SessionRecoveryModalCallbacks) {
        this.options = {
            title: 'Session Recovery',
            showProgress: true,
            allowSkip: true,
            allowPartialRecovery: true,
            theme: 'auto',
            position: 'center',
            maxWidth: '600px',
            autoClose: false,
            timeout: 30000,
            ...options
        };
        this.callbacks = callbacks;

        this.createModal();
        this.setupEventListeners();
    }

    public async show(recoveryInfo: SessionRecoveryInfo[]): Promise<void> {
        if (this.isVisible) return;

        this.currentRecoveryInfo = recoveryInfo.length > 0 ? recoveryInfo[0]! : null; // Use first recoverable session for now
        this.updateModalContent();
        this.showModal();
    }

    public hide(): void {
        if (!this.isVisible) return;

        this.hideModal();
        this.isRecovering = false;
    }

    public updateProgress(progress: RecoveryProgress): void {
        if (!this.isVisible || !this.options.showProgress) return;

        this.updateProgressDisplay(progress);

        if (this.callbacks.onProgress) {
            this.callbacks.onProgress(progress);
        }

        // Auto-close on completion if enabled
        if (progress.stage === 'complete' && this.options.autoClose) {
            setTimeout(() => this.hide(), 2000);
        }
    }

    private createModal(): void {
        // Create overlay
        this.overlay = document.createElement('div');
        this.overlay.className = 'agentic-recovery-overlay';
        this.overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            display: none;
            backdrop-filter: blur(4px);
        `;

        // Create modal container
        this.container = document.createElement('div');
        this.container.className = 'agentic-recovery-container';
        this.container.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: ${this.options.maxWidth};
            width: 90%;
            z-index: 10001;
            display: none;
        `;

        // Create modal
        this.modal = document.createElement('div');
        this.modal.className = 'agentic-recovery-modal';
        this.modal.style.cssText = `
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            padding: 32px;
            max-height: 80vh;
            overflow-y: auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.5;
        `;

        // Apply theme
        this.applyTheme();

        // Assemble modal
        this.container.appendChild(this.modal);
        document.body.appendChild(this.overlay);
        document.body.appendChild(this.container);
    }

    private updateModalContent(): void {
        if (!this.modal || !this.currentRecoveryInfo) return;

        const info = this.currentRecoveryInfo;

        this.modal.innerHTML = `
            <div class="agentic-recovery-header">
                <h2 style="margin: 0 0 16px 0; color: #1f2937; font-size: 24px; font-weight: 600;">
                    ${this.options.title}
                </h2>
                <p style="margin: 0 0 24px 0; color: #6b7280; font-size: 16px;">
                    We found a previous session that can be recovered.
                </p>
            </div>

            <div class="agentic-recovery-info" style="margin-bottom: 24px;">
                <div style="background: #f3f4f6; border-radius: 8px; padding: 20px; margin-bottom: 16px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 12px;">
                        <div>
                            <label style="display: block; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 4px;">Session ID</label>
                            <span style="font-family: monospace; color: #1f2937;">${info.sessionId.substring(0, 12)}...</span>
                        </div>
                        <div>
                            <label style="display: block; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 4px;">Last Activity</label>
                            <span style="color: #1f2937;">${this.formatDate(info.lastActivity)}</span>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                        <div>
                            <label style="display: block; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 4px;">Data Integrity</label>
                            <span style="color: ${this.getIntegrityColor(info.dataIntegrity)}; font-weight: 500;">
                                ${info.dataIntegrity.charAt(0).toUpperCase() + info.dataIntegrity.slice(1)}
                            </span>
                        </div>
                        <div>
                            <label style="display: block; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 4px;">Risk Level</label>
                            <span style="color: ${this.getRiskColor(info.riskLevel)}; font-weight: 500;">
                                ${info.riskLevel.charAt(0).toUpperCase() + info.riskLevel.slice(1)}
                            </span>
                        </div>
                    </div>
                </div>

                ${info.missingComponents.length > 0 ? `
                    <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
                        <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 14px; font-weight: 500;">
                            ⚠️ Missing Components
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                            ${info.missingComponents.map(component => `<li>${component}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>

            ${this.options.showProgress ? `
                <div class="agentic-recovery-progress" style="margin-bottom: 24px; display: none;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span style="font-size: 14px; font-weight: 500; color: #1f2937;">Recovery Progress</span>
                        <span class="progress-percentage" style="font-size: 14px; color: #6b7280;">0%</span>
                    </div>
                    <div style="background: #e5e7eb; border-radius: 6px; height: 8px; margin-bottom: 8px;">
                        <div class="progress-bar" style="background: #3b82f6; height: 100%; border-radius: 6px; width: 0%; transition: width 0.3s ease;"></div>
                    </div>
                    <div class="progress-text" style="font-size: 12px; color: #6b7280;">
                        Preparing recovery...
                    </div>
                </div>
            ` : ''}

            <div class="agentic-recovery-actions">
                <div style="display: flex; gap: 12px; justify-content: flex-end;">
                    ${this.options.allowSkip ? `
                        <button class="agentic-btn-skip" style="
                            padding: 12px 20px;
                            border: 1px solid #d1d5db;
                            background: white;
                            color: #6b7280;
                            border-radius: 8px;
                            font-size: 14px;
                            font-weight: 500;
                            cursor: pointer;
                            transition: all 0.2s;
                        ">Skip</button>
                    ` : ''}

                    ${this.options.allowPartialRecovery && info.recoveryType !== 'full' ? `
                        <button class="agentic-btn-partial" style="
                            padding: 12px 20px;
                            border: 1px solid #f59e0b;
                            background: #fef3c7;
                            color: #92400e;
                            border-radius: 8px;
                            font-size: 14px;
                            font-weight: 500;
                            cursor: pointer;
                            transition: all 0.2s;
                        ">Partial Recovery</button>
                    ` : ''}

                    <button class="agentic-btn-recover" style="
                        padding: 12px 20px;
                        border: none;
                        background: #3b82f6;
                        color: white;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s;
                    ">Recover Session</button>
                </div>
            </div>
        `;

        this.setupButtonHandlers();
    }

    private setupButtonHandlers(): void {
        if (!this.modal) return;

        // Recover button
        const recoverBtn = this.modal.querySelector('.agentic-btn-recover') as HTMLButtonElement;
        if (recoverBtn) {
            recoverBtn.addEventListener('click', () => this.handleRecover('full'));
        }

        // Partial recovery button
        const partialBtn = this.modal.querySelector('.agentic-btn-partial') as HTMLButtonElement;
        if (partialBtn) {
            partialBtn.addEventListener('click', () => this.handleRecover('partial'));
        }

        // Skip button
        const skipBtn = this.modal.querySelector('.agentic-btn-skip') as HTMLButtonElement;
        if (skipBtn) {
            skipBtn.addEventListener('click', () => this.handleSkip());
        }
    }

    private async handleRecover(recoveryType: 'full' | 'partial'): Promise<void> {
        if (!this.currentRecoveryInfo || this.isRecovering) return;

        this.isRecovering = true;
        this.showProgress();

        try {
            const options: RecoverySessionOptions = {
                recoveryType,
                autoRecover: true,
                showProgress: this.options.showProgress || false,
            };

            await this.callbacks.onRecover(this.currentRecoveryInfo.sessionId, options);
        } catch (error) {
            this.showError('Recovery failed. Please try again.');
            this.isRecovering = false;
        }
    }

    private handleSkip(): void {
        this.callbacks.onSkip();
        this.hide();
    }

    private showProgress(): void {
        if (!this.modal) return;

        const progressSection = this.modal.querySelector('.agentic-recovery-progress') as HTMLElement;
        const actionsSection = this.modal.querySelector('.agentic-recovery-actions') as HTMLElement;

        if (progressSection) {
            progressSection.style.display = 'block';
        }

        if (actionsSection) {
            actionsSection.style.display = 'none';
        }
    }

    private updateProgressDisplay(progress: RecoveryProgress): void {
        if (!this.modal) return;

        const progressBar = this.modal.querySelector('.progress-bar') as HTMLElement;
        const progressText = this.modal.querySelector('.progress-text') as HTMLElement;
        const progressPercentage = this.modal.querySelector('.progress-percentage') as HTMLElement;

        if (progressBar) {
            progressBar.style.width = `${progress.progress}%`;
        }

        if (progressText) {
            progressText.textContent = progress.currentStep;
        }

        if (progressPercentage) {
            progressPercentage.textContent = `${Math.round(progress.progress)}%`;
        }

        // Handle completion
        if (progress.stage === 'complete') {
            if (progressText) {
                progressText.textContent = 'Recovery completed successfully!';
                progressText.style.color = '#059669';
            }
        }

        // Handle errors
        if (progress.stage === 'failed') {
            if (progressBar) {
                progressBar.style.background = '#ef4444';
            }
            if (progressText) {
                progressText.textContent = 'Recovery failed';
                progressText.style.color = '#dc2626';
            }
        }
    }

    private showError(message: string): void {
        if (!this.modal) return;

        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-size: 14px;
        `;
        errorDiv.textContent = message;

        const header = this.modal.querySelector('.agentic-recovery-header');
        if (header) {
            header.appendChild(errorDiv);
        }
    }

    private applyTheme(): void {
        if (!this.modal) return;

        const theme = this.options.theme === 'auto' ?
            (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') :
            this.options.theme;

        if (theme === 'dark') {
            this.modal.style.background = '#1f2937';
            this.modal.style.color = '#f9fafb';
        }
    }

    private setupEventListeners(): void {
        // Close on overlay click
        if (this.overlay) {
            this.overlay.addEventListener('click', () => {
                if (!this.isRecovering) {
                    this.callbacks.onCancel();
                    this.hide();
                }
            });
        }

        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible && !this.isRecovering) {
                this.callbacks.onCancel();
                this.hide();
            }
        });
    }

    private showModal(): void {
        if (this.overlay) {
            this.overlay.style.display = 'block';
        }
        if (this.container) {
            this.container.style.display = 'block';
        }
        this.isVisible = true;
    }

    private hideModal(): void {
        if (this.overlay) {
            this.overlay.style.display = 'none';
        }
        if (this.container) {
            this.container.style.display = 'none';
        }
        this.isVisible = false;
    }

    private formatDate(date: Date): string {
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (minutes < 1) return 'Just now';
        if (minutes < 60) return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
        if (hours < 24) return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
        if (days < 7) return `${days} day${days !== 1 ? 's' : ''} ago`;

        return date.toLocaleDateString();
    }

    private getIntegrityColor(integrity: string): string {
        switch (integrity) {
            case 'complete': return '#059669';
            case 'partial': return '#f59e0b';
            case 'corrupted': return '#dc2626';
            default: return '#6b7280';
        }
    }

    private getRiskColor(risk: string): string {
        switch (risk) {
            case 'low': return '#059669';
            case 'medium': return '#f59e0b';
            case 'high': return '#dc2626';
            default: return '#6b7280';
        }
    }

    public destroy(): void {
        this.hide();

        if (this.overlay && this.overlay.parentNode) {
            this.overlay.parentNode.removeChild(this.overlay);
        }

        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }

        this.overlay = null;
        this.container = null;
        this.modal = null;
    }
}