import { ApiClient, ApiClientError } from './apiClient';
import { Logger } from '../core/Logger';
import type { SessionData, GetSessionRequest, CreateSessionRequest, UpdateSessionRequest } from '../types/api';

export class SessionApiError extends Error {
    public readonly code: string;
    public readonly statusCode?: number | undefined;
    public readonly details?: Record<string, unknown> | undefined;

    constructor(message: string, code: string, statusCode?: number | undefined, details?: Record<string, unknown> | undefined) {
        super(message);
        this.name = 'SessionApiError';
        this.code = code;
        this.statusCode = statusCode;
        this.details = details;
    }
}

export class SessionApi {
    private apiClient: ApiClient;
    private logger: Logger;
    private basePath: string;

    constructor(apiClient: ApiClient, options?: { basePath?: string }) {
        this.apiClient = apiClient;
        this.logger = new Logger();
        this.basePath = options?.basePath || '/api/sessions';
    }

    private mapHttpError(error: ApiClientError, operation: 'get' | 'create' | 'update' | 'delete', context?: any): SessionApiError {
        const { status, message } = error;

        // Common error mappings
        if (status === 401) {
            return new SessionApiError(`Authentication required to ${operation} session`, 'AUTHENTICATION_REQUIRED', 401);
        }

        if (status === 403) {
            const actionMap = { get: 'access this session', create: 'create sessions', update: 'update this session', delete: 'delete this session' };
            return new SessionApiError(`Not authorized to ${actionMap[operation]}`, 'ACCESS_FORBIDDEN', 403);
        }

        if (status >= 500) {
            const actionMap = { get: 'retrieving', create: 'creating', update: 'updating', delete: 'deleting' };
            return new SessionApiError(`Server error occurred while ${actionMap[operation]} session`, 'SERVER_ERROR', status);
        }

        // Operation-specific error mappings
        switch (operation) {
            case 'get':
                if (status === 404) {
                    const sessionId = context?.sessionId || 'unknown';
                    return new SessionApiError(`Session with ID '${sessionId}' not found`, 'SESSION_NOT_FOUND', 404);
                }
                break;

            case 'create':
                if (status === 400) {
                    return new SessionApiError('Invalid session data provided', 'INVALID_SESSION_DATA', 400, { requestData: context?.requestData });
                }
                if (status === 409) {
                    return new SessionApiError('Session already exists or conflicts with existing data', 'SESSION_CONFLICT', 409);
                }
                break;

            case 'update':
                if (status === 400) {
                    return new SessionApiError('Invalid session update data provided', 'INVALID_UPDATE_DATA', 400, {
                        requestData: context?.requestData,
                    });
                }
                if (status === 404) {
                    const sessionId = context?.sessionId || 'unknown';
                    return new SessionApiError(`Session with ID '${sessionId}' not found`, 'SESSION_NOT_FOUND', 404);
                }
                if (status === 409) {
                    return new SessionApiError('Session update conflicts with existing data', 'UPDATE_CONFLICT', 409);
                }
                break;

            case 'delete':
                if (status === 400) {
                    return new SessionApiError('Invalid session delete request', 'INVALID_DELETE_REQUEST', 400, {
                        sessionId: context?.sessionId,
                        options: context?.options,
                    });
                }
                if (status === 404) {
                    const sessionId = context?.sessionId || 'unknown';
                    return new SessionApiError(`Session with ID '${sessionId}' not found`, 'SESSION_NOT_FOUND', 404);
                }
                if (status === 409) {
                    return new SessionApiError('Session cannot be deleted due to conflicts', 'DELETE_CONFLICT', 409);
                }
                break;
        }

        // Default fallback
        return new SessionApiError(message, 'API_ERROR', status);
    }

    private handleError(error: any, operation: 'get' | 'create' | 'update' | 'delete', context?: any): never {
        // Handle ApiClient errors
        if (error instanceof ApiClientError) {
            this.logger.error(
                `API error while ${operation === 'get' ? 'getting' : operation === 'create' ? 'creating' : operation === 'update' ? 'updating' : 'deleting'} session`,
                {
                    ...context,
                    status: error.status,
                    message: error.message,
                },
            );

            throw this.mapHttpError(error, operation, context);
        }

        // Handle SessionApiError (re-throw)
        if (error instanceof SessionApiError) {
            throw error;
        }

        // Handle unexpected errors
        this.logger.error(
            `Unexpected error while ${operation === 'get' ? 'getting' : operation === 'create' ? 'creating' : operation === 'update' ? 'updating' : 'deleting'} session`,
            {
                ...context,
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
            },
        );

        const actionMap = { get: 'retrieving', create: 'creating', update: 'updating', delete: 'deleting' };

        throw new SessionApiError(`An unexpected error occurred while ${actionMap[operation]} session`, 'UNEXPECTED_ERROR', 500, {
            originalError: error instanceof Error ? error.message : String(error),
        });
    }

    public async getSession(request: GetSessionRequest): Promise<SessionData> {
        try {
            this.logger.info('Getting session', { sessionId: request.id });

            if (!request.id || typeof request.id !== 'string') {
                throw new SessionApiError('Session ID is required and must be a string', 'INVALID_SESSION_ID', 400);
            }

            const response = await this.apiClient.get<SessionData>(`${this.basePath}/${encodeURIComponent(request.id)}`);

            this.logger.info('Session retrieved successfully', {
                sessionId: request.id,
                sessionStatus: response.data.sessionStatus,
                sessionType: response.data.sessionType,
            });

            return response.data;
        } catch (error) {
            this.handleError(error, 'get', { sessionId: request.id });
        }
    }

    public async createSession(request: CreateSessionRequest): Promise<SessionData> {
        try {
            this.logger.info('Creating new session', {
                sessionType: request.sessionType,
                patientId: request.patientId,
                providerId: request.providerId,
            });

            const response = await this.apiClient.post<SessionData>(this.basePath, request);

            this.logger.info('Session created successfully', {
                sessionId: response.data.id,
                sessionStatus: response.data.sessionStatus,
                sessionType: response.data.sessionType,
                patientId: response.data.patientId,
                providerId: response.data.providerId,
            });

            return response.data;
        } catch (error) {
            this.handleError(error, 'create', { sessionType: request.sessionType, requestData: request });
        }
    }

    public async updateSession(sessionId: string, request: UpdateSessionRequest): Promise<SessionData> {
        try {
            this.logger.info('Updating session', {
                sessionId,
                updateFields: Object.keys(request),
            });

            if (!sessionId || typeof sessionId !== 'string') {
                throw new SessionApiError('Session ID is required and must be a string', 'INVALID_SESSION_ID', 400);
            }

            const response = await this.apiClient.put<SessionData>(`${this.basePath}/${encodeURIComponent(sessionId)}`, request);

            this.logger.info('Session updated successfully', {
                sessionId,
                sessionStatus: response.data.sessionStatus,
                sessionType: response.data.sessionType,
                updatedAt: response.data.updatedAt,
            });

            return response.data;
        } catch (error) {
            this.handleError(error, 'update', { sessionId, requestData: request });
        }
    }

    public async deleteSession(sessionId: string, options?: { softDelete?: boolean; deleteReason?: string }): Promise<SessionData> {
        try {
            this.logger.info('Deleting session', {
                sessionId,
                softDelete: options?.softDelete,
                deleteReason: options?.deleteReason,
            });

            if (!sessionId || typeof sessionId !== 'string') {
                throw new SessionApiError('Session ID is required and must be a string', 'INVALID_SESSION_ID', 400);
            }

            const requestBody: any = {};
            if (options?.softDelete !== undefined) {
                requestBody.softDelete = options.softDelete;
            }
            if (options?.deleteReason) {
                requestBody.deleteReason = options.deleteReason;
            }

            const response = await this.apiClient.delete<SessionData>(
                `${this.basePath}/${encodeURIComponent(sessionId)}`,
                Object.keys(requestBody).length > 0 ? requestBody : undefined,
            );

            this.logger.info('Session deleted successfully', {
                sessionId,
                sessionStatus: response.data.sessionStatus,
                deletedAt: response.data.updatedAt,
                softDelete: options?.softDelete,
            });

            return response.data;
        } catch (error) {
            this.handleError(error, 'delete', { sessionId, options });
        }
    }

    public updateApiClient(apiClient: ApiClient): void {
        this.apiClient = apiClient;
    }

    public updateBasePath(basePath: string): void {
        this.basePath = basePath;
    }

    public getBasePath(): string {
        return this.basePath;
    }

    public static isSessionApiError(error: any): error is SessionApiError {
        return error instanceof SessionApiError;
    }

    public static isNotFoundError(error: any): boolean {
        return error instanceof SessionApiError && error.code === 'SESSION_NOT_FOUND';
    }

    public static isAuthError(error: any): boolean {
        return error instanceof SessionApiError && (error.code === 'AUTHENTICATION_REQUIRED' || error.code === 'ACCESS_FORBIDDEN');
    }

    public static isServerError(error: any): boolean {
        return error instanceof SessionApiError && error.statusCode !== undefined && error.statusCode >= 500;
    }

    public static isValidationError(error: any): boolean {
        return (
            error instanceof SessionApiError && error.statusCode === 400 && (error.code.includes('INVALID_') || error.code === 'INVALID_SESSION_DATA')
        );
    }

    public static isConflictError(error: any): boolean {
        return error instanceof SessionApiError && error.code === 'SESSION_CONFLICT';
    }

    public static isUpdateConflictError(error: any): boolean {
        return error instanceof SessionApiError && error.code === 'UPDATE_CONFLICT';
    }

    public static isDeleteConflictError(error: any): boolean {
        return error instanceof SessionApiError && error.code === 'DELETE_CONFLICT';
    }
}
