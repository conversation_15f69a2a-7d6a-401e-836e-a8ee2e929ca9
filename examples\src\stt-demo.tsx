import type { ErrorInfo } from '@arcaai/agentic-sdk';
import { useArcaSessionManager, useArcaSpeechToText, useAudioCapture, useSMR } from '@arcaai/agentic-sdk';
import { useCallback, useEffect, useRef, useState } from 'react';
import { SDK_CONFIG_OPTIONS } from './assets/constants';

export function STTDemo() {
    const [chunk, setChunk] = useState<string>('');
    const [language, setLanguage] = useState<string>('en-US');
    const [fileTranscriptionLanguage, setFileTranscriptionLanguage] = useState<string>('en-US');

    // Session Manager state
    const [doctorName, setDoctorName] = useState<string>('Dr. Smith');
    const [patientName, setPatientName] = useState<string>('John Doe');
    const [sessionIdInput, setSessionIdInput] = useState<string>('');
    const [transcriptHistory, setTranscriptHistory] = useState<string[]>([]);
    const [isUpdatingSession, setIsUpdatingSession] = useState<boolean>(false);
    
    // SMR state (unified for both live and upload)
    const [fullTranscript, setFullTranscript] = useState<string>('');
    const [currentSummary, setCurrentSummary] = useState<any>(null);
    const [summaryProgress, setSummaryProgress] = useState<number>(0);
    const [autoSummarize, setAutoSummarize] = useState<boolean>(false);
    const [isSummarizing, setIsSummarizing] = useState<boolean>(false);
    const [summarySource, setSummarySource] = useState<'live' | 'upload' | null>(null);

    const [uploadedFile, setUploadedFile] = useState<File | null>(null);
    const [uploadTaskId, setUploadTaskId] = useState<string>('');
    const [uploadTranscript, setUploadTranscript] = useState<string>('');
    const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'processing' | 'completed' | 'failed'>('idle');
    const [uploadError, setUploadError] = useState<string>('');

    // Track last processed transcript count to prevent unnecessary updates
    const lastProcessedTranscriptCount = useRef<number>(0);
    const transcriptRef = useRef<string>('');
    const lastSummaryLength = useRef<number>(0);

    // Helper function to safely extract summary data from enhanced SMR response
    const extractSummaryData = (summaryResponse: any) => {
        if (!summaryResponse) return null;
        
        // Check if this is the new SummaryResponse format
        if (summaryResponse.summary && summaryResponse.session_id) {
            const actualSummary = summaryResponse.summary;
            
            // Handle EnhancedMedicalSummary format
            if (actualSummary.encounter_summary) {
                return {
                    summary: actualSummary.clinical_summary?.summary || actualSummary.encounter_summary?.chief_complaint || 'No summary available',
                    keyPoints: actualSummary.clinical_summary?.key_findings || [],
                    diagnosis: actualSummary.clinical_assessment?.primary_diagnosis?.diagnosis || '',
                    recommendations: actualSummary.treatment_plan?.procedures || [],
                    medications: actualSummary.treatment_plan?.medications?.map((med: any) => med.name) || [],
                    metadata: {
                        provider: summaryResponse.llm_provider,
                        model: summaryResponse.model_name,
                        processingTime: summaryResponse.processing_time_ms,
                        confidence: summaryResponse.confidence_score
                    }
                };
            }
            
            // Handle SimplifiedMedicalSummary format
            if (actualSummary.chief_complaint) {
                return {
                    summary: actualSummary.summary,
                    keyPoints: actualSummary.symptoms || [],
                    diagnosis: actualSummary.assessment,
                    recommendations: actualSummary.treatment_plan ? [actualSummary.treatment_plan] : [],
                    medications: [],
                    metadata: {
                        provider: summaryResponse.llm_provider,
                        model: summaryResponse.model_name,
                        processingTime: summaryResponse.processing_time_ms,
                        confidence: summaryResponse.confidence_score
                    }
                };
            }
        }
        
        // Fallback to old format (backward compatibility)
        return summaryResponse;
    };

    // Use Session Manager hook
    const {
        session,
        isLoading: sessionLoading,
        error: sessionError,
        createSession,
        startSession,
        pauseSession,
        resumeSession,
        endSession,
        loadSession,
        clearError,
        updateSession
    } = useArcaSessionManager({
        doctorId: 'doctor-123',
        doctorName: doctorName,
        patientId: 'patient-456',
        patientName: patientName,
        options: {
            apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
            credentials: SDK_CONFIG_OPTIONS.credentials
        },
        onError: (error: ErrorInfo) => {
            console.error('Session Manager Error:', error);
        }
    });

    // SMR (Summarization) hook - only connect when session is active
    const smr = useSMR({
        apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
        sessionId: session?.status === 'ACTIVE' ? session.id : undefined, // Only use session ID when active
        options: {
            credentials: {
                apiKey: SDK_CONFIG_OPTIONS.credentials.apiKey
            }
        },
        onProgress: (progress: number) => {
            setSummaryProgress(progress);
        },
        onComplete: (summary: any) => { // SummaryResponse from enhanced SMR service
            setCurrentSummary(summary);
            setSummaryProgress(100);
        },
        onError: (error: ErrorInfo) => {
            console.error('SMR Error:', error);
        }
    });

    // Use Speech-to-Text hook
    const {
        error: sttError,
        startTranscription,
        stopTranscription,
        sendAudioData,
        transcript,
        uploadAudioFile,
        getTranscriptionStatus,
        isUploading,
        uploadProgress,
    } = useArcaSpeechToText({
        sessionId: session?.id || 'temp-session',
        language: language,
        options: {
            apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
            websocketUrl: SDK_CONFIG_OPTIONS.websocketUrl,
            credentials: SDK_CONFIG_OPTIONS.credentials
        },
        onTranscript: (text, isFinal) => {
            console.log('Transcript:', text, 'Final:', isFinal);
            if (isFinal) {
                setChunk('');
                // Add to transcript history
                if (text.trim()) {
                    setTranscriptHistory(prev => [...prev, text.trim()]);
                    
                    // Update full transcript for SMR
                    const newTranscript = transcriptRef.current + ' ' + text.trim();
                    transcriptRef.current = newTranscript;
                    setFullTranscript(newTranscript);
                    
                    // Auto-summarize if enabled and transcript is long enough
                    // Hide auto-summarize at 500 words for now
                    const wordCount = newTranscript.split(/\s+/).filter(word => word.length > 0).length;
                    if (autoSummarize && wordCount < 500 && newTranscript.length > lastSummaryLength.current + 500) {
                        handleAutoSummarize(newTranscript);
                    }
                }
            } else {
                setChunk(text);
            }
        },
        onError: (error) => {
            console.error('STT Error:', error);
        },
    });

    // Use Audio Capture hook
    const {
        isRecording,
        startRecording,
        stopRecording,
        isReady,
        error: audioError,
        deviceStatus,
        getDeviceStatus,
    } = useAudioCapture({
        onAudioData: (data) => {
            if (canUseSTT) {
            sendAudioData(data);
            }
        },
        onError: (error) => {
            console.error('Audio Capture Error:', error);
        },
    });

    // Check if STT can be used (session must be active)
    const canUseSTT = session?.status === 'ACTIVE';

    // SMR helper functions
    const handleAutoSummarize = async (transcript: string) => {
        // Check if session is active and SMR is connected before auto-summarizing
        if (!canUseSTT || !smr.isConnected) {
            console.log('Auto-summarization skipped: Session not active or SMR not connected');
            return;
        }
        
        try {
            lastSummaryLength.current = transcript.length;
            const summary = await smr.summarizeSync({
                text: transcript,
                sessionId: session?.id,
                language: language.split('-')[0]
            });
            console.log('Auto-summarization completed:', summary);
        } catch (error) {
            console.error('Auto-summarization error:', error);
        }
    };

    const handleManualSummarize = async () => {
        if (!canUseSTT) {
            alert('Session must be active to use Medical Summarization');
            return;
        }
        
        // Check if recording is still active - wait for stop recording
        if (isRecording) {
            alert('Please stop recording before generating summary');
            return;
        }
        
        if (!fullTranscript.trim()) {
            alert('No transcript available to summarize');
            return;
        }

        try {
            setSummaryProgress(0);
            const summary = await smr.summarizeSync({
                text: fullTranscript,
                sessionId: session?.id,
                language: language.split('-')[0]
            });
            
            console.log('Summarization completed:', summary);
            setSummaryProgress(100);
        } catch (error) {
            console.error('Summarization error:', error);
        }
    };

    const handleClearTranscript = () => {
        setFullTranscript('');
        setCurrentSummary(null);
        setSummaryProgress(0);
        transcriptRef.current = '';
        lastSummaryLength.current = 0;
        setTranscriptHistory([]);
    };

    // Check if STT connection is allowed (session must be active)
    const canConnectSTT = session?.status === 'ACTIVE';

    // Handle session creation
    const handleCreateSession = async () => {
        try {
            await createSession({
                sessionType: 'consultation',
                priority: 'medium',
                tags: ['stt-session', 'medical-consultation'],
                customFields: {
                    sttLanguage: language,
                    createdFrom: 'stt-demo'
                }
            });
            console.log('✅ Session created successfully');
        } catch (error) {
            console.error('❌ Failed to create session:', error);
        }
    };

    // Handle session start
    const handleStartSession = async () => {
        try {
            await startSession();
            console.log('✅ Session started successfully');
        } catch (error) {
            console.error('❌ Failed to start session:', error);
        }
    };

    // Handle session load
    const handleLoadSession = async () => {
        if (!sessionIdInput.trim()) {
            alert('Please enter a session ID');
            return;
        }

        try {
            await loadSession(sessionIdInput.trim());
            console.log('✅ Session loaded successfully:', sessionIdInput);
        } catch (error) {
            console.error('❌ Failed to load session:', error);
            alert('Failed to load session. Check console for details.');
        }
    };

    // Auto-update session when new transcript is available
    useEffect(() => {
        const updateSessionWithTranscript = async () => {
            if (
                session?.status === 'ACTIVE' &&
                transcriptHistory.length > 0 &&
                transcriptHistory.length > lastProcessedTranscriptCount.current &&
                !isUpdatingSession &&
                !sessionLoading
            ) {
                try {
                    setIsUpdatingSession(true);

                    console.log('🔄 Auto-updating session with new transcript:', {
                        sessionId: session.id,
                        newTranscriptCount: transcriptHistory.length,
                        lastProcessedCount: lastProcessedTranscriptCount.current,
                        newTranscripts: transcriptHistory.slice(lastProcessedTranscriptCount.current)
                    });

                    // Calculate transcript statistics
                    const fullTranscript = transcriptHistory.join(' ');
                    const totalCharacters = fullTranscript.length;
                    const totalWords = fullTranscript.split(/\s+/).filter(word => word.length > 0).length;
                    const latestTranscript = transcriptHistory[transcriptHistory.length - 1];

                    const sessionUpdateData = {
                        sttData: {
                            lastTranscriptText: latestTranscript,
                            lastTranscriptLanguage: language,
                            lastTranscriptTime: new Date().toISOString(),
                            totalTranscripts: transcriptHistory.length,
                            fullTranscript: fullTranscript,
                            transcriptData: transcriptHistory, // Store the actual transcript array
                            transcriptMetadata: {
                                totalCharacters: totalCharacters,
                                totalWords: totalWords,
                                averageWordsPerTranscript: Math.round(totalWords / transcriptHistory.length),
                                language: language,
                                encoding: 'utf-8'
                            },
                            transcriptStatistics: {
                                segmentCount: transcriptHistory.length,
                                longestSegment: Math.max(...transcriptHistory.map(t => t.length)),
                                shortestSegment: Math.min(...transcriptHistory.map(t => t.length)),
                                averageSegmentLength: Math.round(totalCharacters / transcriptHistory.length)
                            }
                        },
                        lastActivity: new Date().toISOString(),
                        activityLog: [
                            ...(Array.isArray(session?.metadata?.customFields?.activityLog) ? session.metadata.customFields.activityLog : []),
                            {
                                type: 'stt_transcript',
                                timestamp: new Date().toISOString(),
                                text: latestTranscript.substring(0, 100) + (latestTranscript.length > 100 ? '...' : ''),
                                language: language,
                                transcriptCount: transcriptHistory.length,
                                characterCount: latestTranscript.length,
                                wordCount: latestTranscript.split(/\s+/).filter(word => word.length > 0).length,
                                hasTranscriptData: true
                            }
                        ]
                    };

                    await updateSession(sessionUpdateData);

                    // Update the processed count after successful update
                    lastProcessedTranscriptCount.current = transcriptHistory.length;

                    console.log('✅ Session auto-updated with transcript data:', {
                        transcriptCount: transcriptHistory.length,
                        totalCharacters: totalCharacters,
                        totalWords: totalWords,
                        transcriptDataSize: JSON.stringify(transcriptHistory).length,
                        lastTranscript: latestTranscript.substring(0, 50) + '...'
                    });
                } catch (updateError) {
                    console.error('❌ Failed to auto-update session with transcript data:', updateError);
                } finally {
                    setIsUpdatingSession(false);
                }
            } else {
                // Log why update was skipped
                if (session?.status !== 'ACTIVE') {
                    console.log('⏭️ Skipping session update: Session not active', session?.status);
                } else if (transcriptHistory.length === 0) {
                    console.log('⏭️ Skipping session update: No transcripts');
                } else if (transcriptHistory.length <= lastProcessedTranscriptCount.current) {
                    console.log('⏭️ Skipping session update: No new transcripts', {
                        current: transcriptHistory.length,
                        processed: lastProcessedTranscriptCount.current
                    });
                } else if (isUpdatingSession) {
                    console.log('⏭️ Skipping session update: Already updating');
                } else if (sessionLoading) {
                    console.log('⏭️ Skipping session update: Session loading');
                }
            }
        };

        // Debounce the update to avoid too frequent calls
        const timeoutId = setTimeout(updateSessionWithTranscript, 1000);
        return () => clearTimeout(timeoutId);
    }, [transcriptHistory, language, updateSession, isUpdatingSession, sessionLoading]);

    // Handle start recording
    const handleStart = async () => {
        if (!canUseSTT) return;

        try {
            await startTranscription();
            await startRecording();
            console.log('🎤 Started recording and transcription');
        } catch (error) {
            console.error('Failed to start recording:', error);
        }
    };

    // Handle stop recording
    const handleStop = async () => {
        try {
            await stopRecording();
            await stopTranscription();
            console.log('⏹️ Stopped recording and transcription');
        } catch (error) {
            console.error('Failed to stop recording:', error);
        }
    };

    // Handle device status check
    const handleCheckDeviceStatus = async () => {
        await getDeviceStatus();
    };

    // Add new file upload handlers
    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setUploadedFile(file);
            setUploadStatus('idle');
            setUploadError('');
            setUploadTranscript('');
            setUploadTaskId('');
        }
    };

    const handleFileUpload = useCallback(async () => {
        setUploadTaskId('');

        let interval: any;

        if (!uploadedFile) return;

        try {
            setUploadStatus('uploading');
            setUploadError('');

            const taskId = await uploadAudioFile(uploadedFile, fileTranscriptionLanguage);
            setUploadTaskId(taskId);
            setUploadStatus('processing');

            console.log('✅ File uploaded successfully, task ID:', taskId);

            const pollStatus = async () => {
                try {
                    const res = await getTranscriptionStatus(taskId);
                    console.log('🔄 Status:', res);

                    if (res.status === 'SUCCESS') {
                        setUploadStatus('completed');
                        setUploadTranscript(res.result.text || '');
                        if (interval) {
                            clearInterval(interval);
                        }
                    } else if (res.status === 'FAILURE') {
                        setUploadStatus('failed');
                        setUploadError(res.error || 'Transcription failed');
                        if (interval) {
                            clearInterval(interval);
                        }
                    }
                } catch (error) {
                    console.error('❌ Failed to check status:', error);
                    setUploadStatus('failed');
                    setUploadError(error instanceof Error ? error.message : 'Status check failed');
                    if (interval) {
                        clearInterval(interval);
                    }
                }
            };
            interval = setInterval(pollStatus, 2000);
            pollStatus();
        } catch (error) {
            console.error('❌ Failed to upload file:', error);
            setUploadStatus('failed');
            setUploadError(error instanceof Error ? error.message : 'Upload failed');
        }
    }, [uploadedFile, fileTranscriptionLanguage]);

    // Handle file upload SMR summarization
    const handleUploadSummarize = async () => {
        if (!uploadTranscript.trim()) {
            alert('No transcript available to summarize from uploaded file');
            return;
        }

        try {
            setIsSummarizing(true);
            setSummaryProgress(0);
            setSummarySource('upload');
            
            const summary = await smr.summarizeSync({
                text: uploadTranscript,
                sessionId: session?.id,
                language: fileTranscriptionLanguage.split('-')[0]
            });
            
            setCurrentSummary(summary);
            setSummaryProgress(100);
            console.log('Upload summarization completed:', summary);
        } catch (error) {
            console.error('Upload summarization error:', error);
            alert('Failed to summarize uploaded transcript. Check console for details.');
        } finally {
            setIsSummarizing(false);
        }
    };

    const resetFileUpload = () => {
        setUploadedFile(null);
        setUploadTaskId('');
        setUploadTranscript('');
        setUploadStatus('idle');
        setUploadError('');
        // Clear summary if it was from upload
        if (summarySource === 'upload') {
            setCurrentSummary(null);
            setSummaryProgress(0);
            setSummarySource(null);
        }
        setIsSummarizing(false);
    };

    // Check if STT session is active
    const sttActive = transcript !== '' || isRecording;
    const isActive = isRecording && sttActive;
    const hasError = sttError || audioError;

    // Session debug logging
    useEffect(() => {
        console.log('🔄 Session State Updated:', {
            sessionId: session?.id,
            status: session?.status,
            sessionLoading,
            sessionError,
            canConnectSTT,
            canUseSTT,
        });
    }, [session, sessionLoading, sessionError, canConnectSTT, canUseSTT]);

    // Reset transcript counter when session changes
    useEffect(() => {
        lastProcessedTranscriptCount.current = 0;
        console.log('🔄 Reset transcript counter for new session:', session?.id);
    }, [session?.id]);

    if (sessionLoading) {
        return (
            <div
                style={{
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    padding: '20px',
                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                    maxWidth: '800px',
                    margin: '0 auto',
                    textAlign: 'center',
                }}
            >
                <h2>Initializing Session Manager...</h2>
                <p>Please wait while we set up the session manager.</p>
                <div
                    style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        border: '4px solid #f3f3f3',
                        borderTop: '4px solid #007bff',
                        animation: 'spin 1s linear infinite',
                        margin: '20px auto',
                    }}
                />
            </div>
        );
    }

    return (
        <div
            style={{
                backgroundColor: 'white',
                borderRadius: '8px',
                padding: '20px',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                maxWidth: '800px',
                margin: '0 auto',
            }}
        >
            <h2 style={{ marginTop: 0, color: '#333' }}>Speech-to-Text with Session Management</h2>

            {/* Session Management Section */}
            <div
                style={{
                    backgroundColor: '#f8f9fa',
                    border: '1px solid #dee2e6',
                    borderRadius: '6px',
                    padding: '15px',
                    marginBottom: '20px',
                }}
            >
                <h3 style={{ marginTop: 0, color: '#495057' }}>Session Management</h3>

                {/* Session Status */}
                <div style={{ marginBottom: '15px' }}>
                    <span style={{ fontWeight: 'bold' }}>Session Status: </span>
                    <span
                        style={{
                            color:
                                session?.status === 'ACTIVE'
                                    ? '#28a745'
                                    : session?.status === 'PAUSED'
                                      ? '#ffc107'
                                      : session?.status === 'IDLE'
                                        ? '#17a2b8'
                                        : '#6c757d',
                            fontWeight: 'bold',
                        }}
                    >
                        {session?.status || 'No Session'}
                    </span>
                    {session && <span style={{ marginLeft: '10px', fontSize: '0.9em', color: '#6c757d' }}>ID: {session.id.substring(0, 8)}...</span>}
                </div>

                {/* Doctor and Patient Info */}
                <div
                    style={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        gap: '10px',
                        marginBottom: '15px',
                    }}
                >
                    <div>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Doctor Name:</label>
                        <input
                            type="text"
                            value={doctorName}
                            onChange={(e) => setDoctorName(e.target.value)}
                            disabled={!!session}
                            style={{
                                width: '100%',
                                padding: '8px',
                                borderRadius: '4px',
                                border: '1px solid #ced4da',
                                backgroundColor: session ? '#e9ecef' : 'white',
                            }}
                        />
                    </div>
                    <div>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Patient Name:</label>
                        <input
                            type="text"
                            value={patientName}
                            onChange={(e) => setPatientName(e.target.value)}
                            disabled={!!session}
                            style={{
                                width: '100%',
                                padding: '8px',
                                borderRadius: '4px',
                                border: '1px solid #ced4da',
                                backgroundColor: session ? '#e9ecef' : 'white',
                            }}
                        />
                    </div>
                </div>

                {/* Language Selection */}
                <div style={{ marginBottom: '15px' }}>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Language:</label>
                    <select
                        value={language}
                        onChange={(e) => setLanguage(e.target.value)}
                        disabled={!!session}
                        style={{
                            width: '100%',
                            padding: '8px',
                            borderRadius: '4px',
                            border: '1px solid #ced4da',
                            backgroundColor: session ? '#e9ecef' : 'white',
                        }}
                    >
                        <option value="en-US">English (US)</option>
                        <option value="ml-IN">Malayalam (India)</option>
                        <option value="hi-IN">Hindi (India)</option>
                        <option value="ta-IN">Tamil (India)</option>
                    </select>
                </div>

                {/* Session Actions */}
                <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                    {!session && (
                        <button
                            onClick={handleCreateSession}
                            disabled={sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#007bff',
                                color: 'white',
                                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            Create Session
                        </button>
                    )}

                    {session && session.status === 'IDLE' && (
                        <button
                            onClick={handleStartSession}
                            disabled={sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#28a745',
                                color: 'white',
                                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            Start Session
                        </button>
                    )}

                    {session && session.status === 'ACTIVE' && (
                        <button
                            onClick={() => pauseSession('User requested pause')}
                            disabled={sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#ffc107',
                                color: 'black',
                                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            Pause Session
                        </button>
                    )}

                    {session && session.status === 'PAUSED' && (
                        <button
                            onClick={resumeSession}
                            disabled={sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#17a2b8',
                                color: 'white',
                                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            Resume Session
                        </button>
                    )}

                    {session && (
                        <button
                            onClick={endSession}
                            disabled={sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#dc3545',
                                color: 'white',
                                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            End Session
                        </button>
                    )}
                </div>

                {/* Load Existing Session */}
                <div style={{ marginTop: '15px', paddingTop: '15px', borderTop: '1px solid #dee2e6' }}>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Load Existing Session:</label>
                    <div style={{ display: 'flex', gap: '10px' }}>
                        <input
                            type="text"
                            value={sessionIdInput}
                            onChange={(e) => setSessionIdInput(e.target.value)}
                            placeholder="Enter session ID..."
                            disabled={!!session}
                            style={{
                                flex: 1,
                                padding: '8px',
                                borderRadius: '4px',
                                border: '1px solid #ced4da',
                                backgroundColor: session ? '#e9ecef' : 'white',
                            }}
                        />
                        <button
                            onClick={handleLoadSession}
                            disabled={!!session || !sessionIdInput.trim() || sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#6f42c1',
                                color: 'white',
                                border: 'none',
                                cursor: !!session || !sessionIdInput.trim() || sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            Load
                        </button>
                    </div>
                </div>

                {/* Session Error */}
                {sessionError && (
                    <div
                        style={{
                            marginTop: '10px',
                            padding: '10px',
                            backgroundColor: '#f8d7da',
                            color: '#721c24',
                            borderRadius: '4px',
                            border: '1px solid #f5c6cb',
                        }}
                    >
                        <strong>Session Error:</strong> {sessionError.message}
                        <button
                            onClick={clearError}
                            style={{
                                marginLeft: '10px',
                                padding: '2px 8px',
                                fontSize: '0.8em',
                                backgroundColor: 'transparent',
                                border: '1px solid #721c24',
                                borderRadius: '3px',
                                color: '#721c24',
                                cursor: 'pointer',
                            }}
                        >
                            Clear
                        </button>
                    </div>
                )}
            </div>

            {/* STT Section - Only enabled when session is active */}
            <div
                style={{
                    opacity: canConnectSTT ? 1 : 0.5,
                    pointerEvents: canConnectSTT ? 'auto' : 'none',
                }}
            >
                <h3 style={{ color: '#495057' }}>Speech-to-Text</h3>

                {!canConnectSTT && (
                    <div
                        style={{
                            marginBottom: '15px',
                            padding: '10px',
                            backgroundColor: '#fff3cd',
                            color: '#856404',
                            borderRadius: '4px',
                            border: '1px solid #ffeaa7',
                        }}
                    >
                        <strong>Notice:</strong> Please create and start a session to use Speech-to-Text.
                    </div>
                )}

                {/* Error Display */}
                {hasError && (
                    <div
                        style={{
                            padding: '10px',
                            marginBottom: '20px',
                            backgroundColor: '#ffebee',
                            border: '1px solid #f44336',
                            borderRadius: '4px',
                            color: '#d32f2f',
                        }}
                    >
                        <h4>Error:</h4>
                        {sttError && <p>STT: {sttError.message}</p>}
                        {audioError && <p>Audio: {audioError.message}</p>}
                    </div>
                )}

                {/* Status Display */}
                <div style={{ marginBottom: '20px' }}>
                    <p>Ready: {isReady ? '✅' : '❌'}</p>
                    <p>Recording: {isActive ? '🔴' : '⚫'}</p>
                    <p>Audio Capture: {isRecording ? '🎤' : '⏹️'}</p>
                    <p>STT Session: {sttActive ? '💬' : '😶'}</p>
                    {deviceStatus && (
                        <p>
                            Device Status: {deviceStatus.permissionStatus === 'granted' ? '✅' : '❌'} Permission,
                            {deviceStatus.inputDevices.length > 0 ? '✅' : '❌'} Devices Available
                        </p>
                    )}
                </div>

                {/* Control Buttons */}
                <div style={{ marginBottom: '20px' }}>
                    <button
                        onClick={handleStart}
                        disabled={!isReady || isActive || !canUseSTT}
                        style={{
                            padding: '10px 20px',
                            marginRight: '10px',
                            backgroundColor: !isReady || isActive || !canUseSTT ? '#6c757d' : '#4CAF50',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: !isReady || isActive || !canUseSTT ? 'not-allowed' : 'pointer',
                        }}
                    >
                        Start Recording
                    </button>

                    <button
                        onClick={handleStop}
                        disabled={!isActive}
                        style={{
                            padding: '10px 20px',
                            marginRight: '10px',
                            backgroundColor: !isActive ? '#6c757d' : '#f44336',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: !isActive ? 'not-allowed' : 'pointer',
                        }}
                    >
                        Stop Recording
                    </button>

                    <button
                        onClick={handleCheckDeviceStatus}
                        style={{
                            padding: '10px 20px',
                            backgroundColor: '#2196F3',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                        }}
                    >
                        Check Device
                    </button>
                </div>

                {/* Current Transcript Chunk */}
                {chunk && (
                    <div
                        style={{
                            marginBottom: '15px',
                            padding: '10px',
                            backgroundColor: '#e3f2fd',
                            border: '1px solid #2196F3',
                            borderRadius: '4px',
                        }}
                    >
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span style={{ fontWeight: 'bold', color: '#1976d2' }}>🎤 Live Transcript</span>
                            <span style={{ fontSize: '0.9em', color: '#1976d2' }}>Processing...</span>
                        </div>
                        <p style={{ margin: '8px 0 0 0', fontStyle: 'italic' }}>{chunk}</p>
                    </div>
                )}

                {/* Transcript History */}
                <div
                    style={{
                        padding: '15px',
                        border: '1px solid #ddd',
                        borderRadius: '4px',
                        backgroundColor: '#f9f9f9',
                        minHeight: '200px',
                        maxHeight: '400px',
                        overflowY: 'auto',
                    }}
                >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                        <h4 style={{ margin: 0 }}>Transcript History:</h4>
                        {transcriptHistory.length > 0 && (
                            <div style={{ display: 'flex', gap: '15px', fontSize: '0.9em', color: '#6c757d' }}>
                                <span>
                                    {transcriptHistory.length} transcript{transcriptHistory.length !== 1 ? 's' : ''}
                                </span>
                                <span>{transcriptHistory.join(' ').length} characters</span>
                                <span>
                                    {
                                        transcriptHistory
                                            .join(' ')
                                            .split(/\s+/)
                                            .filter((word) => word.length > 0).length
                                    }{' '}
                                    words
                                </span>
                            </div>
                        )}
                        {isUpdatingSession && (
                            <div
                                style={{
                                    fontSize: '0.85em',
                                    color: '#28a745',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '5px',
                                }}
                            >
                                <div
                                    style={{
                                        width: '12px',
                                        height: '12px',
                                        borderRadius: '50%',
                                        backgroundColor: '#28a745',
                                        animation: 'pulse 1s infinite',
                                    }}
                                />
                                Updating session...
                            </div>
                        )}
                    </div>

                    {transcriptHistory.length === 0 ? (
                        <p style={{ color: '#6c757d', fontStyle: 'italic' }}>
                            No transcripts yet. Start recording to see your speech converted to text.
                        </p>
                    ) : (
                        <div>
                            {/* Session Data Summary */}
                            {session?.status === 'ACTIVE' && transcriptHistory.length > 0 && (
                                <div
                                    style={{
                                        marginBottom: '15px',
                                        padding: '10px',
                                        backgroundColor: '#e8f5e8',
                                        border: '1px solid #28a745',
                                        borderRadius: '4px',
                                    }}
                                >
                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '5px' }}>
                                        <span style={{ fontWeight: 'bold', color: '#155724' }}>📝 Session Transcript Data</span>
                                        <span style={{ fontSize: '0.85em', color: '#155724' }}>Auto-saved to session</span>
                                    </div>
                                    <div style={{ fontSize: '0.9em', color: '#155724' }}>
                                        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '10px' }}>
                                            <div>
                                                <strong>Segments:</strong> {transcriptHistory.length}
                                            </div>
                                            <div>
                                                <strong>Total Characters:</strong> {transcriptHistory.join(' ').length}
                                            </div>
                                            <div>
                                                <strong>Total Words:</strong>{' '}
                                                {
                                                    transcriptHistory
                                                        .join(' ')
                                                        .split(/\s+/)
                                                        .filter((word) => word.length > 0).length
                                                }
                                            </div>
                                        </div>
                                        <div style={{ marginTop: '5px', fontSize: '0.85em' }}>
                                            <strong>Data Size:</strong> {(JSON.stringify(transcriptHistory).length / 1024).toFixed(2)} KB
                                        </div>
                                    </div>
                                </div>
                            )}

                            {transcriptHistory.map((text, index) => (
                                <div
                                    key={index}
                                    style={{
                                        padding: '8px',
                                        marginBottom: '8px',
                                        backgroundColor: 'white',
                                        border: '1px solid #e9ecef',
                                        borderRadius: '4px',
                                        borderLeft: '4px solid #28a745',
                                    }}
                                >
                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
                                        <div style={{ fontSize: '0.8em', color: '#6c757d' }}>Transcript #{index + 1}</div>
                                        <div style={{ fontSize: '0.75em', color: '#6c757d' }}>
                                            {text.length} chars • {text.split(/\s+/).filter((word) => word.length > 0).length} words
                                        </div>
                                    </div>
                                    <div style={{ wordWrap: 'break-word' }}>{text}</div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            {/* SMR (Summarization) Section */}
            <div style={{
                backgroundColor: '#f8f9fa',
                border: '1px solid #dee2e6',
                borderRadius: '6px',
                padding: '15px',
                marginBottom: '20px',
                opacity: (canUseSTT && smr.isConnected) ? 1 : 0.5,
                pointerEvents: (canUseSTT && smr.isConnected) ? 'auto' : 'none'
            }}>
                <h3 style={{ marginTop: 0, color: '#495057' }}>📋 Medical Summarization</h3>
                
                {/* Connection Status Warning */}
                {(!canUseSTT || !smr.isConnected) && (
                    <div
                        style={{
                            marginBottom: '15px',
                            padding: '10px',
                            backgroundColor: '#fff3cd',
                            color: '#856404',
                            borderRadius: '4px',
                            border: '1px solid #ffeaa7',
                        }}
                    >
                        ⚠️ Medical Summarization requires an active session and WebSocket connection.
                        {!canUseSTT && <><br/>• Session Status: {session?.status || 'No session'}</>}
                        {!smr.isConnected && <><br/>• SMR Connection: Disconnected</>}
                    </div>
                )}
                
                {/* SMR Controls */}
                <div style={{ display: 'flex', gap: '10px', alignItems: 'center', marginBottom: '15px' }}>
                    <button
                        onClick={handleManualSummarize}
                        disabled={!canUseSTT || !smr.isConnected || !fullTranscript.trim() || smr.loading}
                        style={{
                            padding: '8px 16px',
                            backgroundColor: (!canUseSTT || !smr.isConnected || !fullTranscript.trim() || smr.loading) ? '#6c757d' : '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: (!canUseSTT || !smr.isConnected || !fullTranscript.trim() || smr.loading) ? 'not-allowed' : 'pointer'
                        }}
                    >
                        {smr.loading ? 'Summarizing...' : '📋 Generate Summary'}
                    </button>
                    
                    <button
                        onClick={handleClearTranscript}
                        style={{
                            padding: '8px 16px',
                            backgroundColor: '#dc3545',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer'
                        }}
                    >
                        🗑️ Clear All
                    </button>
                </div>
                
                {/* Full Transcript Display */}
                <div style={{ marginBottom: '15px' }}>
                    <h4>Full Transcript ({fullTranscript.length} characters):</h4>
                    <div style={{
                        padding: '10px',
                        border: '1px solid #ddd',
                        borderRadius: '4px',
                        backgroundColor: '#ffffff',
                        minHeight: '100px',
                        maxHeight: '200px',
                        overflowY: 'auto',
                        whiteSpace: 'pre-wrap'
                    }}>
                        {fullTranscript || 'Full transcript will appear here as you speak...'}
                    </div>
                </div>
                
                {/* SMR Progress */}
                {smr.loading && (
                    <div style={{ marginBottom: '15px' }}>
                        <div style={{ fontSize: '14px', marginBottom: '5px' }}>
                            Summarization Progress: {summaryProgress}%
                        </div>
                        <div style={{ width: '100%', backgroundColor: '#eee', borderRadius: '10px', height: '10px' }}>
                            <div
                                style={{
                                    width: `${summaryProgress}%`,
                                    backgroundColor: '#4CAF50',
                                    height: '100%',
                                    borderRadius: '10px',
                                    transition: 'width 0.3s ease'
                                }}
                            />
                        </div>
                    </div>
                )}
                
                {/* SMR Status */}
                <div style={{ fontSize: '14px', marginBottom: '15px' }}>
                    <strong>SMR Status:</strong> {smr.isConnected ? '✅ Connected' : '❌ Disconnected'}<br/>
                    <strong>Current Job:</strong> {smr.currentJob ? `${smr.currentJob.status} (${(smr.currentJob as any).job_id || (smr.currentJob as any).id || 'unknown'})` : 'None'}
                </div>
                
                {/* SMR Error Display */}
                {smr.error && (
                    <div style={{
                        color: 'red',
                        padding: '10px',
                        backgroundColor: '#ffebee',
                        border: '1px solid #f44336',
                        borderRadius: '4px',
                        marginBottom: '15px'
                    }}>
                        SMR Error: {smr.error}
                    </div>
                )}
                
                {/* Unified Summary Display */}
                {(() => {
                    const displaySummary = extractSummaryData(currentSummary);
                    return displaySummary && (
                        <div style={{
                            padding: '15px',
                            border: '1px solid #28a745',
                            borderRadius: '4px',
                            backgroundColor: '#f8fff9'
                        }}>
                            <h4>📋 Medical Summary {summarySource === 'upload' ? '(from File Upload)' : '(from Live Recording)'}</h4>
                            
                            <div style={{ marginBottom: '15px' }}>
                                <h5>Summary:</h5>
                                <div style={{ padding: '10px', backgroundColor: '#f0f8ff', borderRadius: '3px' }}>
                                    {displaySummary.summary}
                                </div>
                            </div>
                            
                            {displaySummary.keyPoints && displaySummary.keyPoints.length > 0 && (
                                <div style={{ marginBottom: '15px' }}>
                                    <h5>Key Points:</h5>
                                    <ul>
                                        {displaySummary.keyPoints.map((point: string, index: number) => (
                                            <li key={index}>{point}</li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                            
                            {displaySummary.diagnosis && (
                                <div style={{ marginBottom: '15px' }}>
                                    <h5>Diagnosis:</h5>
                                    <div style={{ padding: '10px', backgroundColor: '#fff0f0', borderRadius: '3px' }}>
                                        {displaySummary.diagnosis}
                                    </div>
                                </div>
                            )}
                            
                            {displaySummary.recommendations && displaySummary.recommendations.length > 0 && (
                                <div style={{ marginBottom: '15px' }}>
                                    <h5>Recommendations:</h5>
                                    <ul>
                                        {displaySummary.recommendations.map((rec: string, index: number) => (
                                            <li key={index}>{rec}</li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                            
                            {displaySummary.medications && displaySummary.medications.length > 0 && (
                                <div style={{ marginBottom: '15px' }}>
                                    <h5>Medications:</h5>
                                    <ul>
                                        {displaySummary.medications.map((med: string, index: number) => (
                                            <li key={index}>{med}</li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                            
                            {displaySummary.metadata && (
                                <div style={{ fontSize: '12px', color: '#666', marginTop: '10px' }}>
                                    <strong>Processing time:</strong> {displaySummary.metadata.processingTime}ms
                                    {displaySummary.metadata.confidence && (
                                        <><br/><strong>Confidence:</strong> {(displaySummary.metadata.confidence * 100).toFixed(1)}%</>
                                    )}
                                </div>
                            )}
                        </div>
                    );
                })()}
            </div>

            {/* Add new File Upload Section */}
            <div
                style={{
                    backgroundColor: '#f8f9fa',
                    border: '1px solid #dee2e6',
                    borderRadius: '6px',
                    padding: '20px',
                    marginTop: '20px',
                }}
            >
                <h3 style={{ marginTop: 0, color: '#495057' }}>Audio File Transcription</h3>
                <p style={{ color: '#6c757d', marginBottom: '20px' }}>Upload an audio file to get a transcript. No session required.</p>

                {/* File Upload Controls */}
                <div style={{ marginBottom: '20px' }}>
                    {/* Language Selection */}
                    <div style={{ marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Language:</label>
                        <select
                            value={fileTranscriptionLanguage}
                            onChange={(e) => setFileTranscriptionLanguage(e.target.value)}
                            disabled={uploadStatus === 'uploading' || uploadStatus === 'processing'}
                            style={{
                                width: '100%',
                                padding: '8px',
                                borderRadius: '4px',
                                border: '1px solid #ced4da',
                                backgroundColor: uploadStatus === 'uploading' || uploadStatus === 'processing' ? '#e9ecef' : 'white',
                            }}
                        >
                            <option value="en-US">English (US)</option>
                            <option value="ml-IN">Malayalam (India)</option>
                        </select>
                    </div>

                    <div style={{ marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Select Audio File:</label>
                        <input
                            type="file"
                            accept="audio/*,.wav,.mp3,.m4a,.flac,.ogg"
                            onChange={handleFileSelect}
                            disabled={uploadStatus === 'uploading' || uploadStatus === 'processing'}
                            style={{
                                width: '100%',
                                padding: '8px',
                                borderRadius: '4px',
                                border: '1px solid #ced4da',
                                backgroundColor: uploadStatus === 'uploading' || uploadStatus === 'processing' ? '#e9ecef' : 'white',
                            }}
                        />
                    </div>

                    {uploadedFile && (
                        <div
                            style={{
                                padding: '10px',
                                backgroundColor: '#e3f2fd',
                                border: '1px solid #2196F3',
                                borderRadius: '4px',
                                marginBottom: '15px',
                            }}
                        >
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <div>
                                    <strong>Selected:</strong> {uploadedFile.name}
                                </div>
                                <div style={{ fontSize: '0.9em', color: '#1976d2' }}>{(uploadedFile.size / (1024 * 1024)).toFixed(2)} MB</div>
                            </div>
                        </div>
                    )}

                    {/* Upload Progress */}
                    {isUploading && (
                        <div style={{ marginBottom: '15px' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                                <span style={{ fontWeight: 'bold', color: '#007bff' }}>Uploading...</span>
                                <span style={{ color: '#007bff' }}>{uploadProgress}%</span>
                            </div>
                            <div
                                style={{
                                    width: '100%',
                                    height: '8px',
                                    backgroundColor: '#e9ecef',
                                    borderRadius: '4px',
                                    overflow: 'hidden',
                                }}
                            >
                                <div
                                    style={{
                                        width: `${uploadProgress}%`,
                                        height: '100%',
                                        backgroundColor: '#007bff',
                                        transition: 'width 0.3s ease',
                                    }}
                                />
                            </div>
                        </div>
                    )}

                    {/* Upload Status */}
                    {uploadStatus !== 'idle' && (
                        <div
                            style={{
                                padding: '10px',
                                borderRadius: '4px',
                                marginBottom: '15px',
                                ...(uploadStatus === 'uploading'
                                    ? {
                                          backgroundColor: '#cce7ff',
                                          border: '1px solid #007bff',
                                          color: '#004085',
                                      }
                                    : uploadStatus === 'processing'
                                      ? {
                                            backgroundColor: '#fff3cd',
                                            border: '1px solid #ffc107',
                                            color: '#856404',
                                        }
                                      : uploadStatus === 'completed'
                                        ? {
                                              backgroundColor: '#d1e7dd',
                                              border: '1px solid #28a745',
                                              color: '#155724',
                                          }
                                        : {
                                              backgroundColor: '#f8d7da',
                                              border: '1px solid #dc3545',
                                              color: '#721c24',
                                          }),
                            }}
                        >
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <div>
                                    <strong>Status:</strong>{' '}
                                    {uploadStatus === 'uploading'
                                        ? '📤 Uploading file...'
                                        : uploadStatus === 'processing'
                                          ? '⚙️ Processing audio...'
                                          : uploadStatus === 'completed'
                                            ? '✅ Transcription completed!'
                                            : '❌ Transcription failed'}
                                </div>
                                {uploadTaskId && <div style={{ fontSize: '0.85em', opacity: 0.8 }}>Task: {uploadTaskId.substring(0, 8)}...</div>}
                            </div>
                            {uploadError && (
                                <div style={{ marginTop: '8px', fontSize: '0.9em' }}>
                                    <strong>Error:</strong> {uploadError}
                                </div>
                            )}
                        </div>
                    )}

                    {/* Action Buttons */}
                    <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                        <button
                            onClick={handleFileUpload}
                            disabled={!uploadedFile || uploadStatus === 'uploading' || uploadStatus === 'processing'}
                            style={{
                                padding: '10px 20px',
                                borderRadius: '4px',
                                backgroundColor:
                                    !uploadedFile || uploadStatus === 'uploading' || uploadStatus === 'processing' ? '#6c757d' : '#007bff',
                                color: 'white',
                                border: 'none',
                                cursor: !uploadedFile || uploadStatus === 'uploading' || uploadStatus === 'processing' ? 'not-allowed' : 'pointer',
                            }}
                        >
                            {uploadStatus === 'uploading' ? 'Uploading...' : uploadStatus === 'processing' ? 'Processing...' : 'Start Transcription'}
                        </button>

                        <button
                            onClick={resetFileUpload}
                            disabled={uploadStatus === 'uploading' || uploadStatus === 'processing'}
                            style={{
                                padding: '10px 20px',
                                borderRadius: '4px',
                                backgroundColor: uploadStatus === 'uploading' || uploadStatus === 'processing' ? '#6c757d' : '#6c757d',
                                color: 'white',
                                border: 'none',
                                cursor: uploadStatus === 'uploading' || uploadStatus === 'processing' ? 'not-allowed' : 'pointer',
                            }}
                        >
                            Reset
                        </button>
                    </div>
                </div>

                {/* Upload Transcript Result */}
                {uploadStatus === 'completed' && (
                    <div
                        style={{
                            padding: '15px',
                            border: '1px solid #28a745',
                            borderRadius: '4px',
                            backgroundColor: '#f8fff9',
                            marginTop: '20px',
                        }}
                    >
                        <h4 style={{ margin: '0 0 10px 0', color: '#155724' }}>📄 Transcript Result</h4>
                        <div
                            style={{
                                padding: '15px',
                                backgroundColor: 'white',
                                border: '1px solid #e9ecef',
                                borderRadius: '4px',
                                maxHeight: '300px',
                                overflowY: 'auto',
                                whiteSpace: 'pre-wrap',
                                wordWrap: 'break-word',
                                lineHeight: '1.5',
                            }}
                        >
                            {uploadTranscript || '(No speech detected in the audio file)'}
                        </div>
                        <div style={{ marginTop: '10px', display: 'flex', justifyContent: 'space-between', fontSize: '0.9em', color: '#6c757d' }}>
                            <span>{(uploadTranscript || '').length} characters</span>
                            <span>{(uploadTranscript || '').split(/\s+/).filter((word) => word.length > 0).length} words</span>
                        </div>
                        
                        {/* Summarize Upload Button */}
                        <div style={{ marginTop: '15px' }}>
                            <button
                                onClick={handleUploadSummarize}
                                disabled={!uploadTranscript.trim() || isSummarizing}
                                style={{
                                    padding: '10px 20px',
                                    borderRadius: '4px',
                                    backgroundColor: !uploadTranscript.trim() || isSummarizing ? '#6c757d' : '#17a2b8',
                                    color: 'white',
                                    border: 'none',
                                    cursor: !uploadTranscript.trim() || isSummarizing ? 'not-allowed' : 'pointer',
                                    fontSize: '14px',
                                    fontWeight: 'bold'
                                }}
                            >
                                {isSummarizing && summarySource === 'upload' ? '🔄 Summarizing...' : '📋 Generate Medical Summary'}
                            </button>
                        </div>
                    </div>
                )}
                
                {/* Summary Progress (for upload) */}
                {isSummarizing && summarySource === 'upload' && (
                    <div style={{ marginTop: '20px' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                            <span style={{ fontWeight: 'bold', color: '#17a2b8' }}>Generating Summary...</span>
                            <span style={{ color: '#17a2b8' }}>{summaryProgress}%</span>
                        </div>
                        <div
                            style={{
                                width: '100%',
                                height: '8px',
                                backgroundColor: '#e9ecef',
                                borderRadius: '4px',
                                overflow: 'hidden',
                            }}
                        >
                            <div
                                style={{
                                    width: `${summaryProgress}%`,
                                    height: '100%',
                                    backgroundColor: '#17a2b8',
                                    transition: 'width 0.3s ease',
                                }}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

// Add CSS styles
const styles = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
    const styleElement = document.createElement('style');
    styleElement.innerHTML = styles;
    document.head.appendChild(styleElement);
}