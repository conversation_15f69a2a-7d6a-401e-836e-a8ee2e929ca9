import { EventEmitter } from 'eventemitter3';
import type {
    MedicalSession,
    RecoveryOptions,
    RecoveryProgress,
    SessionRecoveryInfo,
    SessionStorageConfig,
} from '../types/index';
import { SessionStorageManager } from './SessionStorageManager';

export class SessionRecoveryManager extends EventEmitter {
    private storageManager: SessionStorageManager;
    private config: SessionStorageConfig | null = null;

    constructor() {
        super();
        this.storageManager = new SessionStorageManager();
    }

    public async initialize(config: SessionStorageConfig): Promise<void> {
        this.config = config;
        await this.storageManager.initialize(config);
    }

    public async findRecoverableSessions(): Promise<SessionRecoveryInfo[]> {
        if (!this.config) {
            throw new Error('Recovery manager not initialized');
        }

        try {
            const sessionIds = await this.storageManager.listSessions();
            const recoverableSessions: SessionRecoveryInfo[] = [];

            for (const sessionId of sessionIds) {
                if (sessionId.startsWith('backup_')) {
                    continue;
                }

                try {
                    const session = await this.storageManager.getSession(sessionId);
                    if (session && this.isRecoverable(session)) {
                        const recoveryInfo = await this.analyzeSession(session);
                        recoverableSessions.push(recoveryInfo);
                    }
                } catch (error) {
                    recoverableSessions.push({
                        sessionId,
                        lastActivity: new Date(0),
                        dataIntegrity: 'corrupted',
                        recoveryType: 'metadata-only',
                        missingComponents: ['audio', 'transcript', 'metadata'],
                        estimatedRecoveryTime: 1000, // 1 second
                        riskLevel: 'high',
                    });
                }
            }

            return recoverableSessions.sort((a, b) =>
                b.lastActivity.getTime() - a.lastActivity.getTime()
            );
        } catch (error) {
            throw new Error(`Failed to find recoverable sessions: ${error}`);
        }
    }

    public async recoverSession(sessionId: string, options: RecoveryOptions): Promise<MedicalSession> {
        if (!this.config) {
            throw new Error('Recovery manager not initialized');
        }

        const progress: RecoveryProgress = {
            stage: 'detecting',
            progress: 0,
            currentStep: 'Detecting session data',
            estimatedTimeRemaining: 5000,
            errors: [],
        };

        try {
            this.emitProgress(sessionId, progress);

            // Stage 1: Detecting session data
            await this.delay(100);
            progress.stage = 'validating';
            progress.progress = 20;
            progress.currentStep = 'Validating session integrity';
            this.emitProgress(sessionId, progress);

            const session = await this.storageManager.getSession(sessionId);
            if (!session) {
                throw new Error('Session not found');
            }

            // Stage 2: Validating session data
            await this.delay(200);
            const validationResult = await this.validateSessionData(session);

            if (!validationResult.isValid && !options.fallbackToPartialRecovery) {
                throw new Error(`Session validation failed: ${validationResult.errors.join(', ')}`);
            }

            progress.stage = 'restoring';
            progress.progress = 50;
            progress.currentStep = 'Restoring session state';
            progress.errors = validationResult.errors;
            this.emitProgress(sessionId, progress);

            // Stage 3: Restoring session state
            await this.delay(300);
            const restoredSession = await this.restoreSessionState(session, validationResult);

            progress.stage = 'syncing';
            progress.progress = 80;
            progress.currentStep = 'Synchronizing with server';
            this.emitProgress(sessionId, progress);

            // Stage 4: Sync with server (if online)
            await this.delay(200);

            progress.stage = 'complete';
            progress.progress = 100;
            progress.currentStep = 'Recovery completed successfully';
            progress.estimatedTimeRemaining = 0;
            this.emitProgress(sessionId, progress);

            return restoredSession;
        } catch (error) {
            progress.stage = 'failed';
            progress.errors.push((error as Error).message);
            this.emitProgress(sessionId, progress);
            throw error;
        }
    }

    private async analyzeSession(session: MedicalSession): Promise<SessionRecoveryInfo> {
        const missingComponents: string[] = [];
        let dataIntegrity: 'complete' | 'partial' | 'corrupted' = 'complete';
        let recoveryType: 'full' | 'partial' | 'metadata-only' = 'full';
        let riskLevel: 'low' | 'medium' | 'high' = 'low';

        // Check audio data
        if (!session.audioData || session.audioData.chunks.length === 0) {
            missingComponents.push('audio');
            dataIntegrity = 'partial';
            recoveryType = 'partial';
            riskLevel = 'medium';
        }

        // Check transcript data
        if (!session.transcriptData || session.transcriptData.segments.length === 0) {
            missingComponents.push('transcript');
            dataIntegrity = 'partial';
            recoveryType = 'partial';
            riskLevel = 'medium';
        }

        // Check metadata
        if (!session.metadata) {
            missingComponents.push('metadata');
            dataIntegrity = 'corrupted';
            recoveryType = 'metadata-only';
            riskLevel = 'high';
        }

        // Estimate recovery time based on data size and complexity
        const baseTime = 500; // 500ms base
        const sizeMultiplier = Math.min(session.audioData?.totalSize || 0, 1000000) / 1000; // Up to 1MB
        const complexityMultiplier = missingComponents.length * 200; // 200ms per missing component
        const estimatedRecoveryTime = baseTime + sizeMultiplier + complexityMultiplier;

        return {
            sessionId: session.id,
            lastActivity: session.lastActivity,
            dataIntegrity,
            recoveryType,
            missingComponents,
            estimatedRecoveryTime,
            riskLevel,
        };
    }

    private isRecoverable(session: MedicalSession): boolean {
        const now = Date.now();
        const sessionAge = now - session.lastActivity.getTime();
        const maxAge = this.config?.retentionPeriod || 24 * 60 * 60 * 1000; // 24 hours

        return (
            sessionAge < maxAge &&
            session.status !== 'TERMINATED' &&
            (session.audioData?.chunks.length > 0 ||
             session.transcriptData?.segments.length > 0 ||
             Object.keys(session.metadata?.customFields || {}).length > 0)
        );
    }

    private async validateSessionData(session: MedicalSession): Promise<{
        isValid: boolean;
        errors: string[];
        warnings: string[];
    }> {
        const errors: string[] = [];
        const warnings: string[] = [];

        // Validate required fields
        if (!session.id) {
            errors.push('Missing session ID');
        }

        if (!session.startTime) {
            errors.push('Missing session start time');
        }

        if (!session.deviceInfo) {
            errors.push('Missing device information');
        }

        // Validate audio data consistency
        if (session.audioData) {
            const totalChunkSize = session.audioData.chunks.reduce((sum, chunk) => sum + chunk.size, 0);
            if (Math.abs(totalChunkSize - session.audioData.totalSize) > 1024) { // 1KB tolerance
                warnings.push('Audio size inconsistency detected');
            }
        }

        // Validate transcript data consistency
        if (session.transcriptData) {
            const totalChars = session.transcriptData.segments.reduce((sum, segment) => sum + segment.text.length, 0);
            if (Math.abs(totalChars - session.transcriptData.totalCharacters) > 10) { // 10 char tolerance
                warnings.push('Transcript length inconsistency detected');
            }
        }

        // Validate timestamps
        const now = Date.now();
        if (session.startTime.getTime() > now) {
            errors.push('Session start time is in the future');
        }

        if (session.lastActivity.getTime() > now) {
            warnings.push('Last activity time is in the future');
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings,
        };
    }

    private async restoreSessionState(
        session: MedicalSession,
        validationResult: { errors: string[]; warnings: string[] }
    ): Promise<MedicalSession> {
        const restoredSession = { ...session };

        if (validationResult.errors.length > 0) {
            console.warn('Session restoration with errors:', validationResult.errors);
        }
        if (validationResult.warnings.length > 0) {
            console.warn('Session restoration with warnings:', validationResult.warnings);
        }

        if (!restoredSession.audioData) {
            restoredSession.audioData = {
                totalDuration: 0,
                chunks: [],
                sampleRate: 16000,
                channels: 1,
                format: 'pcm',
                totalSize: 0,
            };
        }

        if (!restoredSession.transcriptData) {
            restoredSession.transcriptData = {
                segments: [],
                totalCharacters: 0,
                language: 'en-US',
                confidence: 0,
            };
        }

        if (!restoredSession.metadata) {
            restoredSession.metadata = {
                tags: [],
                priority: 'medium',
                sessionType: 'consultation',
                customFields: {},
            };
        }

        restoredSession.lastActivity = new Date();
        restoredSession.version++;

        restoredSession.checksum = await this.calculateSessionChecksum(restoredSession);

        return restoredSession;
    }

    private async calculateSessionChecksum(session: MedicalSession): Promise<string> {
        const sessionForHash = { ...session, checksum: '' };
        const data = JSON.stringify(sessionForHash);

        const encoder = new TextEncoder();
        const dataBuffer = encoder.encode(data);
        const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

        return hashHex;
    }

    private emitProgress(sessionId: string, progress: RecoveryProgress): void {
        this.emit('recovery-progress', { sessionId, progress });
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}