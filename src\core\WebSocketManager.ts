import { EventEmitter } from 'eventemitter3';
import type { ConnectionStatus, SDKConfig, WebSocketMessage } from '../types/index';

export class WebSocketManager extends EventEmitter {
    private config: SDKConfig | null = null;
    private websocket: WebSocket | null = null;
    private status: ConnectionStatus = 'disconnected';
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private messageQueue: WebSocketMessage[] = [];

    public async initialize(config: SDKConfig): Promise<void> {
        this.config = config;
    }

    public async connect(): Promise<void> {
        if (!this.config) {
            throw new Error('WebSocket manager not initialized');
        }

        return new Promise((resolve, reject) => {
            try {
                this.setStatus('connecting');
                this.websocket = new WebSocket(this.config!.websocketUrl);

                this.websocket.onopen = () => {
                    this.setStatus('connected');
                    this.reconnectAttempts = 0;
                    this.processMessageQueue();
                    resolve();
                };

                this.websocket.onerror = (error) => {
                    this.setStatus('failed');
                    reject(new Error(`WebSocket connection failed: ${error}`));
                };

                this.websocket.onclose = () => {
                    this.setStatus('disconnected');
                    this.handleReconnection();
                };

                this.websocket.onmessage = (event) => {
                    this.handleMessage(event.data);
                };
            } catch (error) {
                this.setStatus('failed');
                reject(error);
            }
        });
    }

    public async disconnect(): Promise<void> {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
        this.setStatus('disconnected');
    }

    public sendMessage(message: WebSocketMessage): void {
        if (this.status === 'connected' && this.websocket) {
            this.websocket.send(JSON.stringify(message));
        } else {
            this.messageQueue.push(message);
        }
    }

    public getStatus(): ConnectionStatus {
        return this.status;
    }

    public async cleanup(): Promise<void> {
        await this.disconnect();
        this.messageQueue = [];
        this.removeAllListeners();
    }

    private setStatus(newStatus: ConnectionStatus): void {
        if (this.status !== newStatus) {
            this.status = newStatus;
            this.emit('status-changed', newStatus);
        }
    }

    private handleMessage(data: string): void {
        try {
            const message: WebSocketMessage = JSON.parse(data);
            this.emit('message', message);
        } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
        }
    }

    private handleReconnection(): void {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff

            setTimeout(() => {
                this.setStatus('reconnecting');
                this.connect().catch(() => {
                    // Reconnection failed, will try again
                });
            }, delay);
        }
    }

    private processMessageQueue(): void {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            if (message) {
                this.sendMessage(message);
            }
        }
    }
}
