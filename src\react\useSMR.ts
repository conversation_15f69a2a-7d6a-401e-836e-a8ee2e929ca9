import { useCallback, useState } from 'react';
import type { SDKConfig, ErrorInfo } from '../types';

// Enhanced medical summary structures matching SMR service models
interface HistoryOfPresentIllness {
    onset?: string;
    location?: string;
    duration?: string;
    characteristics?: string;
    aggravating_factors: string[];
    relieving_factors: string[];
    timing?: string;
    severity?: string;
    associated_symptoms: string[];
}

interface ReviewOfSystems {
    constitutional?: string;
    cardiovascular?: string;
    respiratory?: string;
    gastrointestinal?: string;
    genitourinary?: string;
    musculoskeletal?: string;
    neurological?: string;
    psychiatric?: string;
}

interface EncounterSummary {
    chief_complaint: string;
    history_of_present_illness: HistoryOfPresentIllness;
    review_of_systems: ReviewOfSystems;
}

interface VitalSigns {
    blood_pressure?: string;
    heart_rate?: string;
    respiratory_rate?: string;
    temperature?: string;
    oxygen_saturation?: string;
    pain_score?: string;
}

interface PhysicalExamination {
    general?: string;
    cardiovascular?: string;
    respiratory?: string;
    abdominal?: string;
    neurological?: string;
    musculoskeletal?: string;
    skin?: string;
    other?: string;
}

interface DiagnosticResults {
    laboratory: string[];
    imaging: string[];
    other_tests: string[];
}

interface ClinicalFindings {
    vital_signs: VitalSigns;
    physical_examination: PhysicalExamination;
    diagnostic_results: DiagnosticResults;
}

interface PrimaryDiagnosis {
    diagnosis: string;
    icd10_code?: string;
    certainty: string;
}

interface DifferentialDiagnosis {
    diagnosis: string;
    likelihood: string;
    reasoning?: string;
}

interface ClinicalAssessment {
    primary_diagnosis: PrimaryDiagnosis;
    differential_diagnoses: DifferentialDiagnosis[];
    clinical_reasoning?: string;
    risk_stratification?: string;
}

interface Medication {
    name: string;
    dose?: string;
    route?: string;
    frequency?: string;
    duration?: string;
    indication?: string;
}

interface Referral {
    specialty: string;
    reason: string;
    urgency: string;
}

interface TreatmentPlan {
    medications: Medication[];
    procedures: string[];
    lifestyle_modifications: string[];
    patient_education: string[];
    referrals: Referral[];
}

interface FollowUp {
    timeline?: string;
    provider?: string;
    conditions?: string;
    warning_signs: string[];
}

interface ClinicalSummarySection {
    summary: string;
    key_findings: string[];
    pending_items: string[];
    care_coordination?: string;
}

interface QualityMetrics {
    completeness_score: number;
    confidence_level: string;
    missing_information: string[];
    documentation_flags: string[];
}

// Enhanced medical summary (primary interface)
interface EnhancedMedicalSummary {
    encounter_summary: EncounterSummary;
    clinical_findings: ClinicalFindings;
    clinical_assessment: ClinicalAssessment;
    treatment_plan: TreatmentPlan;
    follow_up: FollowUp;
    clinical_summary: ClinicalSummarySection;
    quality_metrics: QualityMetrics;
}

// Simplified medical summary (backward compatibility)
interface SimplifiedMedicalSummary {
    chief_complaint: string;
    symptoms: string[];
    medical_history: string;
    examination: string;
    assessment: string;
    treatment_plan: string;
    follow_up: string;
    summary: string;
}

// Union type for medical summaries
type MedicalSummary = EnhancedMedicalSummary | SimplifiedMedicalSummary;

// Enhanced summary response matching SMR service
interface SummaryResponse {
    session_id: string;
    summary: MedicalSummary;
    created_at: string;
    processing_time_ms: number;
    token_usage?: Record<string, number>;
    llm_provider: string;
    model_name: string;
    confidence_score?: number;
    metadata?: Record<string, any>;
}

// Enhanced job status response
interface SMRJobStatus {
    job_id: string;
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
    created_at: string;
    started_at?: string;
    completed_at?: string;
    progress_percent?: number;
    current_step?: string;
    result?: SummaryResponse;
    error?: string;
    error_details?: Record<string, any>;
    retry_count: number;
    max_retries: number;
    metadata?: Record<string, any>;
}

export interface PatientInfo {
    name?: string;
    age?: string;
    gender?: string;
    medical_record_number?: string;
    date_of_birth?: string;
    [key: string]: any;
}

export interface SMRRequest {
    text: string;
    sessionId?: string;
    provider?: 'azure' | 'ollama';
    model?: string;
    language?: string;
    template?: string;
    async?: boolean;
    patientId?: string;
    patientName?: string;
}

interface UseSMROptions {
    apiEndpoint?: string;
    sessionId?: string;
    options?: Partial<SDKConfig>;
    onProgress?: (progress: number) => void;
    onComplete?: (summary: SummaryResponse) => void;
    onError?: (error: ErrorInfo) => void;
}

interface UseSMRReturn {
    summarize: (request: SMRRequest) => Promise<SummaryResponse>;
    summarizeSync: (request: SMRRequest) => Promise<SummaryResponse>;
    summarizeAsync: (request: SMRRequest) => Promise<SMRJobStatus>;
    getJobStatus: (jobId: string) => Promise<SMRJobStatus>;
    getJobResult: (jobId: string) => Promise<SummaryResponse>;
    listJobs: () => Promise<SMRJobStatus[]>;
    getProviders: () => Promise<string[]>;
    getModels: () => Promise<string[]>;
    cancelJob: (jobId: string) => Promise<void>;
    getHealthStatus: () => Promise<any>;
    loading: boolean;
    error: string | null;
    isConnected: boolean;
    currentJob: SMRJobStatus | null;
    jobs: SMRJobStatus[];
}

const DEFAULT_SMR_CONFIG = {
    apiEndpoint: 'http://localhost:5002/api/smr',
    language: 'en',
    template: 'medical',
};

export function useSMR(props: UseSMROptions = {}): UseSMRReturn {
    const {
        apiEndpoint,
        sessionId,
        options,
        onProgress,
        onComplete,
        onError,
    } = props;

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isConnected, setIsConnected] = useState(true); // Always "connected" for HTTP API
    const [currentJob, setCurrentJob] = useState<SMRJobStatus | null>(null);
    const [jobs, setJobs] = useState<SMRJobStatus[]>([]);

    const makeRequest = useCallback(async (endpoint: string, requestOptions: RequestInit = {}) => {
        // Use default API endpoint if not provided
        const baseEndpoint = apiEndpoint || DEFAULT_SMR_CONFIG.apiEndpoint;
        
        // The SMR controller expects paths like /api/smr/api/v1/summary/sync
        // Ensure we're using the correct path format with api/v1 prefix
        const apiPath = endpoint.startsWith('/api/v1/') ? endpoint : `/api/v1/${endpoint.replace(/^\//, '')}`;
        
        // Construct the final URL
        const url = `${baseEndpoint}${apiPath}`;
        
        const headers = {
            'Content-Type': 'application/json',
            'x-api-key': options?.credentials?.apiKey || '',
            ...requestOptions.headers,
        };

        const response = await fetch(url, {
            ...requestOptions,
            headers,
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
            throw new Error(errorData.error || `HTTP ${response.status}`);
        }

        return response.json();
    }, [apiEndpoint, options?.credentials?.apiKey]);

    // Synchronous summarization
    const summarize = useCallback(async (request: SMRRequest): Promise<SummaryResponse> => {
        setLoading(true);
        setError(null);

        try {
            // Transform SDK request to SMR service SyncSummaryRequest format
            const payload = {
                session_data: {
                    session_id: request.sessionId || sessionId || `smr-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                    patient_id: request.patientId || null,
                    provider_id: null,
                    session_type: null,
                    created_at: new Date().toISOString(),
                    conversation_segments: [
                        {
                            speaker: 'user',
                            text: request.text,
                            timestamp: new Date().toISOString(),
                            confidence: 1.0,
                            metadata: {
                                language: request.language || 'en'
                            }
                        }
                    ],
                    patient_info: request.patientName ? { name: request.patientName } : null,
                    session_metadata: {
                        template: request.template,
                        language: request.language || 'en'
                    }
                }
            };

            const result = await makeRequest('summary/sync', {
                method: 'POST',
                body: JSON.stringify(payload),
            });

            if (onComplete) {
                onComplete(result);
            }

            return result;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Summarization failed';
            setError(errorMessage);
            if (onError) {
                onError({
                    message: errorMessage,
                    code: 'SUMMARIZATION_ERROR',
                    severity: 'high',
                    category: 'processing',
                });
            }
            throw err;
        } finally {
            setLoading(false);
        }
    }, [makeRequest, sessionId, onComplete, onError]);

    // Asynchronous summarization
    const summarizeAsync = useCallback(async (request: SMRRequest): Promise<SMRJobStatus> => {
        setLoading(true);
        setError(null);

        try {
            // Transform SDK request to SMR service AsyncSummaryRequest format
            const payload = {
                session_data: {
                    session_id: request.sessionId,
                    patient_id: request.patientId || null,
                    provider_id: null,
                    session_type: null,
                    created_at: new Date().toISOString(),
                    conversation_segments: [
                        {
                            speaker: 'user',
                            text: request.text,
                            timestamp: new Date().toISOString(),
                            confidence: 1.0,
                            metadata: {
                                language: request.language || 'en'
                            }
                        }
                    ],
                    patient_info: request.patientName ? { name: request.patientName } : null,
                    session_metadata: {
                        template: request.template,
                        language: request.language || 'en'
                    }
                }
            };

            const job = await makeRequest('summary/async', {
                method: 'POST',
                body: JSON.stringify(payload),
            });

            setCurrentJob(job);

            return job;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Async summarization failed';
            setError(errorMessage);
            if (onError) {
                onError({
                    message: errorMessage,
                    code: 'ASYNC_SUMMARIZATION_ERROR',
                    severity: 'high',
                    category: 'processing',
                });
            }
            throw err;
        } finally {
            setLoading(false);
        }
    }, [makeRequest, sessionId, onError]);

    // Get job status
    const getJobStatus = useCallback(async (jobId: string): Promise<SMRJobStatus> => {
        try {
            const status = await makeRequest(`jobs/${jobId}`);
            setCurrentJob(status);
            return status;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to get job status';
            setError(errorMessage);
            throw err;
        }
    }, [makeRequest]);

    // Get job result
    const getJobResult = useCallback(async (jobId: string): Promise<SummaryResponse> => {
        try {
            // Job results are included in the job status response
            const jobStatus = await makeRequest(`jobs/${jobId}`);
            if (jobStatus.result) {
                return jobStatus.result;
            }
            throw new Error('Job result not available');
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to get job result';
            setError(errorMessage);
            throw err;
        }
    }, [makeRequest]);

    // List all jobs
    const listJobs = useCallback(async (): Promise<SMRJobStatus[]> => {
        try {
            const jobList = await makeRequest('jobs');
            setJobs(jobList);
            return jobList;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to list jobs';
            setError(errorMessage);
            throw err;
        }
    }, [makeRequest]);

    // Cancel a job
    const cancelJob = useCallback(async (jobId: string): Promise<void> => {
        try {
            await makeRequest(`jobs/${jobId}`, {
                method: 'DELETE',
            });
            // Update current job if it was the cancelled one
            if (currentJob?.job_id === jobId) {
                setCurrentJob(null);
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to cancel job';
            setError(errorMessage);
            throw err;
        }
    }, [makeRequest, currentJob]);

    // Get health status
    const getHealthStatus = useCallback(async (): Promise<any> => {
        try {
            const health = await makeRequest('health');
            return health;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to get health status';
            setError(errorMessage);
            throw err;
        }
    }, [makeRequest]);

    // Get available providers
    const getProviders = useCallback(async (): Promise<string[]> => {
        try {
            // SMR service uses /models/info for model information
            const modelInfo = await makeRequest('/api/v1/models/info');
            return [modelInfo.provider]; // Return provider as array
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to get providers';
            setError(errorMessage);
            throw err;
        }
    }, [makeRequest]);

    // Get available models
    const getModels = useCallback(async (): Promise<string[]> => {
        try {
            // SMR service uses /models/info for model information
            const modelInfo = await makeRequest('/api/v1/models/info');
            return [modelInfo.model]; // Return model as array
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to get models';
            setError(errorMessage);
            throw err;
        }
    }, [makeRequest]);

    // HTTP-only implementation - no useEffect hooks needed for WebSocket connection

    // Alias for sync summarization (same as summarize function)
    const summarizeSync = summarize;

    return {
        summarize,
        summarizeSync,
        summarizeAsync,
        getJobStatus,
        getJobResult,
        listJobs,
        getProviders,
        getModels,
        cancelJob,
        getHealthStatus,
        loading,
        error,
        isConnected,
        currentJob,
        jobs,
    };
}

export default useSMR;
