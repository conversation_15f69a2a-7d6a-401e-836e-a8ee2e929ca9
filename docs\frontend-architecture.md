# Frontend Architecture

## Overview

The Agentic SDK provides a modern, React-first frontend architecture for building medical conversation applications with enterprise-grade reliability. The architecture follows a layered approach with clear separation of concerns, emphasizing real-time audio processing, session management, and declarative React patterns.

## Table of Contents

- [Architecture Principles](#architecture-principles)
- [Core Architecture Layers](#core-architecture-layers)
- [Component Architecture](#component-architecture)
- [State Management](#state-management)
- [Data Flow](#data-flow)
- [Event System](#event-system)
- [Performance Considerations](#performance-considerations)
- [Error Handling Strategy](#error-handling-strategy)
- [Security Architecture](#security-architecture)
- [Testing Strategy](#testing-strategy)

## Architecture Principles

### 1. **Separation of Concerns**
- **Core Layer**: Business logic and SDK functionality
- **React Layer**: UI components and hooks
- **Service Layer**: API communication and external integrations
- **Utils Layer**: Shared utilities and helpers

### 2. **Declarative API Design**
- React hooks provide declarative interfaces
- Configuration-driven initialization
- Event-driven architecture for real-time updates

### 3. **Singleton Pattern for SDK**
- Single SDK instance across application
- Centralized state management
- Resource optimization and consistency

### 4. **Error-First Design**
- Comprehensive error handling at every layer
- Graceful degradation for network issues
- User-friendly error messages with technical details

### 5. **Performance-First**
- Lazy loading and code splitting
- Memory management and cleanup
- Optimized audio processing pipelines

## Core Architecture Layers

### Layer 1: Core SDK (`src/core/`)

The foundational layer containing the main SDK logic and managers.

```typescript
// Core Components
AgenticSDK                 // Main SDK singleton
├── ConfigurationManager   // Configuration validation and management
├── SessionManager        // Medical session lifecycle
├── AudioCaptureManager   // Audio input processing
├── WebSocketManager      // Real-time communication
├── CloudSTTService       // Speech-to-text integration
├── ErrorHandler          // Centralized error management
└── Logger               // Logging and diagnostics
```

**Key Responsibilities:**
- SDK initialization and configuration
- Resource management and cleanup
- Event emission and handling
- Service orchestration

### Layer 2: React Integration (`src/react/`)

React-specific hooks and components for declarative integration.

```typescript
// React Hooks
useArcaSessionManager     // Session lifecycle management
useArcaSpeechToText      // Real-time transcription
useAudioCapture          // Audio recording
useArcaTextToSpeech      // Speech synthesis
useSMR                   // Medical summarization
```

**Key Features:**
- Automatic resource cleanup
- Memoized callbacks and state
- Error boundary compatibility
- TypeScript support

### Layer 3: Services (`src/services/`)

External API communication and service integrations.

```typescript
// Service Components
ApiClient                // HTTP client with retry logic
SessionApi              // Medical session API
├── Authentication      // API key and token management
├── Error Mapping       // HTTP to domain error translation
└── Request/Response    // Type-safe API interfaces
```

### Layer 4: Types & Utilities (`src/types/`, `src/utils/`)

Shared type definitions and utility functions.

```typescript
// Type System
SDKConfig               // Main configuration interface
MedicalSession         // Session data structure
AudioConfig            // Audio processing settings
ErrorInfo              // Standardized error format

// Utilities
DeviceInfoCollector    // Device capability detection
createError            // Error factory functions
```

## Component Architecture

### SDK Instance Management

```typescript
// Singleton Pattern Implementation
class AgenticSDK extends EventEmitter<SDKEvents> {
    private static instance: AgenticSDK | null = null;

    public static getInstance(): AgenticSDK {
        if (!AgenticSDK.instance) {
            AgenticSDK.instance = new AgenticSDK();
        }
        return AgenticSDK.instance;
    }
}
```

### React Hook Architecture

```typescript
// Declarative Hook Pattern
export function useArcaSessionManager(options: IArcaSessionOptions): IArcaSessionManagerReturn {
    // State management
    const [session, setSession] = useState<MedicalSession | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<ErrorInfo | null>(null);

    // SDK integration
    const sdk = useMemo(() => AgenticSDK.getInstance(), []);

    // Memoized actions
    const createSession = useCallback(async (metadata) => {
        // Implementation
    }, [sdk]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            // Cleanup logic
        };
    }, []);
}
```

### Component Composition

```typescript
// Recommended Component Structure
function MedicalConsole() {
    // Session management
    const sessionManager = useArcaSessionManager({
        doctorId: 'dr-123',
        patientId: 'pt-456',
        // ... other config
    });

    // Audio processing
    const audioCapture = useAudioCapture({
        onAudioData: (data) => speechToText.sendAudioData(data),
    });

    // Speech-to-text
    const speechToText = useArcaSpeechToText({
        sessionId: sessionManager.session?.id || '',
        onTranscript: handleTranscript,
    });

    // Component logic and render
}
```

## State Management

### 1. **SDK-Level State**

Centralized state management through the main SDK instance:

```typescript
interface SDKState {
    currentState: 'idle' | 'initializing' | 'connected' | 'processing' | 'error';
    isInitialized: boolean;
    currentSession: MedicalSession | null;
    performanceMetrics: PerformanceMetrics;
    connectionStatus: ConnectionStatus;
}
```

### 2. **Hook-Level State**

Each React hook manages its own state with clear interfaces:

```typescript
// Session Manager State
interface SessionManagerState {
    session: MedicalSession | null;
    isLoading: boolean;
    error: ErrorInfo | null;
}

// Audio Capture State
interface AudioCaptureState {
    isRecording: boolean;
    deviceStatus: AudioDeviceStatus | null;
    error: ErrorInfo | null;
    isReady: boolean;
}
```

### 3. **Session Persistence**

Multi-layered storage strategy:

```typescript
// Storage Hierarchy
IndexedDB (Primary)
├── Full session data
├── Audio chunks
├── Transcripts
└── Metadata

LocalStorage (Fallback)
├── Session IDs
├── Basic metadata
└── Recovery information

Memory (Runtime)
├── Current session
├── Performance metrics
└── Device status
```

## Data Flow

### 1. **Audio Processing Flow**

```
Microphone → AudioCaptureManager → Audio Processing → Chunk Creation → WebSocket Send → STT Service → Transcription Result → React Hook Update → UI Update
```

## Event System

### 1. **SDK Events**

The SDK uses a centralized event system for real-time communication:

```typescript
interface SDKEvents {
    'state-changed': (state: SDKState) => void;
    'connection-changed': (status: ConnectionStatus) => void;
    'transcription': (result: TranscriptionResult) => void;
    'audio-level': (level: number) => void;
    'error': (error: ErrorInfo) => void;
    'session:created': (session: MedicalSession) => void;
    'session:ended': (sessionId: string) => void;
}
```

### 2. **React Event Integration**

```typescript
// Hook Event Subscription Pattern
useEffect(() => {
    const handleTranscription = (result: TranscriptionResult) => {
        setTranscript(prev => prev + ' ' + result.text);
    };

    sdk.on('transcription', handleTranscription);

    return () => {
        sdk.off('transcription', handleTranscription);
    };
}, [sdk]);
```

## Performance Considerations

### 1. **Memory Management**

```typescript
// Automatic Cleanup Pattern
useEffect(() => {
    return () => {
        // Cleanup on unmount
        sessionManager.endSession();
        audioCapture.stopRecording();
        speechToText.stopTranscription();
    };
}, []);
```

### 2. **Audio Processing Optimization**

- **Chunked Processing**: Audio processed in configurable chunks (512-8192 samples)
- **Background Processing**: Web Workers for heavy audio operations
- **Memory Pooling**: Reuse ArrayBuffers to reduce GC pressure

```typescript
// Audio Configuration Presets
const realtimePreset = {
    chunkSize: 512,      // Low latency
    noiseCancellation: false,
    format: 'pcm'
};

const highQualityPreset = {
    chunkSize: 4096,     // Higher quality
    noiseCancellation: true,
    format: 'wav'
};
```

## Error Handling Strategy

### 1. **Error Classification**

```typescript
interface ErrorInfo {
    code: string;
    message: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    category: 'network' | 'audio' | 'processing' | 'authentication' | 'configuration';
    recoverable: boolean;
}
```

### 2. **Error Recovery Patterns**

```typescript
// Automatic Recovery
const handleError = async (error: ErrorInfo) => {
    switch (error.category) {
        case 'network':
            // Retry with exponential backoff
            await retryWithBackoff(() => reconnect());
            break;
        case 'audio':
            // Fallback to different device
            await switchAudioDevice();
            break;
        case 'authentication':
            // Refresh tokens
            await refreshCredentials();
            break;
    }
};
```

## Security Architecture

### 1. **Data Protection**

- **Encryption at Rest**: AES-256 encryption for stored session data
- **Encryption in Transit**: TLS 1.3 for all communications
- **Key Management**: Secure key derivation and rotation

### 2. **Authentication**

- **API Key Authentication**: Secure API key management
- **Token Refresh**: Automatic token renewal
- **Device Fingerprinting**: Device-based security

### 3. **Privacy Compliance**

- **HIPAA Compliance**: Medical data protection
- **Data Minimization**: Store only necessary data
- **User Consent**: Explicit permission for data usage

## Testing Strategy

### 1. **Unit Testing**

```typescript
// SDK Unit Tests
describe('AgenticSDK', () => {
    let sdk: AgenticSDK;

    beforeEach(() => {
        sdk = AgenticSDK.getInstance();
    });

    afterEach(async () => {
        await sdk.cleanup();
    });

    test('should initialize successfully', async () => {
        await expect(sdk.initialize(validConfig)).resolves.not.toThrow();
    });
});
```

### 2. **React Hook Testing**

```typescript
// Hook Testing Pattern
import { renderHook, act } from '@testing-library/react';

test('useArcaSessionManager creates session', async () => {
    const { result } = renderHook(() => useArcaSessionManager(mockOptions));

    await act(async () => {
        await result.current.createSession();
    });

    expect(result.current.session).toBeDefined();
});
```

## Development Patterns

### 1. **Recommended File Structure**

```
src/
├── components/           # React components
│   ├── MedicalConsole/
│   ├── AudioControls/
│   └── TranscriptDisplay/
├── hooks/               # Custom React hooks
│   ├── useSessionManager.ts
│   └── useAudioRecording.ts
├── services/           # API and external services
├── utils/              # Shared utilities
├── types/              # TypeScript definitions
└── constants/          # Application constants
```

### 2. **Configuration Management**

```typescript
// Environment-based configuration
const getConfig = (): SDKConfig => {
    const baseConfig = {
        apiEndpoint: process.env.REACT_APP_API_ENDPOINT!,
        websocketUrl: process.env.REACT_APP_WS_ENDPOINT!,
        credentials: { apiKey: process.env.REACT_APP_API_KEY! },
    };

    if (process.env.NODE_ENV === 'development') {
        return { ...baseConfig, logging: { level: 'debug' } };
    }

    return baseConfig;
};
```

## Conclusion

The Agentic SDK frontend architecture provides a robust, scalable foundation for building medical conversation applications. The layered architecture ensures clear separation of concerns while the React integration provides a modern, declarative development experience.

Key architectural strengths include:

- **Type Safety**: Comprehensive TypeScript coverage
- **Performance**: Optimized audio processing and memory management
- **Reliability**: Comprehensive error handling and recovery
- **Developer Experience**: Declarative React hooks and clear APIs
- **Scalability**: Modular design supporting feature growth
- **Security**: Enterprise-grade data protection and privacy compliance

This architecture supports both simple implementations and complex, enterprise-scale medical applications while maintaining code quality and developer productivity.