import type { AudioConfig, SDKConfig } from '../types/index';

export class ConfigurationManager {
    private config: SDKConfig | null = null;
    private defaultConfig: SDKConfig;

    constructor() {
        this.defaultConfig = this.getDefaultConfig();
    }

    public async initialize(userConfig: Partial<SDKConfig>): Promise<void> {
        this.validateConfig(userConfig);

        this.config = this.mergeConfigs(this.defaultConfig, userConfig);

        this.applyEnvironmentSettings();

        this.validateFinalConfig();
    }

    public getConfig(): SDKConfig {
        if (!this.config) {
            throw new Error('Configuration not initialized');
        }
        return { ...this.config };
    }

    public updateConfig(updates: Partial<SDKConfig>): void {
        if (!this.config) {
            throw new Error('Configuration not initialized');
        }

        this.config = this.mergeConfigs(this.config, updates);
        this.validateFinalConfig();
    }

    private getDefaultConfig(): SDKConfig {
        return {
            apiEndpoint: 'https://api.arcaai.com',
            websocketUrl: 'wss://ws.arcaai.com',
            sttProvider: 'azure',
            audioSettings: {
                sampleRate: 16000,
                format: 'pcm',
                channels: 1,
                bitDepth: 16,
                chunkSize: 1024,
                noiseCancellation: true,
                echoCancellation: true,
                autoGainControl: true,
            },
            errorReporting: {
                enabled: true,
                includeStackTrace: false,
                maxErrorsPerSession: 10,
            },
            logging: {
                level: 'info',
                console: true,
                remote: false,
            },
            environment: 'production',
        };
    }

    private validateConfig(config: Partial<SDKConfig>): void {
        if (!config.apiEndpoint && !config.websocketUrl) {
            throw new Error('Either apiEndpoint or websocketUrl must be provided');
        }

        if (config.audioSettings) {
            this.validateAudioConfig(config.audioSettings);
        }

        if (config.credentials?.apiKey && config.credentials.apiKey.length < 10) {
            throw new Error('API key must be at least 10 characters long');
        }
    }

    private validateAudioConfig(audioConfig: Partial<AudioConfig>): void {
        if (audioConfig.sampleRate && ![8000, 16000, 22050, 44100, 48000].includes(audioConfig.sampleRate)) {
            throw new Error('Invalid sample rate. Supported: 8000, 16000, 22050, 44100, 48000');
        }

        if (audioConfig.channels && ![1, 2].includes(audioConfig.channels)) {
            throw new Error('Invalid channel count. Supported: 1 (mono), 2 (stereo)');
        }

        if (audioConfig.bitDepth && ![8, 16, 24, 32].includes(audioConfig.bitDepth)) {
            throw new Error('Invalid bit depth. Supported: 8, 16, 24, 32');
        }
    }

    private mergeConfigs(base: SDKConfig, override: Partial<SDKConfig>): SDKConfig {
        const merged = { ...base };

        for (const [key, value] of Object.entries(override)) {
            if (value !== undefined) {
                const typedKey = key as keyof SDKConfig;
                if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                    (merged as any)[typedKey] = { ...(merged as any)[typedKey], ...value };
                } else {
                    (merged as any)[typedKey] = value;
                }
            }
        }

        return merged;
    }

    private applyEnvironmentSettings(): void {
        if (!this.config) return;

        const env = this.config.environment || this.detectEnvironment();
        this.config.environment = env;

        switch (env) {
            case 'development':
                this.config.logging.level = 'debug';
                this.config.logging.console = true;
                this.config.errorReporting.includeStackTrace = true;
                break;

            case 'staging':
                this.config.logging.level = 'info';
                this.config.errorReporting.enabled = true;
                break;

            case 'production':
                this.config.logging.level = 'warn';
                this.config.errorReporting.includeStackTrace = false;
                break;
        }
    }

    private detectEnvironment(): 'development' | 'staging' | 'production' {
        // Check for common environment variables
        if (typeof process !== 'undefined' && process.env) {
            const nodeEnv = process.env.NODE_ENV;
            if (nodeEnv && ['development', 'staging', 'production'].includes(nodeEnv)) {
                return nodeEnv as 'development' | 'staging' | 'production';
            }
        }

        // Check for development indicators
        if (typeof window !== 'undefined') {
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' || window.location.hostname.includes('dev')) {
                return 'development';
            }

            if (window.location.hostname.includes('staging') || window.location.hostname.includes('test')) {
                return 'staging';
            }
        }

        return 'production';
    }

    private validateFinalConfig(): void {
        if (!this.config) return;

        // Validate URLs
        try {
            new URL(this.config.apiEndpoint);
            new URL(this.config.websocketUrl);
        } catch {
            throw new Error('Invalid URL format in configuration');
        }

        // Validate audio settings
        if (this.config.audioSettings.chunkSize <= 0) {
            throw new Error('Audio chunk size must be positive');
        }

        // Validate error reporting
        if (this.config.errorReporting.maxErrorsPerSession <= 0) {
            throw new Error('Max errors per session must be positive');
        }
    }
}
