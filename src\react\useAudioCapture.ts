import { useCallback, useEffect, useRef, useState } from 'react';
import { AudioCaptureManager } from '../core/AudioCaptureManager';
import type { AudioConfig, AudioDeviceStatus, ErrorInfo } from '../types/index';

const DEFAULT_AUDIO_CONFIG: AudioConfig = {
    sampleRate: 16000,
    channels: 1,
    bitDepth: 16,
    format: 'pcm',
    chunkSize: 1024,
    noiseCancellation: false,
    echoCancellation: false,
    autoGainControl: false,
};

interface UseAudioCaptureProps {
    options?: Partial<AudioConfig>;
    autoStart?: boolean;
    onAudioData?: (data: ArrayBuffer) => void;
    onError?: (error: ErrorInfo) => void;
}

interface UseAudioCaptureReturn {
    isRecording: boolean;
    deviceStatus: AudioDeviceStatus | null;
    startRecording: () => Promise<void>;
    stopRecording: () => Promise<void>;
    getDeviceStatus: () => Promise<AudioDeviceStatus | null>;
    error: ErrorInfo | null;
    isReady: boolean;
}

export function useAudioCapture(props: UseAudioCaptureProps): UseAudioCaptureReturn {
    const { options, autoStart = false, onAudioData, onError } = props;

    const [isRecording, setIsRecording] = useState(false);
    const [deviceStatus, setDeviceStatus] = useState<AudioDeviceStatus | null>(null);
    const [error, setError] = useState<ErrorInfo | null>(null);
    const [isReady, setIsReady] = useState(false);

    const audioCaptureManagerRef = useRef<AudioCaptureManager | null>(null);
    const audioDataCallbackRef = useRef(onAudioData);
    const errorCallbackRef = useRef(onError);

    // Update callback refs when props change
    useEffect(() => {
        audioDataCallbackRef.current = onAudioData;
        errorCallbackRef.current = onError;
    }, [onAudioData, onError]);

    // Initialize
    useEffect(() => {
        const initialize = async () => {
            try {
                const audioConfig: AudioConfig = {
                    ...DEFAULT_AUDIO_CONFIG,
                    ...options,
                };

                audioCaptureManagerRef.current = new AudioCaptureManager();
                await audioCaptureManagerRef.current.initialize(audioConfig);

                const handleCaptureStarted = () => {
                    setIsRecording(true);
                    setError(null);
                };

                const handleCaptureStopped = () => {
                    setIsRecording(false);
                };

                const handleAudioData = (data: ArrayBuffer) => {
                    audioDataCallbackRef.current?.(data);
                };

                const handleError = (err: any) => {
                    const errorInfo: ErrorInfo = {
                        code: 'AUDIO_CAPTURE_ERROR',
                        message: err.message || 'Audio capture error occurred',
                        severity: 'high',
                        category: 'audio',
                        details: { originalError: err },
                    };
                    setError(errorInfo);
                    errorCallbackRef.current?.(errorInfo);
                    setIsRecording(false);
                };

                audioCaptureManagerRef.current.on('captureStarted', handleCaptureStarted);
                audioCaptureManagerRef.current.on('captureStopped', handleCaptureStopped);
                audioCaptureManagerRef.current.on('audioData', handleAudioData);
                audioCaptureManagerRef.current.on('error', handleError);

                setIsReady(true);

                if (autoStart) {
                    await startRecordingInternal();
                }
            } catch (err: any) {
                const errorInfo: ErrorInfo = {
                    code: 'INITIALIZATION_ERROR',
                    message: err.message || 'Failed to initialize audio capture',
                    severity: 'critical',
                    category: 'configuration',
                    details: { originalError: err },
                };
                setError(errorInfo);
                errorCallbackRef.current?.(errorInfo);
            }
        };

        initialize();

        return () => {
            audioCaptureManagerRef.current?.cleanup();
            audioCaptureManagerRef.current = null;
        };
    }, [autoStart, options]);

    const startRecordingInternal = async (): Promise<void> => {
        if (!audioCaptureManagerRef.current) {
            throw new Error('Audio capture not initialized');
        }

        await audioCaptureManagerRef.current.startCapture();
    };

    const startRecording = useCallback(async (): Promise<void> => {
        if (!isReady || isRecording) return;

        try {
            setError(null);
            await startRecordingInternal();
        } catch (err: any) {
            const errorInfo: ErrorInfo = {
                code: 'START_RECORDING_ERROR',
                message: err.message || 'Failed to start recording',
                severity: 'high',
                category: 'audio',
                details: { originalError: err },
            };
            setError(errorInfo);
            errorCallbackRef.current?.(errorInfo);
        }
    }, [isReady, isRecording]);

    const stopRecording = useCallback(async (): Promise<void> => {
        if (!audioCaptureManagerRef.current || !isRecording) return;

        try {
            await audioCaptureManagerRef.current.stopCapture();
            setError(null);
        } catch (err: any) {
            const errorInfo: ErrorInfo = {
                code: 'STOP_RECORDING_ERROR',
                message: err.message || 'Failed to stop recording',
                severity: 'medium',
                category: 'audio',
                details: { originalError: err },
            };
            setError(errorInfo);
            errorCallbackRef.current?.(errorInfo);
        }
    }, [isRecording]);

    const getDeviceStatus = useCallback(async (): Promise<AudioDeviceStatus | null> => {
        if (!audioCaptureManagerRef.current) return null;

        try {
            const status = await audioCaptureManagerRef.current.getDeviceStatus();
            setDeviceStatus(status);
            return status;
        } catch (err: any) {
            const errorInfo: ErrorInfo = {
                code: 'DEVICE_STATUS_ERROR',
                message: err.message || 'Failed to get device status',
                severity: 'low',
                category: 'audio',
                details: { originalError: err },
            };
            setError(errorInfo);
            errorCallbackRef.current?.(errorInfo);
            return null;
        }
    }, []);

    return {
        isRecording,
        deviceStatus,
        startRecording,
        stopRecording,
        getDeviceStatus,
        error,
        isReady,
    };
}
