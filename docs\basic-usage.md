# Basic Usage Guide

This guide demonstrates how to build a complete medical consultation application using the Agentic Audio SDK. We'll walk through creating a functional app with session management, speech-to-text, and text-to-speech capabilities using the centralized configuration approach.

## Prerequisites

- Completed [installation and setup](./installation.md)
- Basic understanding of React and TypeScript
- SDK configuration file created (see [Installation Guide](./installation.md#configuration-setup))

## Configuration Setup

First, ensure you have your SDK configuration file ready:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: "your-api-key-here",
  },
};
```

## Step 1: Basic Session Management

Let's start by creating a simple consultation app with session management:

```typescript
// src/components/ConsultationApp.tsx
import React, { useState } from "react";
import { useArcaSessionManager } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

export function ConsultationApp() {
  const [doctorName] = useState("Dr. <PERSON>");
  const [patientName] = useState("John Doe");

  const {
    session,
    isLoading,
    error,
    createSession,
    startSession,
    pauseSession,
    endSession,
  } = useArcaSessionManager({
    doctorId: "doctor-123",
    doctorName,
    patientId: "patient-456",
    patientName,
    options: SDK_CONFIG_OPTIONS,
    onError: (error) => {
      console.error("Session error:", error);
    },
  });

  const handleStartConsultation = async () => {
    try {
      await createSession({
        sessionType: "consultation",
        priority: "medium",
        tags: ["routine-checkup"],
      });
      await startSession();
    } catch (err) {
      console.error("Failed to start consultation:", err);
    }
  };

  const handleEndConsultation = async () => {
    try {
      await endSession();
    } catch (err) {
      console.error("Failed to end consultation:", err);
    }
  };

  if (isLoading) {
    return <div>Initializing session manager...</div>;
  }

  if (error) {
    return <div style={{ color: "red" }}>Error: {error.message}</div>;
  }

  return (
    <div style={{ padding: "20px", maxWidth: "600px", margin: "0 auto" }}>
      <h1>Medical Consultation Console</h1>

      <div style={{ marginBottom: "20px" }}>
        <h3>Session Information</h3>
        <p>
          <strong>Doctor:</strong> {doctorName}
        </p>
        <p>
          <strong>Patient:</strong> {patientName}
        </p>
        <p>
          <strong>Status:</strong> {session?.status || "No active session"}
        </p>
        {session && (
          <p>
            <strong>Session ID:</strong> {session.id.substring(0, 8)}...
          </p>
        )}
      </div>

      <div>
        {!session ? (
          <button
            onClick={handleStartConsultation}
            style={{
              padding: "10px 20px",
              backgroundColor: "#007bff",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Start Consultation
          </button>
        ) : (
          <div>
            <button
              onClick={handleEndConsultation}
              style={{
                padding: "10px 20px",
                backgroundColor: "#dc3545",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
              }}
            >
              End Consultation
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
```

## Step 2: Adding Speech-to-Text

Now let's add speech-to-text functionality to our consultation app:

```typescript
// src/components/ConsultationWithSTT.tsx
import React, { useState } from "react";
import {
  useArcaSessionManager,
  useArcaSpeechToText,
  useAudioCapture,
} from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

export function ConsultationWithSTT() {
  const [doctorName] = useState("Dr. Smith");
  const [patientName] = useState("John Doe");
  const [transcript, setTranscript] = useState("");
  const [currentChunk, setCurrentChunk] = useState("");

  // Session Manager
  const sessionManager = useArcaSessionManager({
    doctorId: "doctor-123",
    doctorName,
    patientId: "patient-456",
    patientName,
    options: SDK_CONFIG_OPTIONS,
  });

  // Speech-to-Text
  const speechToText = useArcaSpeechToText({
    sessionId: sessionManager.session?.id || "",
    language: "en-US",
    options: SDK_CONFIG_OPTIONS,
    onTranscript: (text, isFinal) => {
      if (isFinal) {
        setTranscript((prev) => prev + " " + text);
        setCurrentChunk("");
      } else {
        setCurrentChunk(text);
      }
    },
    onError: (error) => {
      console.error("STT Error:", error);
    },
  });

  // Audio Capture
  const audioCapture = useAudioCapture({
    onAudioData: (data) => {
      if (sessionManager.session?.status === "ACTIVE") {
        speechToText.sendAudioData(data);
      }
    },
    onError: (error) => {
      console.error("Audio Error:", error);
    },
    options: {
      sampleRate: 16000,
      channels: 1,
      noiseCancellation: true,
      echoCancellation: true,
    },
  });

  const handleStartConsultation = async () => {
    try {
      await sessionManager.createSession({
        sessionType: "consultation",
        priority: "medium",
      });
      await sessionManager.startSession();
    } catch (err) {
      console.error("Failed to start consultation:", err);
    }
  };

  const handleStartRecording = async () => {
    try {
      await speechToText.startTranscription();
      await audioCapture.startRecording();
    } catch (err) {
      console.error("Failed to start recording:", err);
    }
  };

  const handleStopRecording = async () => {
    try {
      await audioCapture.stopRecording();
      await speechToText.stopTranscription();
    } catch (err) {
      console.error("Failed to stop recording:", err);
    }
  };

  const canUseSTT = sessionManager.session?.status === "ACTIVE";

  return (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <h1>Medical Consultation with Speech-to-Text</h1>

      {/* Session Status */}
      <div
        style={{
          padding: "15px",
          backgroundColor: "#f8f9fa",
          borderRadius: "4px",
          marginBottom: "20px",
        }}
      >
        <h3>Session Status</h3>
        <p>
          <strong>Status:</strong>{" "}
          {sessionManager.session?.status || "No session"}
        </p>
        {sessionManager.session && (
          <p>
            <strong>Session ID:</strong>{" "}
            {sessionManager.session.id.substring(0, 8)}...
          </p>
        )}
      </div>

      {/* Session Controls */}
      <div style={{ marginBottom: "20px" }}>
        {!sessionManager.session ? (
          <button onClick={handleStartConsultation}>Start Consultation</button>
        ) : (
          <button onClick={sessionManager.endSession}>End Consultation</button>
        )}
      </div>

      {/* Audio Controls */}
      <div style={{ marginBottom: "20px" }}>
        <h3>Audio Recording</h3>
        <p>
          Recording: {audioCapture.isRecording ? "🔴 Active" : "⚫ Stopped"}
        </p>
        <p>Ready: {audioCapture.isReady ? "✅" : "❌"}</p>

        <div>
          <button
            onClick={handleStartRecording}
            disabled={
              !canUseSTT || !audioCapture.isReady || audioCapture.isRecording
            }
            style={{ marginRight: "10px" }}
          >
            Start Recording
          </button>
          <button
            onClick={handleStopRecording}
            disabled={!audioCapture.isRecording}
          >
            Stop Recording
          </button>
        </div>
      </div>

      {/* Transcript Display */}
      <div style={{ marginBottom: "20px" }}>
        <h3>Transcript</h3>

        {/* Live transcript */}
        {currentChunk && (
          <div
            style={{
              padding: "10px",
              backgroundColor: "#e3f2fd",
              borderRadius: "4px",
              marginBottom: "10px",
              fontStyle: "italic",
            }}
          >
            <strong>Live:</strong> {currentChunk}
          </div>
        )}

        {/* Final transcript */}
        <div
          style={{
            padding: "10px",
            border: "1px solid #ddd",
            borderRadius: "4px",
            minHeight: "200px",
            backgroundColor: "#f9f9f9",
          }}
        >
          {transcript ||
            "No transcript yet. Start recording to see text appear here."}
        </div>
      </div>

      {/* Errors */}
      {(sessionManager.error || speechToText.error || audioCapture.error) && (
        <div style={{ color: "red", marginTop: "20px" }}>
          <h4>Errors:</h4>
          {sessionManager.error && (
            <p>Session: {sessionManager.error.message}</p>
          )}
          {speechToText.error && <p>STT: {speechToText.error.message}</p>}
          {audioCapture.error && <p>Audio: {audioCapture.error.message}</p>}
        </div>
      )}
    </div>
  );
}
```

## Step 3: Adding Text-to-Speech

Let's enhance our app with text-to-speech capabilities:

```typescript
// src/components/FullConsultationApp.tsx
import React, { useState } from "react";
import {
  useArcaSessionManager,
  useArcaSpeechToText,
  useAudioCapture,
  useArcaTextToSpeech,
} from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

export function FullConsultationApp() {
  const [doctorName] = useState("Dr. Smith");
  const [patientName] = useState("John Doe");
  const [transcript, setTranscript] = useState("");
  const [currentChunk, setCurrentChunk] = useState("");
  const [ttsText, setTtsText] = useState(
    "Thank you for visiting today. How are you feeling?"
  );

  // Session Manager
  const sessionManager = useArcaSessionManager({
    doctorId: "doctor-123",
    doctorName,
    patientId: "patient-456",
    patientName,
    options: SDK_CONFIG_OPTIONS,
  });

  // Speech-to-Text
  const speechToText = useArcaSpeechToText({
    sessionId: sessionManager.session?.id || "",
    language: "en-US",
    options: SDK_CONFIG_OPTIONS,
    onTranscript: (text, isFinal) => {
      if (isFinal) {
        setTranscript((prev) => prev + " " + text);
        setCurrentChunk("");
      } else {
        setCurrentChunk(text);
      }
    },
  });

  // Audio Capture
  const audioCapture = useAudioCapture({
    onAudioData: (data) => {
      if (sessionManager.session?.status === "ACTIVE") {
        speechToText.sendAudioData(data);
      }
    },
    options: {
      sampleRate: 16000,
      channels: 1,
      noiseCancellation: true,
    },
  });

  // Text-to-Speech
  const [
    sendTextData,
    ttsLoading,
    ttsError,
    ttsConnect,
    ttsDisconnect,
    ttsIsConnected,
    ttsAudioData,
    downloadAudio,
  ] = useArcaTextToSpeech({
    apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
    socketPath: "/tts",
    language: "en",
    storeAudio: true,
    options: SDK_CONFIG_OPTIONS,
  });

  const handleStartConsultation = async () => {
    try {
      await sessionManager.createSession({
        sessionType: "consultation",
        priority: "medium",
      });
      await sessionManager.startSession();

      // Auto-connect TTS when session starts
      ttsConnect();
    } catch (err) {
      console.error("Failed to start consultation:", err);
    }
  };

  const handleEndConsultation = async () => {
    try {
      ttsDisconnect();
      await sessionManager.endSession();
    } catch (err) {
      console.error("Failed to end consultation:", err);
    }
  };

  const handleSynthesizeSpeech = () => {
    if (ttsText.trim() && ttsIsConnected) {
      sendTextData(ttsText, {
        voice: "en-US-JennyNeural",
        rate: 1.0,
      });
    }
  };

  const handleDownloadAudio = () => {
    if (ttsAudioData && ttsAudioData.length > 0) {
      const url = downloadAudio();
      if (url) {
        const link = document.createElement("a");
        link.href = url;
        link.download = `consultation-audio-${Date.now()}.mp3`;
        link.click();
        URL.revokeObjectURL(url);
      }
    }
  };

  const canUseFeatures = sessionManager.session?.status === "ACTIVE";

  return (
    <div style={{ padding: "20px", maxWidth: "1000px", margin: "0 auto" }}>
      <h1>Complete Medical Consultation Console</h1>

      {/* Session Management */}
      <section
        style={{
          padding: "15px",
          backgroundColor: "#f8f9fa",
          borderRadius: "4px",
          marginBottom: "20px",
        }}
      >
        <h3>Session Management</h3>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: "20px",
          }}
        >
          <div>
            <p>
              <strong>Doctor:</strong> {doctorName}
            </p>
            <p>
              <strong>Patient:</strong> {patientName}
            </p>
          </div>
          <div>
            <p>
              <strong>Status:</strong>{" "}
              {sessionManager.session?.status || "No session"}
            </p>
            {sessionManager.session && (
              <p>
                <strong>ID:</strong> {sessionManager.session.id.substring(0, 8)}
                ...
              </p>
            )}
          </div>
        </div>
        <div>
          {!sessionManager.session ? (
            <button onClick={handleStartConsultation}>
              Start Consultation
            </button>
          ) : (
            <button onClick={handleEndConsultation}>End Consultation</button>
          )}
        </div>
      </section>

      {/* Audio Recording Section */}
      <section style={{ marginBottom: "20px" }}>
        <h3>Speech-to-Text Recording</h3>
        <div
          style={{
            display: "flex",
            gap: "10px",
            alignItems: "center",
            marginBottom: "10px",
          }}
        >
          <span>Status:</span>
          <span>
            {audioCapture.isRecording ? "🔴 Recording" : "⚫ Stopped"}
          </span>
          <span>{audioCapture.isReady ? "✅ Ready" : "❌ Not Ready"}</span>
        </div>

        <div>
          <button
            onClick={async () => {
              await speechToText.startTranscription();
              await audioCapture.startRecording();
            }}
            disabled={
              !canUseFeatures ||
              !audioCapture.isReady ||
              audioCapture.isRecording
            }
            style={{ marginRight: "10px" }}
          >
            Start Recording
          </button>
          <button
            onClick={async () => {
              await audioCapture.stopRecording();
              await speechToText.stopTranscription();
            }}
            disabled={!audioCapture.isRecording}
          >
            Stop Recording
          </button>
        </div>

        {/* Live Transcript */}
        {currentChunk && (
          <div
            style={{
              margin: "10px 0",
              padding: "10px",
              backgroundColor: "#e3f2fd",
              borderRadius: "4px",
              fontStyle: "italic",
            }}
          >
            <strong>Live:</strong> {currentChunk}
          </div>
        )}

        {/* Final Transcript */}
        <div
          style={{
            margin: "10px 0",
            padding: "10px",
            border: "1px solid #ddd",
            borderRadius: "4px",
            minHeight: "150px",
            backgroundColor: "#f9f9f9",
          }}
        >
          <strong>Transcript:</strong>
          <br />
          {transcript ||
            "No transcript yet. Start recording to see text appear here."}
        </div>
      </section>

      {/* Text-to-Speech Section */}
      <section style={{ marginBottom: "20px" }}>
        <h3>Text-to-Speech</h3>
        <div style={{ marginBottom: "10px" }}>
          <span>TTS Status: </span>
          <span style={{ color: ttsIsConnected ? "green" : "red" }}>
            {ttsIsConnected ? "Connected" : "Disconnected"}
          </span>
          <button
            onClick={ttsIsConnected ? ttsDisconnect : ttsConnect}
            style={{ marginLeft: "10px" }}
            disabled={!canUseFeatures}
          >
            {ttsIsConnected ? "Disconnect" : "Connect"}
          </button>
        </div>

        <div style={{ marginBottom: "10px" }}>
          <textarea
            value={ttsText}
            onChange={(e) => setTtsText(e.target.value)}
            placeholder="Enter text to synthesize..."
            rows={3}
            style={{ width: "100%", padding: "8px" }}
          />
        </div>

        <div style={{ display: "flex", gap: "10px" }}>
          <button
            onClick={handleSynthesizeSpeech}
            disabled={
              !canUseFeatures ||
              !ttsIsConnected ||
              ttsLoading ||
              !ttsText.trim()
            }
          >
            {ttsLoading ? "Synthesizing..." : "Speak Text"}
          </button>

          <button
            onClick={handleDownloadAudio}
            disabled={!ttsAudioData || ttsAudioData.length === 0}
          >
            Download Audio
          </button>
        </div>

        {ttsAudioData && ttsAudioData.length > 0 && (
          <div
            style={{
              margin: "10px 0",
              padding: "10px",
              backgroundColor: "#e8f5e8",
              borderRadius: "4px",
            }}
          >
            Audio ready: {ttsAudioData.length} chunks,
            {Math.round(
              ttsAudioData.reduce((total, chunk) => total + chunk.length, 0) /
                1024
            )} KB
          </div>
        )}
      </section>

      {/* Error Display */}
      {(sessionManager.error ||
        speechToText.error ||
        audioCapture.error ||
        ttsError) && (
        <section
          style={{
            padding: "15px",
            backgroundColor: "#f8d7da",
            borderRadius: "4px",
            color: "#721c24",
          }}
        >
          <h4>Errors:</h4>
          {sessionManager.error && (
            <p>Session: {sessionManager.error.message}</p>
          )}
          {speechToText.error && <p>STT: {speechToText.error.message}</p>}
          {audioCapture.error && <p>Audio: {audioCapture.error.message}</p>}
          {ttsError && <p>TTS: {ttsError}</p>}
        </section>
      )}
    </div>
  );
}
```

## Step 4: Adding Medical Summarization

### Standalone SMR Usage

The `useSMR` hook can be used independently without sessions or other services. Here's a simple example for summarizing medical text:

```typescript
// src/components/StandaloneSMR.tsx
import React, { useState } from "react";
import { useSMR } from "@arcaai/agentic-sdk";

export function StandaloneSMR() {
  const [inputText, setInputText] = useState("");
  const [currentSummary, setCurrentSummary] = useState(null);
  const [summaryProgress, setSummaryProgress] = useState(0);

  // SMR hook - standalone usage
  const smr = useSMR({
    // SMR endpoint is apiEndpoint + '/api/smr'
    apiEndpoint: process.env.REACT_APP_API_ENDPOINT + "/api/smr" || "https://api.taphuynh.dev/api/smr",
    options: {
      credentials: {
        apiKey: process.env.REACT_APP_API_KEY || "your-api-key",
      },
    },
    onProgress: (progress) => {
      setSummaryProgress(progress);
    },
    onComplete: (summary) => {
      setCurrentSummary(summary);
      setSummaryProgress(100);
    },
    onError: (error) => {
      console.error('SMR Error:', error);
    },
  });

  const handleSummarize = async () => {
    if (!inputText.trim()) {
      alert('Please enter some medical text to summarize');
      return;
    }

    try {
      setSummaryProgress(0);
      const summary = await smr.summarizeSync({
        text: inputText,
        language: 'en',
        provider: 'azure', // or 'ollama'
        model: 'gpt-4', // specify your preferred model
      });

      console.log('Summarization completed:', summary);
      setSummaryProgress(100);
    } catch (error) {
      console.error('Summarization error:', error);
    }
  };

  const handleClear = () => {
    setInputText('');
    setCurrentSummary(null);
    setSummaryProgress(0);
  };

  // Helper function to extract summary data
  const extractSummaryData = (summaryResponse) => {
    if (!summaryResponse) return null;

    if (summaryResponse.summary && summaryResponse.session_id) {
      const actualSummary = summaryResponse.summary;

      // Enhanced medical summary format
      if (actualSummary.encounter_summary) {
        return {
          summary: actualSummary.clinical_summary?.summary || 'No summary available',
          diagnosis: actualSummary.clinical_assessment?.primary_diagnosis?.diagnosis || '',
          keyFindings: actualSummary.clinical_summary?.key_findings || [],
          medications: actualSummary.treatment_plan?.medications?.map((med) => med.name) || [],
          metadata: {
            provider: summaryResponse.llm_provider,
            model: summaryResponse.model_name,
            processingTime: summaryResponse.processing_time_ms,
          }
        };
      }

      // Simplified medical summary format
      if (actualSummary.summary) {
        return {
          summary: actualSummary.summary,
          diagnosis: actualSummary.assessment || '',
          keyFindings: actualSummary.symptoms || [],
          medications: [],
          metadata: {
            provider: summaryResponse.llm_provider,
            model: summaryResponse.model_name,
            processingTime: summaryResponse.processing_time_ms,
          }
        };
      }
    }

    return summaryResponse;
  };

  const renderSummary = () => {
    const summaryData = extractSummaryData(currentSummary);
    if (!summaryData) return null;

    return (
      <div style={{ marginTop: "15px", padding: "15px", border: "1px solid #ddd", borderRadius: "8px", backgroundColor: "#f9f9f9" }}>
        <h4 style={{ marginTop: 0, color: "#333" }}>Medical Summary</h4>

        {summaryData.summary && (
          <div style={{ marginBottom: "12px" }}>
            <strong>Summary:</strong>
            <p style={{ margin: "8px 0", lineHeight: "1.5", fontStyle: "italic" }}>{summaryData.summary}</p>
          </div>
        )}

        {summaryData.diagnosis && (
          <div style={{ marginBottom: "12px" }}>
            <strong>Diagnosis:</strong>
            <span style={{ marginLeft: "8px" }}>{summaryData.diagnosis}</span>
          </div>
        )}

        {summaryData.keyFindings && summaryData.keyFindings.length > 0 && (
          <div style={{ marginBottom: "12px" }}>
            <strong>Key Findings:</strong>
            <ul style={{ margin: "8px 0", paddingLeft: "20px" }}>
              {summaryData.keyFindings.map((finding, index) => (
                <li key={index} style={{ marginBottom: "4px" }}>{finding}</li>
              ))}
            </ul>
          </div>
        )}

        {summaryData.medications && summaryData.medications.length > 0 && (
          <div style={{ marginBottom: "12px" }}>
            <strong>Medications:</strong>
            <ul style={{ margin: "8px 0", paddingLeft: "20px" }}>
              {summaryData.medications.map((med, index) => (
                <li key={index} style={{ marginBottom: "4px" }}>{med}</li>
              ))}
            </ul>
          </div>
        )}

        {summaryData.metadata && (
          <div style={{ fontSize: "0.85em", color: "#666", marginTop: "12px", paddingTop: "8px", borderTop: "1px solid #eee" }}>
            Generated by: {summaryData.metadata.provider} ({summaryData.metadata.model})
            {summaryData.metadata.processingTime && ` • Processing time: ${summaryData.metadata.processingTime}ms`}
          </div>
        )}
      </div>
    );
  };

  return (
    <div style={{ padding: "20px", maxWidth: "600px", margin: "0 auto" }}>
      <h1>Medical Text Summarization</h1>

      {/* Input Section */}
      <section style={{ marginBottom: "20px" }}>
        <h3>Input Medical Text</h3>
        <textarea
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          placeholder="Enter medical consultation notes, patient history, or any medical text to summarize..."
          style={{
            width: "100%",
            minHeight: "150px",
            padding: "12px",
            border: "1px solid #ccc",
            borderRadius: "4px",
            fontSize: "14px",
            fontFamily: "monospace",
            resize: "vertical"
          }}
        />
      </section>

      {/* Controls */}
      <section style={{ marginBottom: "20px" }}>
        <div style={{ marginBottom: "10px" }}>
          <span>SMR Status: </span>
          <span style={{ color: smr.isConnected ? "green" : "red", fontWeight: "bold" }}>
            {smr.isConnected ? "Connected" : "Disconnected"}
          </span>
          {smr.loading && <span style={{ marginLeft: "10px", color: "orange" }}>Processing...</span>}
        </div>

        <button
          onClick={handleSummarize}
          disabled={smr.loading || !inputText.trim()}
          style={{
            marginRight: "10px",
            padding: "10px 20px",
            backgroundColor: smr.loading ? "#ccc" : "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: smr.loading ? "not-allowed" : "pointer"
          }}
        >
          {smr.loading ? "Generating Summary..." : "Generate Summary"}
        </button>

        <button
          onClick={handleClear}
          disabled={smr.loading}
          style={{
            padding: "10px 20px",
            backgroundColor: "#6c757d",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: smr.loading ? "not-allowed" : "pointer"
          }}
        >
          Clear
        </button>

        {summaryProgress > 0 && summaryProgress < 100 && (
          <div style={{ marginTop: "15px" }}>
            <div style={{ backgroundColor: "#f0f0f0", height: "8px", borderRadius: "4px", overflow: "hidden" }}>
              <div
                style={{
                  backgroundColor: "#28a745",
                  height: "100%",
                  width: `${summaryProgress}%`,
                  transition: "width 0.3s ease"
                }}
              />
            </div>
            <span style={{ fontSize: "0.9em", color: "#666" }}>Progress: {summaryProgress}%</span>
          </div>
        )}
      </section>

      {/* Summary Display */}
      {renderSummary()}

      {/* Error Display */}
      {smr.error && (
        <section style={{ marginTop: "20px" }}>
          <div style={{ padding: "10px", backgroundColor: "#f8d7da", border: "1px solid #f5c6cb", borderRadius: "4px", color: "#721c24" }}>
            <strong>Error:</strong> {smr.error}
          </div>
        </section>
      )}
    </div>
  );
}
```

### Integrated SMR with Session Management

For more complex applications that need session management and real-time transcription, you can integrate SMR with other services. This allows you to generate structured medical summaries from consultation transcripts:

```typescript
// src/components/ConsultationWithSMR.tsx
import React, { useState } from "react";
import {
  useArcaSessionManager,
  useSTT,
  useSMR,
} from "@arcaai/agentic-sdk";

export function ConsultationWithSMR() {
  const [doctorName] = useState("Dr. Smith");
  const [patientName] = useState("John Doe");
  const [transcript, setTranscript] = useState("");
  const [currentSummary, setCurrentSummary] = useState(null);
  const [summaryProgress, setSummaryProgress] = useState(0);

  // Session Management
  const sessionManager = useArcaSessionManager({
    doctorId: "doctor-123",
    doctorName,
    patientId: "patient-456",
    patientName,
    options: {
      apiEndpoint: process.env.REACT_APP_API_ENDPOINT || "https://api.taphuynh.dev",
      credentials: {
        apiKey: process.env.REACT_APP_API_KEY || "your-api-key",
      },
    },
  });

  // Medical Summarization
  const smr = useSMR({
    // SMR endpoint is apiEndpoint + '/api/smr'
    apiEndpoint: process.env.REACT_APP_API_ENDPOINT + "/api/smr" || "https://api.taphuynh.dev/api/smr",
    sessionId: sessionManager.session?.status === 'ACTIVE' ? sessionManager.session.id : undefined,
    options: {
      credentials: {
        apiKey: process.env.REACT_APP_API_KEY || "your-api-key",
      },
    },
    onProgress: (progress) => {
      setSummaryProgress(progress);
    },
    onComplete: (summary) => {
      setCurrentSummary(summary);
      setSummaryProgress(100);
    },
    onError: (error) => {
      console.error('SMR Error:', error);
    },
  });

  // Speech-to-Text (simplified for this example)
  const { startRecording, stopRecording, isRecording } = useSTT({
    sessionId: sessionManager.session?.id,
    onTranscript: (text, isFinal) => {
      if (isFinal) {
        setTranscript((prev) => prev + " " + text);
      }
    },
  });

  const handleStartConsultation = async () => {
    try {
      await sessionManager.createSession();
      await sessionManager.startSession();
    } catch (error) {
      console.error("Failed to start consultation:", error);
    }
  };

  const handleGenerateSummary = async () => {
    if (!sessionManager.session || sessionManager.session.status !== 'ACTIVE') {
      alert('Session must be active to use Medical Summarization');
      return;
    }

    if (isRecording) {
      alert('Please stop recording before generating summary');
      return;
    }

    if (!transcript.trim()) {
      alert('No transcript available to summarize');
      return;
    }

    try {
      setSummaryProgress(0);
      const summary = await smr.summarizeSync({
        text: transcript,
        sessionId: sessionManager.session.id,
        language: 'en', // Extract from language setting if needed
      });

      console.log('Summarization completed:', summary);
      setSummaryProgress(100);
    } catch (error) {
      console.error('Summarization error:', error);
    }
  };

  // Helper function to extract summary data from enhanced SMR response
  const extractSummaryData = (summaryResponse) => {
    if (!summaryResponse) return null;

    // Check if this is the new SummaryResponse format
    if (summaryResponse.summary && summaryResponse.session_id) {
      const actualSummary = summaryResponse.summary;

      // Handle EnhancedMedicalSummary format
      if (actualSummary.encounter_summary) {
        return {
          summary: actualSummary.clinical_summary?.summary || actualSummary.encounter_summary?.chief_complaint || 'No summary available',
          keyPoints: actualSummary.clinical_summary?.key_findings || [],
          diagnosis: actualSummary.clinical_assessment?.primary_diagnosis?.diagnosis || '',
          recommendations: actualSummary.treatment_plan?.procedures || [],
          medications: actualSummary.treatment_plan?.medications?.map((med) => med.name) || [],
          metadata: {
            provider: summaryResponse.llm_provider,
            model: summaryResponse.model_name,
            processingTime: summaryResponse.processing_time_ms,
            confidence: summaryResponse.confidence_score
          }
        };
      }

      // Handle SimplifiedMedicalSummary format
      if (actualSummary.chief_complaint) {
        return {
          summary: actualSummary.summary,
          keyPoints: actualSummary.symptoms || [],
          diagnosis: actualSummary.assessment,
          recommendations: actualSummary.treatment_plan ? [actualSummary.treatment_plan] : [],
          medications: [],
          metadata: {
            provider: summaryResponse.llm_provider,
            model: summaryResponse.model_name,
            processingTime: summaryResponse.processing_time_ms,
            confidence: summaryResponse.confidence_score
          }
        };
      }
    }

    // Fallback to old format (backward compatibility)
    return summaryResponse;
  };

  const renderSummary = () => {
    const summaryData = extractSummaryData(currentSummary);
    if (!summaryData) return null;

    return (
      <div style={{ marginTop: "10px", padding: "10px", border: "1px solid #ddd", borderRadius: "4px" }}>
        <h4>Medical Summary</h4>

        {summaryData.summary && (
          <div style={{ marginBottom: "10px" }}>
            <strong>Summary:</strong>
            <p style={{ margin: "5px 0", fontStyle: "italic" }}>{summaryData.summary}</p>
          </div>
        )}

        {summaryData.diagnosis && (
          <div style={{ marginBottom: "10px" }}>
            <strong>Diagnosis:</strong> {summaryData.diagnosis}
          </div>
        )}

        {summaryData.keyPoints && summaryData.keyPoints.length > 0 && (
          <div style={{ marginBottom: "10px" }}>
            <strong>Key Findings:</strong>
            <ul style={{ margin: "5px 0", paddingLeft: "20px" }}>
              {summaryData.keyPoints.map((point, index) => (
                <li key={index}>{point}</li>
              ))}
            </ul>
          </div>
        )}

        {summaryData.medications && summaryData.medications.length > 0 && (
          <div style={{ marginBottom: "10px" }}>
            <strong>Medications:</strong>
            <ul style={{ margin: "5px 0", paddingLeft: "20px" }}>
              {summaryData.medications.map((med, index) => (
                <li key={index}>{med}</li>
              ))}
            </ul>
          </div>
        )}

        {summaryData.metadata && (
          <div style={{ fontSize: "0.8em", color: "#666", marginTop: "10px" }}>
            Generated by: {summaryData.metadata.provider} ({summaryData.metadata.model})
            {summaryData.metadata.processingTime && ` in ${summaryData.metadata.processingTime}ms`}
          </div>
        )}
      </div>
    );
  };

  return (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <h1>Medical Consultation with Summarization</h1>

      {/* Session Management */}
      <section style={{ marginBottom: "20px" }}>
        <h3>Session Management</h3>
        <div style={{ marginBottom: "10px" }}>
          <span>Session Status: </span>
          <span style={{ color: sessionManager.session?.status === 'ACTIVE' ? "green" : "red" }}>
            {sessionManager.session?.status || "No Session"}
          </span>
        </div>

        <button
          onClick={handleStartConsultation}
          disabled={sessionManager.isLoading || sessionManager.session?.status === 'ACTIVE'}
          style={{ marginRight: "10px" }}
        >
          {sessionManager.isLoading ? "Starting..." : "Start Consultation"}
        </button>

        <button
          onClick={() => sessionManager.endSession()}
          disabled={!sessionManager.session || sessionManager.session.status !== 'ACTIVE'}
        >
          End Session
        </button>
      </section>

      {/* Recording Controls */}
      <section style={{ marginBottom: "20px" }}>
        <h3>Recording</h3>
        <button
          onClick={isRecording ? stopRecording : startRecording}
          disabled={!sessionManager.session || sessionManager.session.status !== 'ACTIVE'}
          style={{
            backgroundColor: isRecording ? "#ff4444" : "#44ff44",
            color: "white",
            marginRight: "10px"
          }}
        >
          {isRecording ? "Stop Recording" : "Start Recording"}
        </button>
      </section>

      {/* Transcript Display */}
      <section style={{ marginBottom: "20px" }}>
        <h3>Transcript</h3>
        <div style={{
          border: "1px solid #ccc",
          padding: "10px",
          minHeight: "100px",
          backgroundColor: "#f9f9f9",
          whiteSpace: "pre-wrap"
        }}>
          {transcript || "No transcript available..."}
        </div>
      </section>

      {/* Summarization Controls */}
      <section style={{ marginBottom: "20px" }}>
        <h3>Medical Summarization</h3>
        <div style={{ marginBottom: "10px" }}>
          <span>SMR Status: </span>
          <span style={{ color: smr.isConnected ? "green" : "red" }}>
            {smr.isConnected ? "Connected" : "Disconnected"}
          </span>
          {smr.loading && <span style={{ marginLeft: "10px" }}>Processing...</span>}
        </div>

        <button
          onClick={handleGenerateSummary}
          disabled={smr.loading || !transcript.trim() || !sessionManager.session || sessionManager.session.status !== 'ACTIVE'}
          style={{ marginRight: "10px" }}
        >
          {smr.loading ? "Generating Summary..." : "Generate Summary"}
        </button>

        {summaryProgress > 0 && summaryProgress < 100 && (
          <div style={{ marginTop: "10px" }}>
            <div style={{ backgroundColor: "#f0f0f0", height: "10px", borderRadius: "5px" }}>
              <div
                style={{
                  backgroundColor: "#4CAF50",
                  height: "100%",
                  width: `${summaryProgress}%`,
                  borderRadius: "5px"
                }}
              />
            </div>
            <span style={{ fontSize: "0.9em" }}>Progress: {summaryProgress}%</span>
          </div>
        )}

        {renderSummary()}
      </section>

      {/* Error Display */}
      {(sessionManager.error || smr.error) && (
        <section style={{ marginBottom: "20px" }}>
          <h3 style={{ color: "red" }}>Errors</h3>
          {sessionManager.error && <p style={{ color: "red" }}>Session: {sessionManager.error}</p>}
          {smr.error && <p style={{ color: "red" }}>SMR: {smr.error}</p>}
        </section>
      )}
    </div>
  );
}
```

## Step 5: Main App Integration

Finally, let's create the main app file to tie everything together:

```typescript
// src/App.tsx
import React from "react";
import { FullConsultationApp } from "./components/FullConsultationApp";

function App() {
  return (
    <div className="App">
      <FullConsultationApp />
    </div>
  );
}

export default App;
```

## Key Concepts Demonstrated

### 1. Session Lifecycle

- Creating and starting sessions
- Managing session state
- Proper cleanup on session end

### 2. Audio Processing

- Requesting microphone permissions
- Real-time audio capture
- Audio streaming to STT service

### 3. Speech-to-Text Integration

- WebSocket connection management
- Real-time transcript processing
- Handling both interim and final results

### 4. Text-to-Speech Integration

- TTS service connection
- Voice synthesis with options
- Audio data handling and download

### 5. Error Handling

- Graceful error display
- Service-specific error handling
- User-friendly error messages

## Best Practices Implemented

1. **State Management**: Proper React state management for all SDK hooks
2. **Error Boundaries**: Comprehensive error handling for all services
3. **User Experience**: Clear status indicators and disabled states
4. **Resource Cleanup**: Proper cleanup of connections and sessions
5. **Performance**: Efficient audio processing and memory management

## Next Steps

Now that you have a working consultation app, you can:

1. **Customize the UI**: Style components to match your design system
2. **Add Features**: Implement additional functionality like session notes
3. **Integrate Backend**: Connect to your medical records system
4. **Add Security**: Implement proper authentication and encryption
5. **Deploy**: Follow the [deployment guide](./advanced/deployment.md) for production

## Common Issues and Solutions

### Permission Errors

```typescript
// Check microphone permissions
useEffect(() => {
  navigator.mediaDevices
    .getUserMedia({ audio: true })
    .then(() => console.log("Microphone access granted"))
    .catch(() => console.error("Microphone access denied"));
}, []);
```

### Connection Issues

```typescript
// Add connection retry logic
const retryConnection = useCallback(async () => {
  if (!sessionManager.session) {
    await sessionManager.createSession();
  }
}, [sessionManager]);
```

### Audio Quality Issues

```typescript
// Optimize audio settings for medical use
const audioConfig = {
  sampleRate: 16000,
  channels: 1,
  noiseCancellation: true,
  echoCancellation: true,
  autoGainControl: true,
};
```

This basic usage guide provides a solid foundation for building medical consultation applications with the Agentic Audio SDK. For more advanced features and customization options, refer to the individual hook documentation and advanced guides.
