import type { LogConfig } from '../types/index';

export class Logger {
    private config: LogConfig | null = null;
    private logBuffer: LogEntry[] = [];
    private readonly maxBufferSize = 100;

    public async initialize(config: LogConfig): Promise<void> {
        this.config = config;
    }

    public debug(message: string, data?: Record<string, unknown>): void {
        this.log('debug', message, data);
    }

    public info(message: string, data?: Record<string, unknown>): void {
        this.log('info', message, data);
    }

    public warn(message: string, data?: Record<string, unknown>): void {
        this.log('warn', message, data);
    }

    public error(message: string, data?: Record<string, unknown>): void {
        this.log('error', message, data);
    }

    private log(level: LogConfig['level'], message: string, data?: Record<string, unknown>): void {
        if (!this.config || !this.shouldLog(level)) {
            return;
        }

        const entry: LogEntry = {
            timestamp: new Date().toISOString(),
            level,
            message,
            ...(data && { data }),
        };

        // Console logging
        if (this.config.console) {
            this.logToConsole(entry);
        }

        // Buffer for remote logging
        if (this.config.remote) {
            this.addToBuffer(entry);
        }
    }

    private shouldLog(level: LogConfig['level']): boolean {
        if (!this.config) return false;

        const levels = ['debug', 'info', 'warn', 'error'];
        const configLevelIndex = levels.indexOf(this.config.level);
        const messageLevelIndex = levels.indexOf(level);

        return messageLevelIndex >= configLevelIndex;
    }

    private logToConsole(entry: LogEntry): void {
        const prefix = `[${entry.timestamp}] [${entry.level.toUpperCase()}]`;
        const message = `${prefix} ${entry.message}`;

        switch (entry.level) {
            case 'debug':
                console.debug(message, entry.data);
                break;
            case 'info':
                console.info(message, entry.data);
                break;
            case 'warn':
                console.warn(message, entry.data);
                break;
            case 'error':
                console.error(message, entry.data);
                break;
        }
    }

    private addToBuffer(entry: LogEntry): void {
        this.logBuffer.push(entry);

        // Maintain buffer size
        if (this.logBuffer.length > this.maxBufferSize) {
            this.logBuffer.shift();
        }

        if (this.config?.endpoint) {
        }
    }
}

interface LogEntry {
    timestamp: string;
    level: LogConfig['level'];
    message: string;
    data?: Record<string, unknown>;
}
