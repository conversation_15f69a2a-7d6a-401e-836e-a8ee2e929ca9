# useArcaSpeechToText

The `useArcaSpeechToText` hook provides real-time speech-to-text transcription capabilities with support for multiple languages and high-accuracy medical terminology recognition.

## Overview

This hook establishes a WebSocket connection to the speech-to-text service and provides real-time transcription of audio data. It integrates seamlessly with the session management system and audio capture components.

## Import

```typescript
import { useArcaSpeechToText } from "@arcaai/agentic-sdk";
```

## Configuration Setup

Before using the hook, ensure you have your SDK configuration ready:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: "your-api-key-here",
  },
  // Optional: Audio settings
  audioSettings: {
    sampleRate: 16000,
    channels: 1,
    noiseCancellation: true,
    echoCancellation: true,
    autoGainControl: true,
  },
};
```

## Basic Usage

```typescript
import React, { useState } from "react";
import { useArcaSpeechToText, useAudioCapture } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

function SpeechToTextDemo() {
  const [transcript, setTranscript] = useState("");
  const [currentChunk, setCurrentChunk] = useState("");

  const {
    error: sttError,
    startTranscription,
    stopTranscription,
    sendAudioData,
  } = useArcaSpeechToText({
    sessionId: "session-123",
    language: "en-US",
    options: SDK_CONFIG_OPTIONS,
    onTranscript: (text, isFinal) => {
      if (isFinal) {
        setTranscript((prev) => prev + " " + text);
        setCurrentChunk("");
      } else {
        setCurrentChunk(text);
      }
    },
    onError: (error) => {
      console.error("STT Error:", error);
    },
  });

  const { isRecording, startRecording, stopRecording, isReady } =
    useAudioCapture({
      onAudioData: sendAudioData,
      onError: (error) => {
        console.error("Audio Error:", error);
      },
    });

  const handleStart = async () => {
    try {
      await startTranscription();
      await startRecording();
    } catch (error) {
      console.error("Failed to start:", error);
    }
  };

  const handleStop = async () => {
    try {
      await stopRecording();
      await stopTranscription();
    } catch (error) {
      console.error("Failed to stop:", error);
    }
  };

  return (
    <div>
      <h2>Speech-to-Text Demo</h2>

      <div>
        <button onClick={handleStart} disabled={!isReady || isRecording}>
          Start Recording
        </button>
        <button onClick={handleStop} disabled={!isRecording}>
          Stop Recording
        </button>
      </div>

      {currentChunk && (
        <div style={{ fontStyle: "italic", color: "#666" }}>
          Live: {currentChunk}
        </div>
      )}

      <div>
        <h3>Final Transcript:</h3>
        <p>{transcript}</p>
      </div>

      {sttError && (
        <div style={{ color: "red" }}>Error: {sttError.message}</div>
      )}
    </div>
  );
}
```

## API Reference

### Parameters

The hook accepts a configuration object with the following properties:

#### `UseArcaSpeechToTextProps`

| Property       | Type                                       | Required | Description                                              |
| -------------- | ------------------------------------------ | -------- | -------------------------------------------------------- |
| `sessionId`    | `string`                                   | ✅       | Unique session identifier                                 |
| `language`     | `string`                                   | ✅       | Language code (e.g., 'en-US', 'hi-IN', 'ta-IN', 'ml-IN') |
| `options`      | `Partial<SDKConfig>`                        | ❌       | SDK configuration options (use `SDK_CONFIG_OPTIONS`)      |
| `onTranscript` | `(text: string, isFinal: boolean) => void` | ❌       | Transcript callback function                             |
| `onError`      | `(error: ErrorInfo) => void`               | ❌       | Error callback function                                  |

### Return Value

The hook returns an object with the following properties:

#### `UseArcaSpeechToTextReturn`

| Property             | Type                          | Description                        |
| -------------------- | ----------------------------- | ---------------------------------- |
| `error`              | `ErrorInfo \| null`           | Current error state                |
| `startTranscription` | `() => Promise<void>`         | Starts the STT session             |
| `stopTranscription`  | `() => Promise<void>`         | Stops the STT session              |
| `sendAudioData`      | `(data: ArrayBuffer) => void` | Sends audio data for transcription |
| `transcript`         | `string`                      | Current accumulated transcript     |

## Supported Languages

The SDK supports multiple languages with medical terminology optimization:

| Language Code | Language          | Medical Terminology Support   |
| ------------- | ----------------- | ----------------------------- |
| `en-US`       | English (US)      | ✅ Full medical dictionary    |
| `en-GB`       | English (UK)      | ✅ Full medical dictionary    |
| `hi-IN`       | Hindi (India)     | ✅ Medical terms in Hindi     |
| `ta-IN`       | Tamil (India)     | ✅ Medical terms in Tamil     |
| `ml-IN`       | Malayalam (India) | ✅ Medical terms in Malayalam |
| `es-ES`       | Spanish (Spain)   | ✅ Medical terms in Spanish   |
| `fr-FR`       | French (France)   | ✅ Medical terms in French    |

## Advanced Usage

### Custom Audio Settings

```typescript
import { useArcaSpeechToText } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const speechToText = useArcaSpeechToText({
  sessionId: "session-123",
  language: "en-US",
  options: {
    ...SDK_CONFIG_OPTIONS,
    audioSettings: {
      ...SDK_CONFIG_OPTIONS.audioSettings,
      sampleRate: 48000, // Override specific settings
      channels: 2,
      bitDepth: 24,
      chunkSize: 2048,
    },
  },
  onTranscript: (text, isFinal) => {
    console.log(`${isFinal ? "Final" : "Interim"}: ${text}`);
  },
});
```

### Error Handling

```typescript
import { useArcaSpeechToText } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const speechToText = useArcaSpeechToText({
  sessionId: "session-123",
  language: "en-US",
  options: SDK_CONFIG_OPTIONS,
  onError: (error) => {
    switch (error.code) {
      case "WEBSOCKET_CONNECTION_FAILED":
        console.error("Failed to connect to STT service");
        // Implement retry logic
        break;
      case "SESSION_START_FAILED":
        console.error("Failed to start STT session");
        // Handle session creation issues
        break;
      case "AUDIO_PROCESSING_ERROR":
        console.error("Audio processing error");
        // Handle audio format issues
        break;
      case "AUTHENTICATION_FAILED":
        console.error("Authentication failed");
        // Handle API key issues
        break;
      default:
        console.error("Unknown STT error:", error);
    }
  },
});
```

### Integration with Session Manager

```typescript
import {
  useArcaSessionManager,
  useArcaSpeechToText,
} from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

function MedicalConsultation() {
  const sessionManager = useArcaSessionManager({
    doctorId: "dr-123",
    doctorName: "Dr. Smith",
    patientId: "pt-456",
    patientName: "John Doe",
    options: SDK_CONFIG_OPTIONS,
  });

  const speechToText = useArcaSpeechToText({
    sessionId: sessionManager.session?.id || "",
    language: "en-US",
    options: SDK_CONFIG_OPTIONS,
    onTranscript: (text, isFinal) => {
      if (isFinal && sessionManager.session?.status === "ACTIVE") {
        // Transcript automatically saved to session
        console.log("Transcript saved to session:", text);
      }
    },
    onError: (error) => {
      console.error("STT Error:", error);
    },
  });

  // Only allow STT when session is active
  const canUseStt = sessionManager.session?.status === "ACTIVE";

  return (
    <div>
      {!canUseStt && <div>Please start a session to use speech-to-text</div>}
      {canUseStt && <div>{/* STT controls */}</div>}
    </div>
  );
}
```

### Real-time Transcript Processing

```typescript
import { useArcaSpeechToText } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const [transcriptHistory, setTranscriptHistory] = useState<string[]>([]);
const [confidence, setConfidence] = useState<number>(0);

const speechToText = useArcaSpeechToText({
  sessionId: "session-123",
  language: "en-US",
  options: SDK_CONFIG_OPTIONS,
  onTranscript: (text, isFinal, metadata) => {
    if (isFinal) {
      // Add to transcript history
      setTranscriptHistory((prev) => [...prev, text]);

      // Extract medical entities (if available)
      if (metadata?.entities) {
        console.log("Medical entities found:", metadata.entities);
      }

      // Track confidence scores
      if (metadata?.confidence) {
        setConfidence(metadata.confidence);
      }
    }
  },
});
```

## Transcript Metadata

The `onTranscript` callback can receive additional metadata:

```typescript
interface TranscriptMetadata {
  confidence: number; // 0-1 confidence score
  startTime: number; // Start time in milliseconds
  endTime: number; // End time in milliseconds
  language: string; // Detected language
  alternatives?: Array<{
    // Alternative transcriptions
    text: string;
    confidence: number;
  }>;
  entities?: MedicalEntity[]; // Extracted medical entities
}
```

## Medical Entity Recognition

The STT service can identify and extract medical entities:

```typescript
interface MedicalEntity {
  text: string; // Entity text
  type: string; // Entity type (symptom, medication, etc.)
  confidence: number; // Confidence score
  start: number; // Start position in text
  end: number; // End position in text
  codes?: MedicalCode[]; // Medical codes (ICD-10, SNOMED, etc.)
}
```

## Performance Optimization

### Chunking Strategy

```typescript
import { useArcaSpeechToText } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

// Optimize for real-time processing
const speechToText = useArcaSpeechToText({
  sessionId: "session-123",
  language: "en-US",
  options: {
    ...SDK_CONFIG_OPTIONS,
    audioSettings: {
      chunkSize: 512, // Smaller chunks for lower latency
      sampleRate: 16000, // Optimal for speech recognition
      format: "pcm", // Best quality format
    },
  },
});
```

### Connection Management

```typescript
import { useArcaSpeechToText } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const [isConnected, setIsConnected] = useState(false);

const speechToText = useArcaSpeechToText({
  sessionId: "session-123",
  language: "en-US",
  options: SDK_CONFIG_OPTIONS,
  onConnect: () => {
    setIsConnected(true);
    console.log("STT service connected");
  },
  onDisconnect: () => {
    setIsConnected(false);
    console.log("STT service disconnected");
  },
  onReconnect: (attempt) => {
    console.log(`Reconnection attempt ${attempt}`);
  },
});
```

## Error Codes

Common error codes and their meanings:

| Error Code                    | Description                      | Action                            |
| ----------------------------- | -------------------------------- | --------------------------------- |
| `WEBSOCKET_CONNECTION_FAILED` | Failed to connect to WebSocket   | Check network and endpoint        |
| `SESSION_START_FAILED`        | Failed to start STT session      | Verify session ID and credentials |
| `AUDIO_PROCESSING_ERROR`      | Audio format or processing error | Check audio settings              |
| `AUTHENTICATION_FAILED`       | API key invalid or expired       | Update credentials                |
| `LANGUAGE_NOT_SUPPORTED`      | Unsupported language code        | Use supported language            |
| `RATE_LIMIT_EXCEEDED`         | Too many requests                | Implement rate limiting           |
| `SESSION_EXPIRED`             | Session has expired              | Create new session                |

## Best Practices

### 1. Audio Quality

```typescript
// Use optimal audio settings for medical transcription from configuration
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const audioConfig = {
  sampleRate: SDK_CONFIG_OPTIONS.audioSettings?.sampleRate || 16000,
  channels: SDK_CONFIG_OPTIONS.audioSettings?.channels || 1,
  noiseCancellation:
    SDK_CONFIG_OPTIONS.audioSettings?.noiseCancellation ?? true,
  echoCancellation: SDK_CONFIG_OPTIONS.audioSettings?.echoCancellation ?? true,
  autoGainControl: SDK_CONFIG_OPTIONS.audioSettings?.autoGainControl ?? true,
};
```

### 2. Error Recovery

```typescript
import { useArcaSpeechToText } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

const handleSttError = useCallback(
  (error: ErrorInfo) => {
    if (error.code === "WEBSOCKET_CONNECTION_FAILED") {
      // Implement exponential backoff retry
      setTimeout(() => {
        speechToText.startTranscription();
      }, Math.min(1000 * Math.pow(2, retryCount), 30000));
    }
  },
  [speechToText, retryCount]
);
```

### 3. Transcript Validation

```typescript
const validateTranscript = (text: string): boolean => {
  // Minimum length validation
  if (text.length < 3) return false;

  // Check for meaningful content
  const meaningfulWords = text
    .split(" ")
    .filter(
      (word) =>
        word.length > 2 && !["um", "uh", "ah"].includes(word.toLowerCase())
    );

  return meaningfulWords.length > 0;
};
```

### 4. Session Integration

```typescript
import { useArcaSpeechToText } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

// Automatically save transcripts to session
const speechToText = useArcaSpeechToText({
  sessionId: sessionManager.session?.id || "",
  language: "en-US",
  options: SDK_CONFIG_OPTIONS,
  onTranscript: async (text, isFinal) => {
    if (isFinal && validateTranscript(text)) {
      await sessionManager.updateSession({
        transcript: text,
        lastActivity: new Date().toISOString(),
      });
    }
  },
});
```

## Troubleshooting

### Common Issues

1. **No Transcription Output**

   - Check microphone permissions
   - Verify audio data is being sent
   - Confirm language code is correct
   - Verify API endpoint in `SDK_CONFIG_OPTIONS`

2. **Poor Transcription Quality**

   - Ensure quiet environment
   - Check microphone quality
   - Verify audio settings in configuration

3. **Connection Issues**

   - Check network connectivity
   - Verify API endpoint and credentials in `SDK_CONFIG_OPTIONS`
   - Review firewall settings

4. **Session Not Starting**
   - Verify session ID is valid
   - Check session status is ACTIVE
   - Confirm API key permissions in configuration

### Debug Mode

Enable debug logging in your configuration:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: "your-api-key-here",
  },
  logging: {
    level: "debug",
    console: true,
  },
};
```

## Examples

- **[Complete Examples](./examples/README.md)** - Working code examples and demos