import { useState, useEffect, useCallback, useRef } from 'react';
import { SessionManager } from '../core/SessionManager';
import type { MedicalSession, PatientInfo, ProviderInfo, ErrorInfo, SessionMetadata, SessionManagerConfig, SDKConfig } from '../types/index';

const DEFAULT_SDK_CONFIG: SDKConfig = {
    apiEndpoint: 'https://api.agentic.ai',
    websocketUrl: 'wss://api.agentic.ai',
    sttProvider: 'azure',
    audioSettings: {
        sampleRate: 16000,
        format: 'pcm',
        channels: 1,
        bitDepth: 16,
        chunkSize: 1024,
        noiseCancellation: true,
        echoCancellation: true,
        autoGainControl: true,
    },
    errorReporting: {
        enabled: true,
        includeStackTrace: false,
        maxErrorsPerSession: 10,
    },
    logging: {
        level: 'info',
        console: true,
        remote: false,
    },
    credentials: {
        apiKey: 'AFUTlhD/pGyyKOBTP3KTnA==',
    },
    environment: 'development',
};

/**
 * Options for useSessionManager hook
 */
export interface IArcaSessionOptions {
    sessionId?: string; // Optional session ID for recovery
    doctorId: string; // Doctor/Provider ID
    doctorName: string; // Doctor/Provider name
    patientId: string; // Patient ID
    patientName: string; // Patient name
    options?: Partial<SDKConfig>; // SessionManager configuration
    onError?: (error: ErrorInfo) => void; // Optional error handler
}

/**
 * Return type for useSessionManager hook
 */
export interface IArcaSessionManagerReturn {
    session: MedicalSession | null;
    isLoading: boolean;
    error: ErrorInfo | null;
    createSession: (metadata?: Partial<SessionMetadata>) => Promise<MedicalSession>;
    updateSession: (jsonData?: Record<string, unknown>) => Promise<void>;
    loadSession: (sessionId: string) => Promise<MedicalSession>;
    startSession: () => Promise<void>;
    pauseSession: (reason?: string) => Promise<void>;
    resumeSession: () => Promise<void>;
    endSession: () => Promise<void>;
    clearError: () => void;
}

export function useArcaSessionManager(props: IArcaSessionOptions): IArcaSessionManagerReturn {
    const [session, setSession] = useState<MedicalSession | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<ErrorInfo | null>(null);

    const sessionManagerRef = useRef<SessionManager | null>(null);
    const configRef = useRef<SDKConfig | null>(null);
    const { doctorId, doctorName, patientId, patientName, options, onError } = props;

    useEffect(() => {
        configRef.current = {
            ...DEFAULT_SDK_CONFIG,
            ...options,
        };
    }, [options]);

    const initializeSessionManager = useCallback(async () => {
        if (!configRef.current) return;
        try {
            const sessionManagerConfig: SessionManagerConfig = {
                apiEndpoint: configRef.current.apiEndpoint,
                credentials: {
                    apiKey: configRef.current.credentials?.apiKey || '',
                },
                encryptionKey: 'default-************************************', // 32 chars
                maxStorageSize: 100 * 1024 * 1024, // 100MB
                compressionEnabled: true,
                backupInterval: 30000, // 30 seconds
                retentionPeriod: 24 * 60 * 60 * 1000, // 24 hours
                maxSessions: 50,
                autoCleanup: true,
            };

            const sessionManager = new SessionManager();
            await sessionManager.initialize(sessionManagerConfig);
            sessionManagerRef.current = sessionManager;

            console.log('🎯 SessionManager initialized');
            setIsLoading(false);
        } catch (error) {
            console.error('Failed to initialize SessionManager:', error);
            setError({
                code: 'SESSION_MANAGER_INIT_FAILED',
                message: error instanceof Error ? error.message : 'Failed to initialize SessionManager',
                severity: 'high',
                category: 'configuration',
            });
            setIsLoading(false);
        }
    }, []);

    const setupEventListeners = useCallback(() => {
        const sessionManager = sessionManagerRef.current;
        if (!sessionManager) return;

        console.log('🎯 Setting up event listeners...');

        sessionManager.removeAllListeners();

        sessionManager.on('session:created', ({ session: createdSession }) => {
            console.log('🔥 Session created event received:', createdSession.id, 'status:', createdSession.status);
            setSession(createdSession);
        });

        sessionManager.on('session:loaded', ({ session: loadedSession }) => {
            console.log('🔥 Session loaded event received:', loadedSession.id, 'status:', loadedSession.status);
            setSession(loadedSession);
        });

        sessionManager.on('session:started', ({ sessionId }) => {
            console.log('🔥 Session started event received:', sessionId);
            const currentSession = sessionManager.getCurrentSession();
            console.log('🔥 Current session after start:', currentSession?.status);
            if (currentSession) {
                setSession({ ...currentSession });
            }
        });

        sessionManager.on('session:paused', ({ sessionId, reason }) => {
            console.log('🔥 Session paused event received:', sessionId, reason);
            const currentSession = sessionManager.getCurrentSession();
            console.log('🔥 Current session after pause:', currentSession?.status);
            if (currentSession) {
                setSession({ ...currentSession });
            }
        });

        sessionManager.on('session:resumed', ({ sessionId }) => {
            console.log('🔥 Session resumed event received:', sessionId);
            const currentSession = sessionManager.getCurrentSession();
            console.log('🔥 Current session after resume:', currentSession?.status);
            if (currentSession) {
                setSession({ ...currentSession });
            }
        });

        sessionManager.on('session:ended', ({ sessionId, duration }) => {
            console.log('🔥 Session ended event received:', sessionId, duration);
            setSession(null);
        });

        sessionManager.on('session:recovery-completed', ({ sessionId, success }) => {
            console.log('🔥 Session recovery completed:', sessionId, success);
            if (success) {
                const currentSession = sessionManager.getCurrentSession();
                if (currentSession) {
                    setSession({ ...currentSession });
                }
            }
        });

        console.log('✅ All event listeners setup completed');
    }, []);

    useEffect(() => {
        if (!sessionManagerRef.current) {
            initializeSessionManager();
        }
        return () => {
            if (sessionManagerRef.current) {
                sessionManagerRef.current.removeAllListeners();
                sessionManagerRef.current.cleanup?.();
            }
        };
    }, []);

    useEffect(() => {
        if (sessionManagerRef.current && !isLoading) {
            setupEventListeners();
        }
    }, [setupEventListeners, isLoading]);

    const createSession = useCallback(
        async (metadata: Partial<SessionMetadata> = {}) => {
            if (!sessionManagerRef.current) {
                const errorInfo: ErrorInfo = {
                    code: 'SESSION_MANAGER_NOT_READY',
                    message: 'SessionManager not ready',
                    severity: 'medium',
                    category: 'configuration',
                };
                setError(errorInfo);
                onError?.(errorInfo);
                throw new Error('SessionManager not ready');
            }

            try {
                setError(null);
                console.log('Creating new session with metadata...', metadata);

                const newSession = await sessionManagerRef.current.createSession({
                    sessionType: 'consultation',
                    priority: 'medium',
                    tags: ['medical-consultation'],
                    customFields: {},
                    ...metadata,
                });

                setSession(newSession);
                console.log('Session created successfully:', newSession);

                try {
                    console.log('📝 Setting patient and provider info for new session:', newSession.id);

                    const patientInfo: PatientInfo = {
                        id: patientId,
                        name: patientName,
                    };
                    await sessionManagerRef.current.setPatientInfo(patientInfo);

                    const providerInfo: ProviderInfo = {
                        id: doctorId,
                        name: doctorName,
                        role: 'physician',
                    };
                    await sessionManagerRef.current.setProviderInfo(providerInfo);

                    console.log('✅ Patient and provider info set successfully');
                } catch (infoErr) {
                    console.warn('Failed to set patient/provider info:', infoErr);
                }

                return newSession;
            } catch (err) {
                console.error('Session creation failed:', err);
                const errorInfo: ErrorInfo = {
                    code: 'SESSION_CREATE_FAILED',
                    message: err instanceof Error ? err.message : 'Failed to create session',
                    severity: 'medium',
                    category: 'processing',
                };
                setError(errorInfo);
                onError?.(errorInfo);
                throw err;
            }
        },
        [patientId, patientName, doctorId, doctorName, onError],
    );

    const loadSession = useCallback(
        async (sessionId: string) => {
            if (!sessionManagerRef.current) {
                const errorInfo: ErrorInfo = {
                    code: 'SESSION_MANAGER_NOT_READY',
                    message: 'SessionManager not ready',
                    severity: 'medium',
                    category: 'configuration',
                };
                setError(errorInfo);
                onError?.(errorInfo);
                throw new Error('SessionManager not ready');
            }

            try {
                setError(null);
                console.log('Loading session from storage...', sessionId);

                const storageManager = sessionManagerRef.current.getStorageManager();
                if (!storageManager) {
                    throw new Error('Storage manager not available');
                }

                const loadedSession = await storageManager.getSession(sessionId);

                if (!loadedSession) {
                    throw new Error(`Session not found: ${sessionId}`);
                }

                sessionManagerRef.current.setCurrentSession(loadedSession);

                console.log('Session loaded successfully from storage:', loadedSession);

                try {
                    if (!loadedSession.metadata.patientInfo?.id || !loadedSession.metadata.providerInfo?.id) {
                        console.log('📝 Setting patient and provider info for loaded session:', loadedSession.id);

                        const patientInfo: PatientInfo = {
                            id: patientId,
                            name: patientName,
                        };
                        await sessionManagerRef.current.setPatientInfo(patientInfo);

                        const providerInfo: ProviderInfo = {
                            id: doctorId,
                            name: doctorName,
                            role: 'physician',
                        };
                        await sessionManagerRef.current.setProviderInfo(providerInfo);

                        console.log('✅ Patient and provider info set successfully');
                    } else {
                        console.log('Patient/provider info already exists in loaded session');
                    }
                } catch (infoErr) {
                    console.warn('Failed to set patient/provider info:', infoErr);
                }

                return loadedSession;
            } catch (err) {
                console.error('Session loading failed:', err);
                const errorInfo: ErrorInfo = {
                    code: 'SESSION_LOAD_FAILED',
                    message: err instanceof Error ? err.message : 'Failed to load session',
                    severity: 'medium',
                    category: 'processing',
                };
                setError(errorInfo);
                onError?.(errorInfo);
                throw err;
            }
        },
        [patientId, patientName, doctorId, doctorName, onError],
    );

    useEffect(() => {
        if (!sessionManagerRef.current || !props.sessionId || session || isLoading) return;

        const autoLoadSession = async () => {
            try {
                console.log('Auto-loading session from props sessionId:', props.sessionId);
                await loadSession(props.sessionId!);
                console.log('Auto-load session completed successfully');
            } catch (err) {
                console.error('Auto-load session failed:', err);
            }
        };

        autoLoadSession();
    }, [props.sessionId, session, isLoading]);

    const startSession = useCallback(async () => {
        if (!sessionManagerRef.current) {
            const errorInfo: ErrorInfo = {
                code: 'SESSION_MANAGER_NOT_READY',
                message: 'SessionManager not ready',
                severity: 'medium',
                category: 'configuration',
            };
            setError(errorInfo);
            onError?.(errorInfo);
            return;
        }

        const currentSession = sessionManagerRef.current.getCurrentSession();
        if (currentSession?.status === 'ACTIVE') {
            console.log('⚠️ Session already active, skipping start');
            return;
        }

        try {
            setError(null);
            console.log('🚀 Starting session...', new Date().toISOString());
            console.log('🚀 SessionManager instance:', !!sessionManagerRef.current);
            console.log('🚀 Current session before start:', sessionManagerRef.current?.getCurrentSession()?.status);

            await sessionManagerRef.current.startSession();
            console.log('✅ Session started successfully', new Date().toISOString());

            console.log('🚀 Current session after start:', sessionManagerRef.current?.getCurrentSession()?.status);

            setTimeout(() => {
                const currentSession = sessionManagerRef.current?.getCurrentSession();
                if (currentSession) {
                    console.log('🔄 Fallback: Force updating session state', currentSession.status);
                    setSession({ ...currentSession });
                }
            }, 100);
        } catch (err) {
            console.error('❌ Session start failed:', err);
            const errorInfo: ErrorInfo = {
                code: 'SESSION_START_FAILED',
                message: err instanceof Error ? err.message : 'Failed to start session',
                severity: 'medium',
                category: 'processing',
            };
            setError(errorInfo);
            onError?.(errorInfo);
        }
    }, [onError]);

    const pauseSession = useCallback(
        async (reason: string = 'User initiated') => {
            if (!sessionManagerRef.current) {
                const errorInfo: ErrorInfo = {
                    code: 'SESSION_MANAGER_NOT_READY',
                    message: 'SessionManager not ready',
                    severity: 'medium',
                    category: 'configuration',
                };
                setError(errorInfo);
                onError?.(errorInfo);
                return;
            }

            const currentSession = sessionManagerRef.current.getCurrentSession();
            if (currentSession?.status === 'PAUSED') {
                console.log('⚠️ Session already paused, skipping pause');
                return;
            }

            try {
                setError(null);
                console.log('⏸️ Pausing session with reason:', reason, new Date().toISOString());
                await sessionManagerRef.current.pauseSession(reason);
                console.log('✅ Session paused successfully', new Date().toISOString());

                setTimeout(() => {
                    const currentSession = sessionManagerRef.current?.getCurrentSession();
                    if (currentSession) {
                        console.log('🔄 Fallback: Force updating session state', currentSession.status);
                        setSession({ ...currentSession });
                    }
                }, 100);
            } catch (err) {
                console.error('❌ Session pause failed:', err);
                const errorInfo: ErrorInfo = {
                    code: 'SESSION_PAUSE_FAILED',
                    message: err instanceof Error ? err.message : 'Failed to pause session',
                    severity: 'low',
                    category: 'processing',
                };
                setError(errorInfo);
                onError?.(errorInfo);
            }
        },
        [onError],
    );

    const resumeSession = useCallback(async () => {
        if (!sessionManagerRef.current) {
            const errorInfo: ErrorInfo = {
                code: 'SESSION_MANAGER_NOT_READY',
                message: 'SessionManager not ready',
                severity: 'medium',
                category: 'configuration',
            };
            setError(errorInfo);
            onError?.(errorInfo);
            return;
        }

        const currentSession = sessionManagerRef.current.getCurrentSession();
        if (currentSession?.status === 'ACTIVE') {
            console.log('⚠️ Session already active, skipping resume');
            return;
        }

        try {
            setError(null);
            console.log('▶️ Resuming session...', new Date().toISOString());
            await sessionManagerRef.current.resumeSession();
            console.log('✅ Session resumed successfully', new Date().toISOString());

            setTimeout(() => {
                const currentSession = sessionManagerRef.current?.getCurrentSession();
                if (currentSession) {
                    console.log('🔄 Fallback: Force updating session state', currentSession.status);
                    setSession({ ...currentSession });
                }
            }, 100);
        } catch (err) {
            console.error('❌ Session resume failed:', err);
            const errorInfo: ErrorInfo = {
                code: 'SESSION_RESUME_FAILED',
                message: err instanceof Error ? err.message : 'Failed to resume session',
                severity: 'low',
                category: 'processing',
            };
            setError(errorInfo);
            onError?.(errorInfo);
        }
    }, [onError]);

    const updateSession = useCallback(
        async (jsonData?: Record<string, unknown>) => {
            if (!sessionManagerRef.current) {
                const errorInfo: ErrorInfo = {
                    code: 'SESSION_MANAGER_NOT_READY',
                    message: 'SessionManager not ready',
                    severity: 'medium',
                    category: 'configuration',
                };
                setError(errorInfo);
                onError?.(errorInfo);
                return;
            }

            const currentSession = sessionManagerRef.current.getCurrentSession();
            if (currentSession?.status !== 'ACTIVE') {
                const errorInfo: ErrorInfo = {
                    code: 'SESSION_NOT_ACTIVE',
                    message: 'Session is not active',
                    severity: 'medium',
                    category: 'configuration',
                };
                setError(errorInfo);
                onError?.(errorInfo);
                return;
            }

            try {
                setError(null);
                console.log('▶️ Updating session...', new Date().toISOString());
                await sessionManagerRef.current.updateSession(jsonData);
                console.log('✅ Session updated successfully', new Date().toISOString());

                setTimeout(() => {
                    const currentSession = sessionManagerRef.current?.getCurrentSession();
                    if (currentSession) {
                        console.log('🔄 Fallback: Force updating session state', currentSession.status);
                        setSession({ ...currentSession });
                    }
                }, 100);
            } catch (err) {
                console.error('❌ Session resume failed:', err);
                const errorInfo: ErrorInfo = {
                    code: 'SESSION_RESUME_FAILED',
                    message: err instanceof Error ? err.message : 'Failed to resume session',
                    severity: 'low',
                    category: 'processing',
                };
                setError(errorInfo);
                onError?.(errorInfo);
            }
        },
        [onError],
    );

    const endSession = useCallback(async () => {
        if (!sessionManagerRef.current) {
            const errorInfo: ErrorInfo = {
                code: 'SESSION_MANAGER_NOT_READY',
                message: 'SessionManager not ready',
                severity: 'medium',
                category: 'configuration',
            };
            setError(errorInfo);
            onError?.(errorInfo);
            return;
        }

        try {
            setError(null);

            const currentSession = sessionManagerRef.current.getCurrentSession();
            if (currentSession) {
                const terminatedSession = {
                    ...currentSession,
                    status: 'TERMINATED' as const,
                    endTime: new Date(),
                    lastActivity: new Date(),
                };
                setSession(terminatedSession);

                await new Promise((resolve) => setTimeout(resolve, 500));
            }

            await sessionManagerRef.current.endSession();
        } catch (err) {
            const errorInfo: ErrorInfo = {
                code: 'SESSION_END_FAILED',
                message: err instanceof Error ? err.message : 'Failed to end session',
                severity: 'medium',
                category: 'processing',
            };
            setError(errorInfo);
            onError?.(errorInfo);
        }
    }, [onError]);

    const clearError = useCallback(() => {
        setError(null);
    }, []);

    return {
        session,
        isLoading,
        error,
        createSession,
        updateSession,
        loadSession,
        startSession,
        pauseSession,
        resumeSession,
        endSession,
        clearError,
    };
}
