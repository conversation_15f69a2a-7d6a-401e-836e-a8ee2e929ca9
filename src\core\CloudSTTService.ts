import { EventEmitter } from 'eventemitter3';
import type { ProcessingStatus, SDKConfig } from '../types/index';

export class CloudSTTService extends EventEmitter {
    private config: SDKConfig | null = null;
    private processingStatus: ProcessingStatus = {
        stage: 'idle',
        latency: 0,
        queueSize: 0,
    };

    public async initialize(config: SDKConfig): Promise<void> {
        this.config = config;
    }

    public getProcessingStatus(): ProcessingStatus {
        return { ...this.processingStatus };
    }

    public getProviderConfig(): string {
        return this.config?.sttProvider || 'azure';
    }

    public async cleanup(): Promise<void> {
        this.removeAllListeners();
    }
}
