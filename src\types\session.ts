import type { MedicalEntity } from './index';

export type SessionStatus = 'IDLE' | 'ACTIVE' | 'PAUSED' | 'EXPIRED' | 'TERMINATED' | 'SUSPENDED';

export interface MedicalSession {
    id: string;
    patientId?: string;
    startTime: Date;
    lastActivity: Date;
    endTime?: Date;
    status: SessionStatus;
    audioData: AudioSessionData;
    transcriptData: TranscriptSessionData;
    metadata: SessionMetadata;
    deviceInfo: DeviceInfo;
    version: number; // For conflict resolution
    checksum: string; // For integrity verification
}

export interface AudioSessionData {
    totalDuration: number; // Total audio duration in milliseconds
    chunks: AudioChunkInfo[];
    sampleRate: number;
    channels: number;
    format: 'pcm' | 'wav' | 'opus';
    totalSize: number; // Total size in bytes
    lastChunkTimestamp?: number;
}

export interface AudioChunkInfo {
    id: string;
    sessionId: string;
    timestamp: number;
    duration: number;
    size: number;
    checksum: string;
    isUploaded: boolean;
    retryCount: number;
}

export interface TranscriptSessionData {
    segments: TranscriptSegment[];
    totalCharacters: number;
    language: string;
    confidence: number;
    lastSegmentTimestamp?: number;
}

export interface TranscriptSegment {
    id: string;
    sessionId: string;
    text: string;
    startTime: number;
    endTime: number;
    confidence: number;
    speakerId?: string;
    medicalEntities?: MedicalEntity[];
    isFinalized: boolean;
    isSynced: boolean;
}

export interface SessionMetadata {
    title?: string;
    description?: string;
    tags: string[];
    priority: 'low' | 'medium' | 'high' | 'critical';
    patientInfo?: PatientInfo;
    providerInfo?: ProviderInfo;
    appointmentId?: string;
    sessionType: 'consultation' | 'follow-up' | 'emergency' | 'routine';
    customFields: Record<string, unknown>;
    serverSessionId?: string; // Server-side session ID for database record
}

export interface PatientInfo {
    id: string;
    mrn?: string; // Medical Record Number
    name?: string;
    dateOfBirth?: Date;
    gender?: 'male' | 'female' | 'other' | 'unknown';
}

export interface ProviderInfo {
    id: string;
    name: string;
    role: 'physician' | 'nurse' | 'specialist' | 'technician';
    department?: string;
    licenseNumber?: string;
}

export interface DeviceInfo {
    deviceId: string;
    deviceType: 'desktop' | 'tablet' | 'mobile';
    browser: string;
    browserVersion: string;
    platform: string;
    userAgent: string;
    screenResolution: string;
    timezone: string;
    lastSeen: Date;
}

export interface SessionStorageConfig {
    encryptionKey: string;
    backupInterval: number; // milliseconds, default: 5 minutes
    maxStorageSize: number; // bytes, default: 100MB
    compressionEnabled: boolean;
    retentionPeriod: number; // milliseconds, default: 24 hours
    maxSessions: number; // default: 50
    autoCleanup: boolean;
}

export interface SessionManagerConfig extends SessionStorageConfig {
    apiEndpoint?: string;
    credentials?: {
        apiKey?: string;
        clientId?: string;
        clientSecret?: string;
        accessToken?: string;
        refreshToken?: string;
    };
}

export interface EncryptedSessionData {
    sessionId: string;
    encryptedData: string;
    iv: string; // Initialization vector for encryption
    checksum: string;
    timestamp: number;
    version: number;
    size: number;
}

export interface StorageQuota {
    total: number;
    used: number;
    available: number;
    percentage: number;
}

export interface RecoveryOptions {
    autoRecover: boolean;
    showRecoveryModal: boolean;
    maxRecoveryAttempts: number;
    recoveryTimeout: number; // milliseconds
    fallbackToPartialRecovery: boolean;
    userConfirmationRequired: boolean;
}

export interface SessionRecoveryInfo {
    sessionId: string;
    lastActivity: Date;
    dataIntegrity: 'complete' | 'partial' | 'corrupted';
    recoveryType: 'full' | 'partial' | 'metadata-only';
    missingComponents: string[];
    estimatedRecoveryTime: number;
    riskLevel: 'low' | 'medium' | 'high';
}

export interface RecoveryProgress {
    stage: 'detecting' | 'validating' | 'restoring' | 'syncing' | 'complete' | 'failed';
    progress: number; // 0-100
    currentStep: string;
    estimatedTimeRemaining: number;
    errors: string[];
}

export interface OfflineAction {
    id: string;
    sessionId: string;
    action: 'create' | 'update' | 'delete' | 'upload';
    data: unknown;
    timestamp: number;
    priority: 'low' | 'medium' | 'high' | 'critical';
    retryCount: number;
    maxRetries: number;
    dependencies: string[]; // Other action IDs this depends on
}

export interface SyncStatus {
    isOnline: boolean;
    lastSyncTime: Date;
    pendingActions: number;
    syncInProgress: boolean;
    conflictsDetected: number;
    errorCount: number;
    nextSyncAttempt?: Date;
}

export interface SessionConflict {
    sessionId: string;
    conflictType: 'version' | 'device' | 'concurrent-edit' | 'data-mismatch';
    localVersion: number;
    remoteVersion: number;
    localDevice: string;
    remoteDevice: string;
    conflictingFields: string[];
    autoResolvable: boolean;
    resolutionOptions: ConflictResolutionOption[];
}

export interface ConflictResolutionOption {
    strategy: 'local' | 'remote' | 'merge' | 'manual';
    description: string;
    riskLevel: 'low' | 'medium' | 'high';
    dataLoss: boolean;
}

export interface DeviceRegistration {
    deviceId: string;
    deviceName: string;
    deviceType: 'desktop' | 'tablet' | 'mobile';
    userId: string;
    registrationDate: Date;
    lastActivity: Date;
    isActive: boolean;
    capabilities: DeviceCapabilities;
    trustLevel: 'trusted' | 'verified' | 'unverified';
}

export interface DeviceCapabilities {
    supportsAudio: boolean;
    supportsVideo: boolean;
    supportsOfflineStorage: boolean;
    storageQuota: number;
    networkType: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
    batteryLevel?: number;
    isCharging?: boolean;
}

export interface SessionTransferRequest {
    sessionId: string;
    sourceDeviceId: string;
    targetDeviceId: string;
    transferType: 'full' | 'state-only' | 'continue-here';
    userInitiated: boolean;
    timestamp: Date;
    expiresAt: Date;
}

export interface SessionLifecycleEvents {
    'session:created': { session: MedicalSession };
    'session:loaded': { session: MedicalSession };
    'session:started': { sessionId: string };
    'session:paused': { sessionId: string; reason: string };
    'session:resumed': { sessionId: string };
    'session:ended': { sessionId: string; duration: number };
    'session:backup-created': { sessionId: string; backupId: string };
    'session:recovery-started': { sessionId: string; recoveryInfo: SessionRecoveryInfo };
    'session:recovery-progress': { sessionId: string; progress: RecoveryProgress };
    'session:recovery-completed': { sessionId: string; success: boolean };
    'session:conflict-detected': { conflict: SessionConflict };
    'session:transfer-requested': { transfer: SessionTransferRequest };
    'session:transfer-completed': { sessionId: string; targetDevice: string };
    'session:sync-started': { sessionId: string };
    'session:sync-completed': { sessionId: string; success: boolean };
    'session:storage-warning': { currentUsage: number; threshold: number };
    'session:cleanup-completed': { removedSessions: number; freedSpace: number };
}

export interface SessionPerformanceMetrics {
    sessionId: string;
    creationTime: number;
    lastBackupTime: number;
    recoveryTime?: number;
    totalSize: number;
    audioSize: number;
    transcriptSize: number;
    metadataSize: number;
    syncLatency: number;
    storageOperationTime: number;
    compressionRatio?: number;
}

export interface SessionHealthCheck {
    sessionId: string;
    isHealthy: boolean;
    lastCheck: Date;
    issues: SessionIssue[];
    recommendations: string[];
}

export interface SessionIssue {
    type: 'data-corruption' | 'sync-failure' | 'storage-limit' | 'network-issue' | 'device-compatibility';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    affectedComponents: string[];
    suggestedAction: string;
    autoFixable: boolean;
}

export interface SessionError extends Error {
    sessionId?: string;
    errorType: 'storage' | 'encryption' | 'network' | 'recovery' | 'sync' | 'validation';
    errorCode: string;
    recoverable: boolean;
    userMessage: string;
    technicalDetails: Record<string, unknown>;
    timestamp: Date;
}