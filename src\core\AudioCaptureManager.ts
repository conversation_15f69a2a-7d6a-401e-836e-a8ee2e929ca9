import { EventEmitter } from 'eventemitter3';
import type { AudioConfig, AudioDeviceStatus } from '../types/index';

export class AudioCaptureManager extends EventEmitter {
    private config: AudioConfig | null = null;
    private isCapturing = false;
    private mediaStream: MediaStream | null = null;
    private audioContext: AudioContext | null = null;
    private workletNode: AudioWorkletNode | null = null;

    public async initialize(config: AudioConfig): Promise<void> {
        this.config = config;
    }

    public async startCapture(): Promise<void> {
        if (this.isCapturing) return;
        
        if (!this.config) {
            throw new Error('AudioCaptureManager not initialized.');
        }

        try {
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: this.config.sampleRate,
                    channelCount: this.config.channels,
                    echoCancellation: this.config.echoCancellation,
                    noiseSuppression: this.config.noiseCancellation,
                    autoGainControl: this.config.autoGainControl,
                },
            });

            this.audioContext = new AudioContext({ sampleRate: this.config.sampleRate });
            
            await this.audioContext.audioWorklet.addModule(
                URL.createObjectURL(
                    new Blob(
                        [
                            `
                            class PCMProcessor extends AudioWorkletProcessor {
                                constructor() {
                                    super();
                                    this.buf = [];
                                }
                                process(inputs) {
                                    const in0 = inputs[0][0];
                                    if (!in0) return true;
                                    this.buf.push(...in0);
                                    while (this.buf.length >= ${this.config.sampleRate}) {
                                        this.port.postMessage(this.buf.splice(0, ${this.config.sampleRate}));
                                    }
                                    return true;
                                }
                            }
                            registerProcessor('pcm-processor', PCMProcessor);
                            `,
                        ],
                        { type: 'application/javascript' },
                    ),
                ),
            );

            this.workletNode = new AudioWorkletNode(this.audioContext, 'pcm-processor');
            this.workletNode.port.onmessage = (e) => {
                const float32 = e.data;
                const pcm16 = new Int16Array(float32.length);
                for (let i = 0; i < float32.length; i++) {
                    const s = Math.max(-1, Math.min(1, float32[i]));
                    pcm16[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
                }
                this.emit('audioData', pcm16.buffer);
            };

            const src = this.audioContext.createMediaStreamSource(this.mediaStream);
            src.connect(this.workletNode).connect(this.audioContext.destination);

            this.isCapturing = true;
            this.emit('captureStarted');
        } catch (error) {
            throw new Error(`Failed to start audio capture: ${error}`);
        }
    }

    public async stopCapture(): Promise<void> {
        if (!this.isCapturing) return;

        this.workletNode?.disconnect();
        this.audioContext?.close();
        
        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach((track) => track.stop());
            this.mediaStream = null;
        }

        this.audioContext = null;
        this.workletNode = null;
        this.isCapturing = false;
        this.emit('captureStopped');
    }

    public async getDeviceStatus(): Promise<AudioDeviceStatus> {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const inputDevices = devices.filter((device) => device.kind === 'audioinput');

        const result: AudioDeviceStatus = {
            inputDevices,
            permissionStatus: 'granted',
            audioLevel: 0,
        };

        if (inputDevices.length > 0) {
            result.selectedDevice = inputDevices[0]!;
        }

        return result;
    }

    public getIsCapturing(): boolean {
        return this.isCapturing;
    }

    public async cleanup(): Promise<void> {
        await this.stopCapture();
        this.removeAllListeners();
    }
}
