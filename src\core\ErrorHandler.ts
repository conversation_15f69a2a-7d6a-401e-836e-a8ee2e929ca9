import { EventEmitter } from 'eventemitter3';
import type { ErrorConfig, ErrorInfo } from '../types/index';

export class ErrorHandler extends EventEmitter {
    private config: ErrorConfig | null = null;
    private errorCount = 0;
    private errorHistory: ErrorInfo[] = [];

    public async initialize(config: ErrorConfig): Promise<void> {
        this.config = config;
        this.errorCount = 0;
        this.errorHistory = [];
    }

    public handleError(error: Error, category: ErrorInfo['category']): ErrorInfo {
        const errorInfo = this.classifyError(error, category);

        this.recordError(errorInfo);

        if (this.config?.enabled && this.shouldReportError()) {
            this.reportError(errorInfo);
        }

        this.emit('error', errorInfo);

        return errorInfo;
    }

    public getRecoverySuggestions(errorInfo: ErrorInfo): string[] {
        const suggestions: string[] = [];

        switch (errorInfo.category) {
            case 'network':
                suggestions.push('Check your internet connection');
                suggestions.push('Try reconnecting to the service');
                if (errorInfo.code === 'WEBSOCKET_CONNECTION_FAILED') {
                    suggestions.push('Verify WebSocket URL configuration');
                }
                break;

            case 'audio':
                suggestions.push('Check microphone permissions');
                suggestions.push('Ensure microphone is not being used by another application');
                if (errorInfo.code === 'DEVICE_NOT_FOUND') {
                    suggestions.push('Connect a microphone device');
                }
                break;

            case 'authentication':
                suggestions.push('Verify your API credentials');
                suggestions.push('Check if your access token has expired');
                break;

            case 'configuration':
                suggestions.push('Review your SDK configuration');
                suggestions.push('Ensure all required fields are provided');
                break;

            case 'processing':
                suggestions.push('Try reducing audio quality settings');
                suggestions.push('Check if the service is experiencing high load');
                break;
        }

        return suggestions;
    }

    public getErrorStats(): ErrorStats {
        const categoryCount: Record<string, number> = {};
        const severityCount: Record<string, number> = {};

        this.errorHistory.forEach((error) => {
            categoryCount[error.category] = (categoryCount[error.category] || 0) + 1;
            severityCount[error.severity] = (severityCount[error.severity] || 0) + 1;
        });

        return {
            totalErrors: this.errorCount,
            recentErrors: this.errorHistory.length,
            categoryBreakdown: categoryCount,
            severityBreakdown: severityCount,
        };
    }

    public clearHistory(): void {
        this.errorHistory = [];
    }

    private classifyError(error: Error, category: ErrorInfo['category']): ErrorInfo {
        let code = 'UNKNOWN_ERROR';
        let severity: ErrorInfo['severity'] = 'medium';
        const details: Record<string, unknown> = {};

        // Network errors
        if (category === 'network') {
            if (error.message.includes('WebSocket')) {
                code = 'WEBSOCKET_CONNECTION_FAILED';
                severity = 'high';
            } else if (error.message.includes('fetch') || error.message.includes('network')) {
                code = 'NETWORK_REQUEST_FAILED';
                severity = 'medium';
            }
        }

        // Audio errors
        else if (category === 'audio') {
            if (error.message.includes('Permission denied')) {
                code = 'MICROPHONE_PERMISSION_DENIED';
                severity = 'high';
            } else if (error.message.includes('device')) {
                code = 'DEVICE_NOT_FOUND';
                severity = 'medium';
            } else if (error.message.includes('NotAllowedError')) {
                code = 'AUDIO_ACCESS_DENIED';
                severity = 'high';
            }
        }

        // Authentication errors
        else if (category === 'authentication') {
            if (error.message.includes('401') || error.message.includes('Unauthorized')) {
                code = 'INVALID_CREDENTIALS';
                severity = 'high';
            } else if (error.message.includes('403') || error.message.includes('Forbidden')) {
                code = 'ACCESS_FORBIDDEN';
                severity = 'high';
            }
        }

        // Configuration errors
        else if (category === 'configuration') {
            code = 'INVALID_CONFIGURATION';
            severity = 'critical';
        }

        // Processing errors
        else if (category === 'processing') {
            if (error.message.includes('timeout')) {
                code = 'PROCESSING_TIMEOUT';
                severity = 'medium';
            } else {
                code = 'PROCESSING_FAILED';
                severity = 'low';
            }
        }

        const errorInfo: ErrorInfo = {
            code,
            message: error.message,
            severity,
            category,
            details,
        };

        if (this.config?.includeStackTrace && error.stack) {
            errorInfo.stack = error.stack;
        }

        return errorInfo;
    }

    private recordError(errorInfo: ErrorInfo): void {
        this.errorCount++;
        this.errorHistory.push(errorInfo);

        // Maintain history size
        const maxHistorySize = 50;
        if (this.errorHistory.length > maxHistorySize) {
            this.errorHistory.shift();
        }
    }

    private shouldReportError(): boolean {
        if (!this.config?.enabled) return false;

        return this.errorCount <= (this.config.maxErrorsPerSession || 10);
    }

    private reportError(errorInfo: ErrorInfo): void {
        if (!this.config?.endpoint) return;

        console.warn('Error reporting not yet implemented:', errorInfo);
    }
}

interface ErrorStats {
    totalErrors: number;
    recentErrors: number;
    categoryBreakdown: Record<string, number>;
    severityBreakdown: Record<string, number>;
}
