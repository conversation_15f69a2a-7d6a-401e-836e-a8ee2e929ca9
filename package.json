{"name": "@arcaai/agentic-sdk", "version": "0.1.7", "description": "Cloud-first AgenticSDK for medical conversation applications with enterprise-grade reliability", "private": false, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "rimraf dist && ncc build src/index.ts --out dist --minify --external react --external react-dom", "clean": "rimraf dist tsconfig.tsbuildinfo", "nuke": "rimraf dist node_modules .turbo tsconfig.tsbuildinfo", "lint": "eslint \"src/**/*.ts*\" --max-warnings 0", "test": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "docs:generate": "typedoc src/index.ts --out docs", "type-check": "tsc --noEmit"}, "keywords": ["medical", "conversation", "speech-to-text", "text-to-speech", "webassembly", "cloud-first", "healthcare", "agentic", "sdk"], "author": "ARCAAI", "license": "MIT", "peerDependencies": {"react": ">=16.8.0"}, "devDependencies": {"@types/node": "^22.13.13", "@types/react": "^18.2.0", "@types/socket.io-client": "^3.0.0", "@vercel/ncc": "^0.38.3", "@vitest/coverage-v8": "^1.6.0", "jsdom": "^24.0.0", "typedoc": "^0.25.13", "typescript": "^5.3.3", "vitest": "^1.6.0"}, "publishConfig": {"access": "public", "registry": "https://npm.pkg.github.com"}, "repository": {"type": "git", "url": "git+https://github.com/ArcaAI/Arca-AgenticSDK.git"}, "type": "module", "dependencies": {"eventemitter3": "^5.0.1", "socket.io-client": "^4.8.1"}}