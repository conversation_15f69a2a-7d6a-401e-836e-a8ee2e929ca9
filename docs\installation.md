# Installation & Setup Guide

This guide will walk you through setting up the Agentic Audio SDK in your React application, from installation to your first working consultation app.

## 📋 Prerequisites

Before installing the SDK, ensure your development environment meets these requirements:

### System Requirements

| Requirement    | Minimum Version | Recommended              |
| -------------- | --------------- | ------------------------ |
| **Node.js**    | 16.0.0          | 18.0.0+                  |
| **npm**        | 8.0.0           | 9.0.0+                   |
| **React**      | 18.0.0          | 18.2.0+                  |
| **TypeScript** | 4.5.0           | 5.0.0+ (for TS projects) |

### Browser Support

| Browser | Minimum Version | Features                  |
| ------- | --------------- | ------------------------- |
| Chrome  | 90+             | ✅ All features supported |
| Firefox | 88+             | ✅ All features supported |
| Safari  | 14+             | ✅ All features supported |
| Edge    | 90+             | ✅ All features supported |

### Required Browser APIs

The SDK requires these browser APIs to be available:

- **WebSocket API** - Real-time communication
- **Web Audio API** - Audio processing
- **MediaDevices API** - Microphone access
- **Blob API** - Audio data handling

## 🚀 Installation Methods

### Method 1: NPM Registry (Recommended)

```bash
# Using npm
npm install @arcaai/agentic-sdk

# Using yarn
yarn add @arcaai/agentic-sdk

# Using pnpm
pnpm add @arcaai/agentic-sdk
```

### Method 2: GitHub Package Registry

If you're installing from GitHub packages, first configure your npm registry:

```bash
# Configure GitHub package registry
echo "@arcaai:registry=https://npm.pkg.github.com" >> .npmrc

# Install the package
npm install @arcaai/agentic-sdk
```

### Method 3: Local Development

For local development or testing:

```bash
# Clone the repository
git clone https://github.com/ArcaAI/Arca-AgenticSDK.git

# Navigate to the SDK directory
cd Arca-AgenticSDK

# Install dependencies
npm install

# Build the SDK
npm run build

# Link for local development
npm link

# In your project directory
npm link @arcaai/agentic-sdk
```

## ⚙️ Configuration Setup

### Step 1: Create SDK Configuration File

The SDK uses a centralized configuration approach. Create a configuration file in your project:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: "your-api-key-here",
  },
};
```

### Environment-Based Configuration

For different environments, you can use environment variables:

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: process.env.REACT_APP_API_ENDPOINT || "https://api.arcaai.com",
  websocketUrl: process.env.REACT_APP_WS_ENDPOINT || "wss://api.arcaai.com",
  credentials: {
    apiKey: process.env.REACT_APP_API_KEY || "your-default-api-key",
  },
};
```

And create environment files:

```bash
# .env

# API Configuration
REACT_APP_API_ENDPOINT=https://api.taphuynh.dev
REACT_APP_WS_ENDPOINT=wss://api.taphuynh.dev
REACT_APP_API_KEY=3GHtCUPJDSRIzonWoAMU0GO6p01ggC0w

# SMR (Summarization) Service
REACT_APP_SMR_ENDPOINT=https://smr.arcaai.com/api/smr

# Optional: Development overrides
# REACT_APP_API_ENDPOINT=http://localhost:8080
# REACT_APP_WS_ENDPOINT=ws://localhost:8080
# REACT_APP_SMR_ENDPOINT=http://localhost:5002/api/smr

# Environment
REACT_APP_ENVIRONMENT=development

# Logging
REACT_APP_LOG_LEVEL=debug
```

### Step 2: TypeScript Configuration

If using TypeScript, update your `tsconfig.json`:

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["dom", "dom.iterable", "es6", "es2020"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx"
  },
  "include": ["src", "node_modules/@arcaai/agentic-sdk/dist/index.d.ts"]
}
```

### Step 3: Webpack Configuration (if needed)

For custom webpack configurations, ensure these settings:

```javascript
// webpack.config.js
module.exports = {
  resolve: {
    fallback: {
      crypto: require.resolve("crypto-browserify"),
      stream: require.resolve("stream-browserify"),
      buffer: require.resolve("buffer"),
    },
  },
  plugins: [
    new webpack.ProvidePlugin({
      Buffer: ["buffer", "Buffer"],
    }),
  ],
};
```

### Step 4: Vite Configuration (if using Vite)

For Vite projects, update `vite.config.ts`:

```typescript
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react()],
  define: {
    global: "globalThis",
  },
  optimizeDeps: {
    include: ["@arcaai/agentic-sdk"],
  },
});
```

## 🔐 API Key Setup

### Obtaining Your API Key

1. **Sign up** at [console.arcaai.com](https://console.arcaai.com)
2. **Create a new project** for your medical application
3. **Generate an API key** in the project settings
4. **Copy the API key** to your configuration

### API Key Configuration Examples

#### Basic Configuration

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: "your-api-key-here",
  },
};
```

#### Advanced Configuration with Options

```typescript
// src/config/sdk-config.ts
export const SDK_CONFIG_OPTIONS = {
  apiEndpoint: "https://api.arcaai.com",
  websocketUrl: "wss://api.arcaai.com",
  credentials: {
    apiKey: "your-api-key-here",
  },
  // Optional: Audio settings
  audioSettings: {
    sampleRate: 16000,
    channels: 1,
    noiseCancellation: true,
    echoCancellation: true,
    autoGainControl: true,
  },
  // Optional: Logging configuration
  logging: {
    level: "info",
    console: true,
  },
  // Optional: Environment
  environment: "production",
};
```

## ✅ Verification & Testing

### Step 1: Basic Import Test

Create a test file to verify the installation:

```typescript
// src/test/sdk-test.ts
import {
  useArcaSessionManager,
  useArcaSpeechToText,
  useAudioCapture,
  useArcaTextToSpeech,
  useSMR,
} from "@arcaai/agentic-sdk";

import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

console.log("✅ SDK imports successful");
console.log("✅ Configuration loaded:", SDK_CONFIG_OPTIONS.apiEndpoint);
```

### Step 2: API Connection Test

Test your API connection:

```typescript
// src/test/api-test.ts
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

async function testAPIConnection() {
  try {
    const response = await fetch(`${SDK_CONFIG_OPTIONS.apiEndpoint}/health`, {
      headers: {
        "x-api-key": SDK_CONFIG_OPTIONS.credentials.apiKey,
      },
    });

    if (response.ok) {
      console.log("✅ API connection successful");
      return true;
    } else {
      console.error("❌ API connection failed:", response.statusText);
      return false;
    }
  } catch (error) {
    console.error("❌ API connection error:", error);
    return false;
  }
}

testAPIConnection();
```

### Step 3: Audio Permissions Test

Test browser audio permissions:

```typescript
// src/test/audio-test.ts
async function testAudioPermissions() {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: true,
    });

    console.log("✅ Audio permissions granted");
    stream.getTracks().forEach((track) => track.stop());
    return true;
  } catch (error) {
    console.error("❌ Audio permissions denied:", error);
    return false;
  }
}

testAudioPermissions();
```

### Step 4: Complete Integration Test

Create a minimal test component:

```typescript
// src/components/SDKTest.tsx
import React, { useEffect, useState } from "react";
import { useArcaSessionManager } from "@arcaai/agentic-sdk";
import { SDK_CONFIG_OPTIONS } from "../config/sdk-config";

export function SDKTest() {
  const [testResults, setTestResults] = useState<string[]>([]);

  const sessionManager = useArcaSessionManager({
    doctorId: "test-doctor",
    doctorName: "Test Doctor",
    patientId: "test-patient",
    patientName: "Test Patient",
    options: SDK_CONFIG_OPTIONS,
    onError: (error) => {
      setTestResults((prev) => [...prev, `❌ Error: ${error.message}`]);
    },
  });

  useEffect(() => {
    if (!sessionManager.isLoading && !sessionManager.error) {
      setTestResults((prev) => [...prev, "✅ Session Manager initialized"]);
    }
  }, [sessionManager.isLoading, sessionManager.error]);

  const runTest = async () => {
    try {
      setTestResults(["🧪 Running SDK tests..."]);

      // Test session creation
      await sessionManager.createSession({
        sessionType: "consultation",
        tags: ["test"],
      });
      setTestResults((prev) => [...prev, "✅ Session created"]);

      // Test session start
      await sessionManager.startSession();
      setTestResults((prev) => [...prev, "✅ Session started"]);

      // Test session end
      await sessionManager.endSession();
      setTestResults((prev) => [...prev, "✅ Session ended"]);

      setTestResults((prev) => [...prev, "🎉 All tests passed!"]);
    } catch (error) {
      setTestResults((prev) => [...prev, `❌ Test failed: ${error}`]);
    }
  };

  return (
    <div style={{ padding: "20px", fontFamily: "monospace" }}>
      <h2>🧪 Agentic SDK Test</h2>

      <div style={{ marginBottom: "20px" }}>
        <button onClick={runTest} disabled={sessionManager.isLoading}>
          Run Tests
        </button>
      </div>

      <div>
        <h3>Test Results:</h3>
        {testResults.map((result, index) => (
          <div key={index} style={{ padding: "2px 0" }}>
            {result}
          </div>
        ))}
      </div>

      {sessionManager.error && (
        <div style={{ color: "red", marginTop: "10px" }}>
          <strong>Error:</strong> {sessionManager.error.message}
        </div>
      )}
    </div>
  );
}
```

## 🐛 Common Installation Issues

### Issue 1: Module Resolution Errors

**Error:** `Cannot resolve module '@arcaai/agentic-sdk'`

**Solution:**

```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Or with yarn
rm -rf node_modules yarn.lock
yarn install
```

### Issue 2: Configuration Import Errors

**Error:** `Cannot find module '../config/sdk-config'`

**Solution:**

```bash
# Ensure configuration file exists
touch src/config/sdk-config.ts

# Add the basic configuration structure
```

### Issue 3: TypeScript Type Errors

**Error:** `Cannot find type definitions`

**Solution:**

```bash
# Install type definitions
npm install --save-dev @types/node

# Update tsconfig.json to include SDK types
```

### Issue 4: WebSocket Connection Failures

**Error:** `WebSocket connection failed`

**Solutions:**

1. Check your API endpoint configuration in `SDK_CONFIG_OPTIONS`
2. Verify your API key is correct
3. Ensure WebSocket URL uses `wss://` for HTTPS sites
4. Check firewall/proxy settings

### Issue 5: Audio Permission Denied

**Error:** `NotAllowedError: Permission denied`

**Solutions:**

1. Enable microphone permissions in browser
2. Use HTTPS for production (required for audio access)
3. Handle permission requests gracefully in your app

### Issue 6: Build Errors with Webpack

**Error:** `Module not found: Can't resolve 'crypto'`

**Solution:**

```bash
# Install browser polyfills
npm install crypto-browserify stream-browserify buffer

# Update webpack config as shown above
```

## 🎯 Next Steps

Now that you have the SDK installed and configured:

1. **[Follow the Basic Usage Guide](./basic-usage.md)** - Build your first consultation app
2. **[Explore the Hooks](./hooks/use-arca-session-manager.md)** - Learn about individual components
3. **[Check out Examples](./examples/README.md)** - See working implementations
4. **[Review Configuration Options](./configuration.md)** - Customize the SDK for your needs

## 📁 Recommended Project Structure

After installation, your project structure should look like this:

```
src/
├── config/
│   └── sdk-config.ts          # Centralized SDK configuration
├── components/
│   ├── MedicalConsole/
│   ├── AudioControls/
│   └── TranscriptDisplay/
├── hooks/                     # Custom React hooks
├── services/                  # API and external services
├── utils/                     # Shared utilities
├── types/                     # TypeScript definitions
└── App.tsx
```

## 🆘 Getting Help

If you encounter issues during installation:

1. **Check our [Troubleshooting Guide](./advanced/troubleshooting.md)**
2. **Search [GitHub Issues](https://github.com/ArcaAI/Arca-AgenticSDK/issues)**
3. **Contact Support** at [<EMAIL>](mailto:<EMAIL>)
4. **Join our [Discord Community](https://discord.gg/arcaai)**

---

**Installation Complete!** 🎉 You're ready to start building medical consultation applications with the Agentic Audio SDK using the centralized `SDK_CONFIG_OPTIONS` approach.
