/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable jsx-a11y/label-has-associated-control */
import { useState, useCallback } from 'react';
import { useArcaSessionManager, useSMR } from '@arcaai/agentic-sdk';
import type { ErrorInfo } from '@arcaai/agentic-sdk';
import { SDK_CONFIG_OPTIONS } from './assets/constants';

export function SummarizationDemo() {
    // State for text input and results
    const [inputText, setInputText] = useState<string>('');
    const [chunks, setChunks] = useState<string[]>([]);
    const [summaries, setSummaries] = useState<any[]>([]);
    const [currentSummary, setCurrentSummary] = useState<any>(null);
    const [summaryProgress, setSummaryProgress] = useState<number>(0);
    const [isProcessing, setIsProcessing] = useState<boolean>(false);
    const [language, setLanguage] = useState<string>('en-US');

    // Session Manager state
    const [doctorName, setDoctorName] = useState<string>('Dr. Smith');
    const [patientName, setPatientName] = useState<string>('John Doe');
    const [sessionIdInput, setSessionIdInput] = useState<string>('');

    // Chunking configuration
    const MAX_CHUNK_LENGTH = 6000; // Conservative limit to stay under 8192 tokens
    const OVERLAP_SIZE = 200; // Overlap between chunks for context

    // Helper function to split text into chunks
    const splitTextIntoChunks = useCallback((text: string): string[] => {
        if (text.length <= MAX_CHUNK_LENGTH) {
            return [text];
        }

        const chunks: string[] = [];
        let start = 0;

        while (start < text.length) {
            let end = start + MAX_CHUNK_LENGTH;

            // If this isn't the last chunk, try to find a good break point
            if (end < text.length) {
                // Look for sentence endings near the target position
                const searchStart = Math.max(start + MAX_CHUNK_LENGTH - 200, start);
                const searchEnd = Math.min(start + MAX_CHUNK_LENGTH + 100, text.length);
                const segment = text.substring(searchStart, searchEnd);

                // Find the last sentence ending
                const sentenceEndings = ['. ', '! ', '? ', '.\n', '!\n', '?\n'];
                let bestBreak = -1;

                for (const ending of sentenceEndings) {
                    const lastIndex = segment.lastIndexOf(ending);
                    if (lastIndex > bestBreak) {
                        bestBreak = lastIndex;
                    }
                }

                if (bestBreak > -1) {
                    end = searchStart + bestBreak + 1;
                } else {
                    // Fall back to word boundary
                    const lastSpace = text.lastIndexOf(' ', end);
                    if (lastSpace > start + MAX_CHUNK_LENGTH - 500) {
                        end = lastSpace;
                    }
                }
            }

            chunks.push(text.substring(start, end));
            start = Math.max(end - OVERLAP_SIZE, end);
        }

        return chunks;
    }, []);

    // Helper function to safely extract summary data
    const extractSummaryData = (summaryResponse: any) => {
        if (!summaryResponse) return null;

        // Check if this is the new SummaryResponse format
        if (summaryResponse.summary && summaryResponse.session_id) {
            const actualSummary = summaryResponse.summary;

            // Handle EnhancedMedicalSummary format
            if (actualSummary.encounter_summary) {
                return {
                    summary: actualSummary.clinical_summary?.summary || actualSummary.encounter_summary?.chief_complaint || 'No summary available',
                    keyPoints: actualSummary.clinical_summary?.key_findings || [],
                    diagnosis: actualSummary.clinical_assessment?.primary_diagnosis?.diagnosis || '',
                    recommendations: actualSummary.treatment_plan?.procedures || [],
                    medications: actualSummary.treatment_plan?.medications?.map((med: any) => med.name) || [],
                    metadata: {
                        provider: summaryResponse.llm_provider,
                        model: summaryResponse.model_name,
                        processingTime: summaryResponse.processing_time_ms,
                        confidence: summaryResponse.confidence_score
                    }
                };
            }

            // Handle SimplifiedMedicalSummary format
            if (actualSummary.chief_complaint || actualSummary.summary) {
                return {
                    summary: actualSummary.summary || actualSummary.chief_complaint,
                    keyPoints: actualSummary.symptoms || [],
                    diagnosis: actualSummary.assessment || '',
                    recommendations: actualSummary.treatment_plan ? [actualSummary.treatment_plan] : [],
                    medications: [],
                    metadata: {
                        provider: summaryResponse.llm_provider,
                        model: summaryResponse.model_name,
                        processingTime: summaryResponse.processing_time_ms,
                        confidence: summaryResponse.confidence_score
                    }
                };
            }
        }

        // Fallback to old format (backward compatibility)
        return summaryResponse;
    };

    // Use Session Manager hook
    const {
        session,
        isLoading: sessionLoading,
        error: sessionError,
        createSession,
        startSession,
        pauseSession,
        resumeSession,
        endSession,
        loadSession,
        clearError
    } = useArcaSessionManager({
        doctorId: 'doctor-123',
        doctorName: doctorName,
        patientId: 'patient-456',
        patientName: patientName,
        options: {
            apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
            credentials: SDK_CONFIG_OPTIONS.credentials
        },
        onError: (error: ErrorInfo) => {
            console.error('Session Manager Error:', error);
        }
    });

    // SMR (Summarization) hook
    const smr = useSMR({
        apiEndpoint: SDK_CONFIG_OPTIONS.apiEndpoint,
        sessionId: session?.status === 'ACTIVE' ? session.id : undefined,
        options: {
            credentials: {
                apiKey: SDK_CONFIG_OPTIONS.credentials.apiKey
            }
        },
        onProgress: (progress: number) => {
            setSummaryProgress(progress);
        },
        onComplete: (summary: any) => {
            setCurrentSummary(summary);
            setSummaryProgress(100);
        },
        onError: (error: ErrorInfo) => {
            console.error('SMR Error:', error);
        }
    });

    // Check if summarization can be used
    const canUseSummarization = session?.status === 'ACTIVE' && smr.isConnected;

    // Handle session creation
    const handleCreateSession = async () => {
        try {
            await createSession({
                sessionType: 'consultation',
                priority: 'medium',
                tags: ['summarization-session', 'medical-consultation'],
                customFields: {
                    language: language,
                    createdFrom: 'summarization-demo'
                }
            });
            console.log('✅ Session created successfully');
        } catch (error) {
            console.error('❌ Failed to create session:', error);
        }
    };

    // Handle session start
    const handleStartSession = async () => {
        try {
            await startSession();
            console.log('✅ Session started successfully');
        } catch (error) {
            console.error('❌ Failed to start session:', error);
        }
    };

    // Handle session load
    const handleLoadSession = async () => {
        if (!sessionIdInput.trim()) {
            alert('Please enter a session ID');
            return;
        }

        try {
            await loadSession(sessionIdInput.trim());
            console.log('✅ Session loaded successfully:', sessionIdInput);
        } catch (error) {
            console.error('❌ Failed to load session:', error);
            alert('Failed to load session. Check console for details.');
        }
    };

    // Handle text chunking
    const handleChunkText = () => {
        if (!inputText.trim()) {
            alert('Please enter text to analyze');
            return;
        }

        const textChunks = splitTextIntoChunks(inputText.trim());
        setChunks(textChunks);
        setSummaries([]);
        setCurrentSummary(null);

        console.log(`Text split into ${textChunks.length} chunks:`, textChunks.map((chunk, i) => ({
            chunk: i + 1,
            length: chunk.length,
            preview: chunk.substring(0, 50) + '...'
        })));
    };

    // Handle summarization
    const handleSummarize = async () => {
        if (!canUseSummarization) {
            alert('Session must be active to use Medical Summarization');
            return;
        }

        if (!inputText.trim()) {
            alert('Please enter text to summarize');
            return;
        }

        try {
            setIsProcessing(true);
            setSummaryProgress(0);
            setSummaries([]);
            setCurrentSummary(null);

            // First, chunk the text
            const textChunks = splitTextIntoChunks(inputText.trim());
            setChunks(textChunks);

            console.log(`Processing ${textChunks.length} chunks for summarization`);

            if (textChunks.length === 1) {
                // Single chunk - direct summarization
                const summary = await smr.summarizeSync({
                    text: textChunks[0],
                    sessionId: session?.id,
                    language: language.split('-')[0]
                });

                setCurrentSummary(summary);
                setSummaries([summary]);
                console.log('Single chunk summarization completed:', summary);
            } else {
                // Multiple chunks - process each chunk
                const chunkSummaries: any[] = [];

                for (let i = 0; i < textChunks.length; i++) {
                    const chunk = textChunks[i];
                    console.log(`Processing chunk ${i + 1}/${textChunks.length}`);

                    setSummaryProgress(Math.round((i / textChunks.length) * 80)); // Reserve 20% for final summary

                    const chunkSummary = await smr.summarizeSync({
                        text: chunk,
                        sessionId: session?.id,
                        language: language.split('-')[0]
                    });

                    chunkSummaries.push({
                        chunkIndex: i,
                        chunkText: chunk.substring(0, 200) + '...',
                        summary: chunkSummary
                    });

                    console.log(`Chunk ${i + 1} summarized:`, chunkSummary);
                }

                setSummaries(chunkSummaries);

                // Combine chunk summaries for final summary
                setSummaryProgress(90);
                const combinedSummaryText = chunkSummaries
                    .map((cs, i) => {
                        const extracted = extractSummaryData(cs.summary);
                        return `Chunk ${i + 1}: ${extracted?.summary || 'No summary available'}`;
                    })
                    .join('\n\n');

                if (combinedSummaryText.length > MAX_CHUNK_LENGTH) {
                    // If combined summaries are still too long, take a subset
                    const finalText = combinedSummaryText.substring(0, MAX_CHUNK_LENGTH);
                    const finalSummary = await smr.summarizeSync({
                        text: finalText,
                        sessionId: session?.id,
                        language: language.split('-')[0]
                    });
                    setCurrentSummary(finalSummary);
                } else {
                    // Create combined summary
                    const finalSummary = await smr.summarizeSync({
                        text: combinedSummaryText,
                        sessionId: session?.id,
                        language: language.split('-')[0]
                    });
                    setCurrentSummary(finalSummary);
                }

                console.log('Multi-chunk summarization completed');
            }

            setSummaryProgress(100);
        } catch (error) {
            console.error('Summarization error:', error);
            alert('Failed to summarize text. Check console for details.');
        } finally {
            setIsProcessing(false);
        }
    };

    // Handle clear
    const handleClear = () => {
        setInputText('');
        setChunks([]);
        setSummaries([]);
        setCurrentSummary(null);
        setSummaryProgress(0);
    };

    // Statistics calculation
    const stats = {
        characters: inputText.length,
        words: inputText.split(/\s+/).filter(word => word.length > 0).length,
        estimatedTokens: Math.ceil(inputText.length / 4), // Rough estimation
        chunks: chunks.length
    };

    if (sessionLoading) {
        return (
            <div
                style={{
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    padding: '20px',
                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                    maxWidth: '800px',
                    margin: '0 auto',
                    textAlign: 'center',
                }}
            >
                <h2>Initializing Session Manager...</h2>
                <p>Please wait while we set up the session manager.</p>
                <div
                    style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        border: '4px solid #f3f3f3',
                        borderTop: '4px solid #007bff',
                        animation: 'spin 1s linear infinite',
                        margin: '20px auto',
                    }}
                />
            </div>
        );
    }

    return (
        <div
            style={{
                backgroundColor: 'white',
                borderRadius: '8px',
                padding: '20px',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                maxWidth: '900px',
                margin: '0 auto',
            }}
        >
            <h2 style={{ marginTop: 0, color: '#333' }}>Text Summarization</h2>
            <p style={{ color: '#666', marginBottom: '20px' }}>
                Enter any text for summarization. The system automatically handles long conversations by splitting them into chunks.
            </p>

            {/* Session Management Section */}
            <div
                style={{
                    backgroundColor: '#f8f9fa',
                    border: '1px solid #dee2e6',
                    borderRadius: '6px',
                    padding: '15px',
                    marginBottom: '20px',
                }}
            >
                <h3 style={{ marginTop: 0, color: '#495057' }}>Session Management</h3>

                {/* Session Status */}
                <div style={{ marginBottom: '15px' }}>
                    <span style={{ fontWeight: 'bold' }}>Session Status: </span>
                    <span
                        style={{
                            color:
                                session?.status === 'ACTIVE'
                                    ? '#28a745'
                                    : session?.status === 'PAUSED'
                                      ? '#ffc107'
                                      : session?.status === 'IDLE'
                                        ? '#17a2b8'
                                        : '#6c757d',
                            fontWeight: 'bold',
                        }}
                    >
                        {session?.status || 'No Session'}
                    </span>
                    {session && <span style={{ marginLeft: '10px', fontSize: '0.9em', color: '#6c757d' }}>ID: {session.id.substring(0, 8)}...</span>}
                </div>

                {/* Doctor and Patient Info */}
                <div
                    style={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        gap: '10px',
                        marginBottom: '15px',
                    }}
                >
                    <div>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Doctor Name:</label>
                        <input
                            type="text"
                            value={doctorName}
                            onChange={(e) => setDoctorName(e.target.value)}
                            disabled={!!session}
                            style={{
                                width: '100%',
                                padding: '8px',
                                borderRadius: '4px',
                                border: '1px solid #ced4da',
                                backgroundColor: session ? '#e9ecef' : 'white',
                            }}
                        />
                    </div>
                    <div>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Patient Name:</label>
                        <input
                            type="text"
                            value={patientName}
                            onChange={(e) => setPatientName(e.target.value)}
                            disabled={!!session}
                            style={{
                                width: '100%',
                                padding: '8px',
                                borderRadius: '4px',
                                border: '1px solid #ced4da',
                                backgroundColor: session ? '#e9ecef' : 'white',
                            }}
                        />
                    </div>
                </div>

                {/* Language Selection */}
                <div style={{ marginBottom: '15px' }}>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Language:</label>
                    <select
                        value={language}
                        onChange={(e) => setLanguage(e.target.value)}
                        disabled={!!session}
                        style={{
                            width: '100%',
                            padding: '8px',
                            borderRadius: '4px',
                            border: '1px solid #ced4da',
                            backgroundColor: session ? '#e9ecef' : 'white',
                        }}
                    >
                        <option value="en-US">English (US)</option>
                        <option value="ml-IN">Malayalam (India)</option>
                        <option value="hi-IN">Hindi (India)</option>
                        <option value="ta-IN">Tamil (India)</option>
                    </select>
                </div>

                {/* Session Actions */}
                <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                    {!session && (
                        <button
                            onClick={handleCreateSession}
                            disabled={sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#007bff',
                                color: 'white',
                                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            Create Session
                        </button>
                    )}

                    {session && session.status === 'IDLE' && (
                        <button
                            onClick={handleStartSession}
                            disabled={sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#28a745',
                                color: 'white',
                                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            Start Session
                        </button>
                    )}

                    {session && session.status === 'ACTIVE' && (
                        <button
                            onClick={() => pauseSession('User requested pause')}
                            disabled={sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#ffc107',
                                color: 'black',
                                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            Pause Session
                        </button>
                    )}

                    {session && session.status === 'PAUSED' && (
                        <button
                            onClick={resumeSession}
                            disabled={sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#17a2b8',
                                color: 'white',
                                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            Resume Session
                        </button>
                    )}

                    {session && (
                        <button
                            onClick={endSession}
                            disabled={sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#dc3545',
                                color: 'white',
                                border: 'none',
                                cursor: sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            End Session
                        </button>
                    )}
                </div>

                {/* Load Existing Session */}
                <div style={{ marginTop: '15px', paddingTop: '15px', borderTop: '1px solid #dee2e6' }}>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Load Existing Session:</label>
                    <div style={{ display: 'flex', gap: '10px' }}>
                        <input
                            type="text"
                            value={sessionIdInput}
                            onChange={(e) => setSessionIdInput(e.target.value)}
                            placeholder="Enter session ID..."
                            disabled={!!session}
                            style={{
                                flex: 1,
                                padding: '8px',
                                borderRadius: '4px',
                                border: '1px solid #ced4da',
                                backgroundColor: session ? '#e9ecef' : 'white',
                            }}
                        />
                        <button
                            onClick={handleLoadSession}
                            disabled={!!session || !sessionIdInput.trim() || sessionLoading}
                            style={{
                                padding: '8px 15px',
                                borderRadius: '4px',
                                backgroundColor: '#6f42c1',
                                color: 'white',
                                border: 'none',
                                cursor: !!session || !sessionIdInput.trim() || sessionLoading ? 'not-allowed' : 'pointer',
                            }}
                        >
                            Load
                        </button>
                    </div>
                </div>

                {/* Session Error */}
                {sessionError && (
                    <div
                        style={{
                            marginTop: '10px',
                            padding: '10px',
                            backgroundColor: '#f8d7da',
                            color: '#721c24',
                            borderRadius: '4px',
                            border: '1px solid #f5c6cb',
                        }}
                    >
                        <strong>Session Error:</strong> {sessionError.message}
                        <button
                            onClick={clearError}
                            style={{
                                marginLeft: '10px',
                                padding: '2px 8px',
                                fontSize: '0.8em',
                                backgroundColor: 'transparent',
                                border: '1px solid #721c24',
                                borderRadius: '3px',
                                color: '#721c24',
                                cursor: 'pointer',
                            }}
                        >
                            Clear
                        </button>
                    </div>
                )}
            </div>

            {/* Text Input Section */}
            <div
                style={{
                    backgroundColor: '#f8f9fa',
                    border: '1px solid #dee2e6',
                    borderRadius: '6px',
                    padding: '15px',
                    marginBottom: '20px',
                    opacity: canUseSummarization ? 1 : 0.5,
                    pointerEvents: canUseSummarization ? 'auto' : 'none'
                }}
            >
                <h3 style={{ marginTop: 0, color: '#495057' }}>📝 Text Input</h3>

                {!canUseSummarization && (
                    <div
                        style={{
                            marginBottom: '15px',
                            padding: '10px',
                            backgroundColor: '#fff3cd',
                            color: '#856404',
                            borderRadius: '4px',
                            border: '1px solid #ffeaa7',
                        }}
                    >
                        ⚠️ Summarization requires an active session and connection to the SMR service.
                    </div>
                )}

                <textarea
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    placeholder="Enter your text here for summarization. You can paste long conversations, documents, or any text content. The system will automatically handle chunking for texts longer than 8192 tokens..."
                    style={{
                        width: '100%',
                        minHeight: '200px',
                        padding: '15px',
                        borderRadius: '4px',
                        border: '1px solid #ced4da',
                        fontSize: '14px',
                        lineHeight: '1.5',
                        fontFamily: 'inherit',
                        resize: 'vertical'
                    }}
                />

                {/* Text Statistics */}
                <div
                    style={{
                        marginTop: '10px',
                        padding: '10px',
                        backgroundColor: '#e9ecef',
                        borderRadius: '4px',
                        fontSize: '14px',
                        color: '#495057'
                    }}
                >
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: '10px' }}>
                        <div><strong>Characters:</strong> {stats.characters.toLocaleString()}</div>
                        <div><strong>Words:</strong> {stats.words.toLocaleString()}</div>
                        <div><strong>Est. Tokens:</strong> {stats.estimatedTokens.toLocaleString()}</div>
                        <div><strong>Chunks:</strong> {stats.chunks}</div>
                    </div>
                    {stats.estimatedTokens > 8000 && (
                        <div style={{ marginTop: '5px', color: '#856404' }}>
                            ⚠️ Large text detected. Will be processed in chunks to handle token limits.
                        </div>
                    )}
                </div>

                {/* Action Buttons */}
                <div style={{ marginTop: '15px', display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                    <button
                        onClick={handleChunkText}
                        disabled={!inputText.trim() || isProcessing}
                        style={{
                            padding: '10px 20px',
                            borderRadius: '4px',
                            backgroundColor: !inputText.trim() || isProcessing ? '#6c757d' : '#17a2b8',
                            color: 'white',
                            border: 'none',
                            cursor: !inputText.trim() || isProcessing ? 'not-allowed' : 'pointer',
                        }}
                    >
                        📋 Analyze Text Structure
                    </button>

                    <button
                        onClick={handleSummarize}
                        disabled={!canUseSummarization || !inputText.trim() || isProcessing}
                        style={{
                            padding: '10px 20px',
                            borderRadius: '4px',
                            backgroundColor: !canUseSummarization || !inputText.trim() || isProcessing ? '#6c757d' : '#007bff',
                            color: 'white',
                            border: 'none',
                            cursor: !canUseSummarization || !inputText.trim() || isProcessing ? 'not-allowed' : 'pointer',
                            fontWeight: 'bold'
                        }}
                    >
                        {isProcessing ? '🔄 Summarizing...' : '📋 Generate Summary'}
                    </button>

                    <button
                        onClick={handleClear}
                        disabled={isProcessing}
                        style={{
                            padding: '10px 20px',
                            borderRadius: '4px',
                            backgroundColor: isProcessing ? '#6c757d' : '#dc3545',
                            color: 'white',
                            border: 'none',
                            cursor: isProcessing ? 'not-allowed' : 'pointer',
                        }}
                    >
                        🗑️ Clear All
                    </button>
                </div>
            </div>

            {/* Progress Section */}
            {isProcessing && (
                <div style={{ marginBottom: '20px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                        <span style={{ fontWeight: 'bold', color: '#007bff' }}>Processing...</span>
                        <span style={{ color: '#007bff' }}>{summaryProgress}%</span>
                    </div>
                    <div
                        style={{
                            width: '100%',
                            height: '8px',
                            backgroundColor: '#e9ecef',
                            borderRadius: '4px',
                            overflow: 'hidden',
                        }}
                    >
                        <div
                            style={{
                                width: `${summaryProgress}%`,
                                height: '100%',
                                backgroundColor: '#007bff',
                                transition: 'width 0.3s ease',
                            }}
                        />
                    </div>
                </div>
            )}

            {/* Chunks Display */}
            {chunks.length > 0 && (
                <div
                    style={{
                        backgroundColor: '#e3f2fd',
                        border: '1px solid #2196F3',
                        borderRadius: '6px',
                        padding: '15px',
                        marginBottom: '20px',
                    }}
                >
                    <h3 style={{ marginTop: 0, color: '#1976d2' }}>📄 Text Chunks ({chunks.length})</h3>
                    <p style={{ color: '#1976d2', marginBottom: '15px' }}>
                        Text has been split into {chunks.length} chunk{chunks.length !== 1 ? 's' : ''} for processing:
                    </p>

                    {chunks.map((chunk, index) => (
                        <div
                            key={index}
                            style={{
                                padding: '10px',
                                backgroundColor: 'white',
                                border: '1px solid #e3f2fd',
                                borderRadius: '4px',
                                marginBottom: '10px',
                            }}
                        >
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                                <strong>Chunk {index + 1}</strong>
                                <span style={{ fontSize: '0.9em', color: '#666' }}>
                                    {chunk.length} characters • {chunk.split(/\s+/).filter(w => w.length > 0).length} words
                                </span>
                            </div>
                            <div style={{
                                maxHeight: '100px',
                                overflowY: 'auto',
                                fontSize: '14px',
                                lineHeight: '1.4',
                                color: '#333'
                            }}>
                                {chunk.substring(0, 300)}{chunk.length > 300 ? '...' : ''}
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* Individual Chunk Summaries */}
            {summaries.length > 0 && (
                <div
                    style={{
                        backgroundColor: '#fff3e0',
                        border: '1px solid #ff9800',
                        borderRadius: '6px',
                        padding: '15px',
                        marginBottom: '20px',
                    }}
                >
                    <h3 style={{ marginTop: 0, color: '#e65100' }}>📋 Individual Chunk Summaries</h3>

                    {summaries.map((chunkSummary, index) => {
                        const displaySummary = extractSummaryData(chunkSummary.summary);
                        return (
                            <div
                                key={index}
                                style={{
                                    padding: '15px',
                                    backgroundColor: 'white',
                                    border: '1px solid #ffcc02',
                                    borderRadius: '4px',
                                    marginBottom: '15px',
                                }}
                            >
                                <h4 style={{ marginTop: 0, color: '#e65100' }}>Chunk {index + 1} Summary</h4>

                                {displaySummary && (
                                    <div>
                                        <div style={{ marginBottom: '10px' }}>
                                            <strong>Summary:</strong>
                                            <div style={{ marginTop: '5px', padding: '10px', backgroundColor: '#f9f9f9', borderRadius: '3px' }}>
                                                {displaySummary.summary}
                                            </div>
                                        </div>

                                        {displaySummary.keyPoints && displaySummary.keyPoints.length > 0 && (
                                            <div style={{ marginBottom: '10px' }}>
                                                <strong>Key Points:</strong>
                                                <ul style={{ marginTop: '5px' }}>
                                                    {displaySummary.keyPoints.map((point: string, pointIndex: number) => (
                                                        <li key={pointIndex}>{point}</li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
            )}

            {/* Final Summary Display */}
            {(() => {
                const displaySummary = extractSummaryData(currentSummary);
                return displaySummary && (
                    <div
                        style={{
                            padding: '20px',
                            border: '2px solid #28a745',
                            borderRadius: '6px',
                            backgroundColor: '#f8fff9',
                        }}
                    >
                        <h3 style={{ marginTop: 0, color: '#155724' }}>
                            🎯 Final Summary
                            {chunks.length > 1 && <span style={{ fontSize: '0.8em', fontWeight: 'normal' }}> (Combined from {chunks.length} chunks)</span>}
                        </h3>

                        <div style={{ marginBottom: '15px' }}>
                            <h4>Summary:</h4>
                            <div style={{ padding: '15px', backgroundColor: '#f0f8ff', borderRadius: '4px', lineHeight: '1.6' }}>
                                {displaySummary.summary}
                            </div>
                        </div>

                        {displaySummary.keyPoints && displaySummary.keyPoints.length > 0 && (
                            <div style={{ marginBottom: '15px' }}>
                                <h4>Key Points:</h4>
                                <ul style={{ paddingLeft: '20px' }}>
                                    {displaySummary.keyPoints.map((point: string, index: number) => (
                                        <li key={index} style={{ marginBottom: '5px' }}>{point}</li>
                                    ))}
                                </ul>
                            </div>
                        )}

                        {displaySummary.diagnosis && (
                            <div style={{ marginBottom: '15px' }}>
                                <h4>Diagnosis:</h4>
                                <div style={{ padding: '10px', backgroundColor: '#fff0f0', borderRadius: '4px' }}>
                                    {displaySummary.diagnosis}
                                </div>
                            </div>
                        )}

                        {displaySummary.recommendations && displaySummary.recommendations.length > 0 && (
                            <div style={{ marginBottom: '15px' }}>
                                <h4>Recommendations:</h4>
                                <ul style={{ paddingLeft: '20px' }}>
                                    {displaySummary.recommendations.map((rec: string, index: number) => (
                                        <li key={index} style={{ marginBottom: '5px' }}>{rec}</li>
                                    ))}
                                </ul>
                            </div>
                        )}

                        {displaySummary.medications && displaySummary.medications.length > 0 && (
                            <div style={{ marginBottom: '15px' }}>
                                <h4>Medications:</h4>
                                <ul style={{ paddingLeft: '20px' }}>
                                    {displaySummary.medications.map((med: string, index: number) => (
                                        <li key={index} style={{ marginBottom: '5px' }}>{med}</li>
                                    ))}
                                </ul>
                            </div>
                        )}

                        {displaySummary.metadata && (
                            <div style={{ fontSize: '12px', color: '#666', marginTop: '15px', paddingTop: '15px', borderTop: '1px solid #dee2e6' }}>
                                <strong>Processing Details:</strong><br/>
                                Processing time: {displaySummary.metadata.processingTime}ms
                                {displaySummary.metadata.confidence && (
                                    <><br/>Confidence: {(displaySummary.metadata.confidence * 100).toFixed(1)}%</>
                                )}
                                {displaySummary.metadata.provider && (
                                    <><br/>Provider: {displaySummary.metadata.provider}</>
                                )}
                                {displaySummary.metadata.model && (
                                    <><br/>Model: {displaySummary.metadata.model}</>
                                )}
                            </div>
                        )}
                    </div>
                );
            })()}

            {/* SMR Status */}
            <div style={{
                marginTop: '20px',
                fontSize: '14px',
                color: '#6c757d',
                padding: '10px',
                backgroundColor: '#f8f9fa',
                borderRadius: '4px',
                border: '1px solid #dee2e6'
            }}>
                <strong>SMR Status:</strong> {smr.isConnected ? '✅ Connected' : '❌ Disconnected'}<br/>
                <strong>Current Job:</strong> {smr.currentJob ? `${smr.currentJob.status} (${(smr.currentJob as any).job_id || (smr.currentJob as any).id || 'unknown'})` : 'None'}<br/>
                <strong>Processing:</strong> {isProcessing ? 'In Progress' : 'Idle'}
            </div>

            {/* SMR Error Display */}
            {smr.error && (
                <div style={{
                    color: 'red',
                    padding: '10px',
                    backgroundColor: '#ffebee',
                    border: '1px solid #f44336',
                    borderRadius: '4px',
                    marginTop: '10px'
                }}>
                    SMR Error: {smr.error}
                </div>
            )}
        </div>
    );
}