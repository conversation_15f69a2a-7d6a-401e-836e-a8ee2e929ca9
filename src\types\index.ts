export * from './session';

export * from './api';

export interface SDKConfig {
    /** API endpoint for cloud services */
    apiEndpoint: string;
    /** WebSocket URL for real-time communication */
    websocketUrl: string;
    /** Speech-to-text provider configuration */
    sttProvider: 'azure' | 'google' | 'aws' | 'custom';
    /** Audio processing settings */
    audioSettings: AudioConfig;
    /** Error reporting configuration */
    errorReporting: ErrorConfig;
    /** Logging configuration */
    logging: LogConfig;
    /** Authentication credentials */
    credentials?: CredentialsConfig;
    /** Environment-specific settings */
    environment?: 'development' | 'staging' | 'production';
}

export interface AudioConfig {
    /** Sample rate in Hz (16000, 44100, 48000) */
    sampleRate: number;
    /** Audio format (PCM, WAV, etc.) */
    format: 'pcm' | 'wav' | 'mp3';
    /** Number of audio channels (1 for mono, 2 for stereo) */
    channels: 1 | 2;
    /** Bit depth (8, 16, 24, 32) */
    bitDepth: 8 | 16 | 24 | 32;
    /** Chunk size for real-time processing */
    chunkSize: number;
    /** Enable noise cancellation */
    noiseCancellation: boolean;
    /** Enable echo cancellation */
    echoCancellation: boolean;
    /** Enable automatic gain control */
    autoGainControl: boolean;
}

export interface ErrorConfig {
    /** Enable error reporting */
    enabled: boolean;
    /** Error reporting endpoint */
    endpoint?: string;
    /** Include stack traces in reports */
    includeStackTrace: boolean;
    /** Maximum number of errors to report per session */
    maxErrorsPerSession: number;
}

export interface LogConfig {
    /** Logging level */
    level: 'debug' | 'info' | 'warn' | 'error';
    /** Enable console logging */
    console: boolean;
    /** Enable remote logging */
    remote: boolean;
    /** Remote logging endpoint */
    endpoint?: string;
}

export interface CredentialsConfig {
    /** API key for authentication */
    apiKey: string;
    /** Client ID for OAuth */
    clientId?: string;
    /** Client secret for OAuth */
    clientSecret?: string;
    /** Access token */
    accessToken?: string;
    /** Refresh token */
    refreshToken?: string;
}

export interface WebSocketMessage {
    /** Message type */
    type: 'audio_chunk' | 'transcription' | 'error' | 'status' | 'heartbeat';
    /** Unique session identifier */
    sessionId: string;
    /** Message timestamp */
    timestamp: number;
    /** Message payload */
    data: AudioChunk | TranscriptionResult | ErrorInfo | StatusUpdate | HeartbeatData;
}

export interface AudioChunk {
    /** Audio data as ArrayBuffer */
    data: ArrayBuffer;
    /** Chunk sequence number */
    sequence: number;
    /** Audio format metadata */
    format: AudioConfig;
    /** Chunk timestamp */
    timestamp: number;
}

export interface TranscriptionResult {
    /** Transcribed text */
    text: string;
    /** Confidence score (0-1) */
    confidence: number;
    /** Whether this is a final result */
    isFinal: boolean;
    /** Start time in milliseconds */
    startTime: number;
    /** End time in milliseconds */
    endTime: number;
    /** Language detected */
    language?: string;
    /** Alternative transcriptions */
    alternatives?: Array<{
        text: string;
        confidence: number;
    }>;
}

export interface ErrorInfo {
    /** Error code */
    code: string;
    /** Error message */
    message: string;
    /** Error severity */
    severity: 'low' | 'medium' | 'high' | 'critical';
    /** Error category */
    category: 'network' | 'audio' | 'processing' | 'authentication' | 'configuration';
    /** Additional error details */
    details?: Record<string, unknown>;
    /** Error stack trace */
    stack?: string;
}

export interface StatusUpdate {
    /** Current SDK state */
    state: SDKState;
    /** Connection status */
    connectionStatus: ConnectionStatus;
    /** Audio device status */
    audioStatus: AudioDeviceStatus;
    /** Processing status */
    processingStatus: ProcessingStatus;
}

export interface HeartbeatData {
    /** Heartbeat timestamp */
    timestamp: number;
    /** Connection quality metrics */
    metrics?: ConnectionMetrics;
}

export type SDKState = 'idle' | 'initializing' | 'connecting' | 'connected' | 'processing' | 'error' | 'disconnected';

export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'failed';

export interface AudioDeviceStatus {
    /** Available audio input devices */
    inputDevices: MediaDeviceInfo[];
    /** Currently selected input device */
    selectedDevice?: MediaDeviceInfo;
    /** Device access permission status */
    permissionStatus: 'granted' | 'denied' | 'prompt';
    /** Audio level (0-100) */
    audioLevel: number;
}

export interface ProcessingStatus {
    /** Current processing stage */
    stage: 'idle' | 'capturing' | 'streaming' | 'transcribing' | 'complete';
    /** Processing latency in milliseconds */
    latency: number;
    /** Queue size for pending operations */
    queueSize: number;
}

export interface ConnectionMetrics {
    /** Round-trip time in milliseconds */
    rtt: number;
    /** Connection quality score (0-100) */
    quality: number;
    /** Packet loss percentage */
    packetLoss: number;
    /** Bandwidth utilization */
    bandwidth: number;
}

export interface SDKEvents {
    /** SDK state changed */
    'state-changed': (state: SDKState) => void;
    /** Connection status changed */
    'connection-changed': (status: ConnectionStatus) => void;
    /** Audio device changed */
    'device-changed': (device: MediaDeviceInfo) => void;
    /** Transcription received */
    transcription: (result: TranscriptionResult) => void;
    /** Error occurred */
    error: (error: ErrorInfo) => void;
    /** Audio level updated */
    'audio-level': (level: number) => void;
    /** Processing status changed */
    'processing-status': (status: ProcessingStatus) => void;
}

export interface MedicalEntity {
    /** Entity text */
    text: string;
    /** Entity type (symptom, medication, procedure, etc.) */
    type: string;
    /** Confidence score */
    confidence: number;
    /** Start position in text */
    start: number;
    /** End position in text */
    end: number;
    /** Medical codes (ICD-10, SNOMED, etc.) */
    codes?: MedicalCode[];
}

export interface MedicalCode {
    /** Code system (ICD-10, SNOMED-CT, etc.) */
    system: string;
    /** Code value */
    code: string;
    /** Code description */
    description: string;
}

export interface PerformanceMetrics {
    /** SDK initialization time */
    initializationTime: number;
    /** Audio capture start time */
    audioCaptureStartTime: number;
    /** WebSocket connection time */
    connectionTime: number;
    /** STT processing latency */
    sttLatency: number;
    /** Memory usage in MB */
    memoryUsage: number;
    /** CPU usage percentage */
    cpuUsage: number;
}
