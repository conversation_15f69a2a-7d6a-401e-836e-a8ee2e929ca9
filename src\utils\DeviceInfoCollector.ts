import type { DeviceInfo } from '../types/index';

export class DeviceInfoCollector {
    private cachedDeviceInfo: DeviceInfo | null = null;
    private readonly deviceId: string;

    constructor() {
        this.deviceId = this.generateDeviceId();
    }

    public async collect(): Promise<DeviceInfo> {
        if (this.cachedDeviceInfo) {
            return {
                ...this.cachedDeviceInfo,
                lastSeen: new Date(),
            };
        }

        try {
            const userAgent = navigator.userAgent;
            const platform = this.detectPlatform();
            const browser = this.detectBrowser(userAgent);
            const browserVersion = this.detectBrowserVersion(userAgent);
            const deviceType = this.detectDeviceType(userAgent);
            const screenResolution = this.getScreenResolution();
            const timezone = this.getTimezone();

            this.cachedDeviceInfo = {
                deviceId: this.deviceId,
                deviceType,
                browser,
                browserVersion,
                platform,
                userAgent,
                screenResolution,
                timezone,
                lastSeen: new Date(),
            };

            return this.cachedDeviceInfo;
        } catch (error) {
            // Fallback device info with explicit type casting for userAgent
            return {
                deviceId: this.deviceId,
                deviceType: 'desktop',
                browser: 'unknown',
                browserVersion: 'unknown',
                platform: 'unknown',
                userAgent: (navigator?.userAgent || 'unknown') as string,
                screenResolution: '1920x1080',
                timezone: 'UTC',
                lastSeen: new Date(),
            };
        }
    }

    public getDeviceId(): string {
        return this.deviceId;
    }

    public async refresh(): Promise<DeviceInfo> {
        this.cachedDeviceInfo = null;
        return await this.collect();
    }

    private generateDeviceId(): string {
        const storageKey = 'agenticsdk_device_id';

        try {
            // Try to get existing device ID from localStorage
            const existingId = localStorage.getItem(storageKey);
            if (existingId) {
                return existingId;
            }
        } catch (error) {
            // localStorage might not be available
        }

        const deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

        try {
            // Store device ID for future use
            localStorage.setItem(storageKey, deviceId);
        } catch (error) {
            // localStorage might not be available, continue with generated ID
        }

        return deviceId;
    }

    private detectPlatform(): string {
        const userAgent = navigator.userAgent.toLowerCase();

        if (userAgent.includes('win')) return 'Windows';
        if (userAgent.includes('mac')) return 'macOS';
        if (userAgent.includes('linux')) return 'Linux';
        if (userAgent.includes('android')) return 'Android';
        if (userAgent.includes('ios') || userAgent.includes('iphone') || userAgent.includes('ipad')) return 'iOS';

        return 'Unknown';
    }

    private detectBrowser(userAgent: string): string {
        const ua = userAgent.toLowerCase();

        if (ua.includes('edg/')) return 'Edge';
        if (ua.includes('chrome/') && !ua.includes('edg/')) return 'Chrome';
        if (ua.includes('firefox/')) return 'Firefox';
        if (ua.includes('safari/') && !ua.includes('chrome/')) return 'Safari';
        if (ua.includes('opera/') || ua.includes('opr/')) return 'Opera';

        return 'Unknown';
    }

    private detectBrowserVersion(userAgent: string): string {
        const ua = userAgent.toLowerCase();

        try {
            let match: RegExpMatchArray | null = null;

            if (ua.includes('edg/')) {
                match = ua.match(/edg\/(\d+\.\d+)/);
            } else if (ua.includes('chrome/') && !ua.includes('edg/')) {
                match = ua.match(/chrome\/(\d+\.\d+)/);
            } else if (ua.includes('firefox/')) {
                match = ua.match(/firefox\/(\d+\.\d+)/);
            } else if (ua.includes('safari/') && !ua.includes('chrome/')) {
                match = ua.match(/version\/(\d+\.\d+)/);
            } else if (ua.includes('opera/') || ua.includes('opr/')) {
                match = ua.match(/(?:opera|opr)\/(\d+\.\d+)/);
            }

            return match ? (match[1] || 'unknown') : 'unknown';
        } catch (error) {
            return 'unknown';
        }
    }

    private detectDeviceType(userAgent: string): 'desktop' | 'tablet' | 'mobile' {
        const ua = userAgent.toLowerCase();

        // Check for mobile indicators
        if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
            return 'mobile';
        }

        // Check for tablet indicators
        if (ua.includes('tablet') || ua.includes('ipad')) {
            return 'tablet';
        }

        // Check screen size if available
        try {
            const screenWidth = window.screen.width;
            if (screenWidth < 768) {
                return 'mobile';
            } else if (screenWidth < 1024) {
                return 'tablet';
            }
        } catch (error) {
            // Screen API not available
        }

        return 'desktop';
    }

    private getScreenResolution(): string {
        try {
            const width = window.screen.width;
            const height = window.screen.height;
            return `${width}x${height}`;
        } catch (error) {
            return 'unknown';
        }
    }

    private getTimezone(): string {
        try {
            return Intl.DateTimeFormat().resolvedOptions().timeZone;
        } catch (error) {
            return 'UTC';
        }
    }
}