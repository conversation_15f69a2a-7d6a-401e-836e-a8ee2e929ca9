import { describe, it, expect, beforeEach } from 'vitest';
import { AgenticSDK } from '../core/AgenticSDK';

describe('AgenticSDK', () => {
  let sdk: AgenticSDK;

  beforeEach(() => {
    sdk = AgenticSDK.getInstance();
  });

  it('should create a singleton instance', () => {
    const sdk1 = AgenticSDK.getInstance();
    const sdk2 = AgenticSDK.getInstance();
    expect(sdk1).toBe(sdk2);
  });

  it('should initialize with basic configuration', async () => {
    const config = {
      apiEndpoint: 'https://test-api.example.com',
      websocketUrl: 'wss://test-ws.example.com'
    };

    await expect(sdk.initialize(config)).resolves.not.toThrow();
    expect(sdk.getState()).toBe('idle');
  });

  it('should have initial state as idle', () => {
    expect(sdk.getState()).toBe('idle');
  });

  it('should generate session ID after initialization', async () => {
    const config = {
      apiEndpoint: 'https://test-api.example.com',
      websocketUrl: 'wss://test-ws.example.com'
    };

    await sdk.initialize(config);
    const sessionId = sdk.getSessionId();
    expect(sessionId).toBeTruthy();
    expect(typeof sessionId).toBe('string');
  });

  it('should provide performance metrics', async () => {
    const config = {
      apiEndpoint: 'https://test-api.example.com',
      websocketUrl: 'wss://test-ws.example.com'
    };

    await sdk.initialize(config);
    const metrics = sdk.getPerformanceMetrics();

    expect(metrics).toHaveProperty('initializationTime');
    expect(metrics).toHaveProperty('audioCaptureStartTime');
    expect(metrics).toHaveProperty('connectionTime');
    expect(metrics.initializationTime).toBeGreaterThan(0);
  });
});
