// Base API Types
export interface ApiError {
    code: string;
    message: string;
    details?: Record<string, unknown>;
}

export interface PaginatedQuery {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}

// Session API Types
export interface SessionData {
    id: string;
    sessionToken: string;
    refreshToken?: string;
    deviceFingerprint?: string;
    ipAddress?: string;
    userAgent?: string;
    expiresAt: string; // ISO string
    lastAccessedAt: string; // ISO string
    sessionStatus: 'IDLE' | 'ACTIVE' | 'PAUSED' | 'EXPIRED' | 'TERMINATED' | 'SUSPENDED';
    sessionType: 'WEB' | 'MOBILE' | 'API' | 'MEDICAL_DEVICE' | 'AGENTIC_SDK';
    jsonData?: Record<string, unknown>;
    
    // Medical session fields
    medicalSessionId?: string;
    patientId?: string;
    providerId?: string;
    providerName?: string;
    encryptedData?: string;
    dataChecksum?: string;
    
    // AgenticSDK specific fields
    audioSessionData?: Record<string, unknown>;
    transcriptData?: Record<string, unknown>;
    deviceCapabilities?: Record<string, unknown>;
    
    // Sync and recovery fields
    syncStatus?: string;
    lastSyncAt?: string; // ISO string
    recoveryData?: Record<string, unknown>;
    offlineActions?: Record<string, unknown>;
    
    // Audit fields
    createdAt: string; // ISO string
    updatedAt: string; // ISO string
    createdBy?: string;
    updatedBy?: string;
    tenantId?: string;
}

// Session API Request/Response Types
export interface GetSessionRequest {
    id: string;
}

export interface GetSessionResponse {
    success: boolean;
    data: SessionData;
    message?: string;
}

export interface GetSessionsRequest extends PaginatedQuery {
    patientId?: string;
    providerId?: string;
    sessionStatus?: SessionData['sessionStatus'];
    sessionType?: SessionData['sessionType'];
    dateFrom?: string; // ISO string
    dateTo?: string; // ISO string
}

export interface GetSessionsResponse {
    success: boolean;
    data: PaginatedResponse<SessionData>;
    message?: string;
}

export interface CreateSessionRequest {
    // Required fields as per API validation
    sessionToken: string; // Required by API validation
    expiresAt: string; // Required - ISO string, API expects Date instance
    
    // Optional session type - defaults to WEB on server if not provided  
    sessionType?: SessionData['sessionType'];
    
    // Optional session configuration
    refreshToken?: string;
    deviceFingerprint?: string;
    ipAddress?: string;
    userAgent?: string;
    lastAccessedAt?: string; // ISO string
    sessionStatus?: SessionData['sessionStatus']; // Defaults to ACTIVE
    jsonData?: Record<string, unknown>;
    
    // Medical session fields
    medicalSessionId?: string;
    patientId?: string;
    providerId?: string;
    providerName?: string;
    encryptedData?: string;
    dataChecksum?: string;
    
    // AgenticSDK specific fields
    audioSessionData?: Record<string, unknown>;
    transcriptData?: Record<string, unknown>;
    deviceCapabilities?: Record<string, unknown>;
    
    // Sync and recovery fields
    syncStatus?: string;
    lastSyncAt?: string; // ISO string
    recoveryData?: Record<string, unknown>;
    offlineActions?: Record<string, unknown>;
}

export interface CreateSessionResponse {
    success: boolean;
    data: SessionData;
    message?: string;
}

export interface UpdateSessionRequest {
    // Session configuration updates
    refreshToken?: string;
    deviceFingerprint?: string;
    ipAddress?: string;
    userAgent?: string;
    expiresAt?: string; // ISO string
    lastAccessedAt?: string; // ISO string
    sessionStatus?: SessionData['sessionStatus'];
    sessionType?: SessionData['sessionType'];
    jsonData?: Record<string, unknown>;
    
    // Medical session fields
    medicalSessionId?: string;
    patientId?: string;
    patientName?: string;
    providerId?: string;
    providerName?: string;
    encryptedData?: string;
    dataChecksum?: string;
    
    // AgenticSDK specific fields
    audioSessionData?: Record<string, unknown>;
    transcriptData?: Record<string, unknown>;
    deviceCapabilities?: Record<string, unknown>;
    
    // Sync and recovery fields
    syncStatus?: string;
    lastSyncAt?: string; // ISO string
    recoveryData?: Record<string, unknown>;
    offlineActions?: Record<string, unknown>;
}

export interface UpdateSessionResponse {
    success: boolean;
    data: SessionData;
    message?: string;
}

export interface DeleteSessionRequest {
    id: string;
    // Optional soft delete flag - if true, marks as deleted instead of hard delete
    softDelete?: boolean;
    // Optional reason for deletion for audit purposes
    deleteReason?: string;
}

export interface DeleteSessionResponse {
    success: boolean;
    data: SessionData;
    message?: string;
} 